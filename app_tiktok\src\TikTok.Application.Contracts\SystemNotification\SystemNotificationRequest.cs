using System;
using System.Collections.Generic;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Request object để gửi system notification
    /// Đ<PERSON><PERSON><PERSON> tạo bởi SystemTemplateRenderer và gửi qua SystemNotificationTriggerService
    /// </summary>
    public class SystemNotificationRequest
    {
        /// <summary>
        /// Tiêu đề thông báo
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Nội dung thông báo
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Danh sách User IDs sẽ nhận thông báo
        /// </summary>
        public List<string> Recipients { get; set; } = new();

        /// <summary>
        /// Loại entity liên quan: "FactGmvMaxCampaign", "FactGmvMaxProduct"
        /// </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// ID của entity liên quan
        /// </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// ID của rule đã trigger
        /// </summary>
        public Guid RuleId { get; set; }

        /// <summary>
        /// Tên rule đã trigger
        /// </summary>
        public string RuleName { get; set; } = string.Empty;

        /// <summary>
        /// Dữ liệu bổ sung cho notification
        /// Chứa thông tin về field, giá trị, matching entities, etc.
        /// </summary>
        public Dictionary<string, object> Data { get; set; } = new();

        /// <summary>
        /// Thời gian tạo request
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;


        /// <summary>
        /// Notification type để routing
        /// </summary>
        public string NotificationType { get; set; } = "SystemNotification";

        /// <summary>
        /// URL để redirect khi click vào notification
        /// </summary>
        public string? RedirectUrl { get; set; }

        /// <summary>
        /// Icon cho notification
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Color cho notification
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// Expiry time cho notification
        /// </summary>
        public DateTime? ExpiresAt { get; set; }
    }
}
