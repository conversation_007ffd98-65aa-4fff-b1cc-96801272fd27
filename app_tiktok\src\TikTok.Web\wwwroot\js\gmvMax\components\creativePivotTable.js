/**
 * ✅ COPY HOÀN TOÀN TiktokGmvMaxProductPivotTable từ productPivotTable.js
 * Điều chỉnh cho FactGmvMaxProductCreativeEntity với các fields:
 * - ItemId, Title, CreativeType, ShopContentType, CreativeDeliveryStatus
 * - ProductImpressions, ProductClicks, ProductClickRate
 * - AdClickRate, AdConversionRate, AdVideoViewRate*
 */
class TiktokGmvMaxCreativePivotTableForGmvMax {
    constructor(containerId) {
        this.containerId = containerId;
        this.pivotTableObj = null;
        this.currencyManager = new Currency('creative');
        this.currentCurrency = this.currencyManager.getCurrentCurrency();
        this.availableCurrencies = ['USD', 'VND'];

        this.pendingRefresh = false;
        this.refreshTimeout = null;

        this.cachedLookups = null;
        this.lastDataHash = null;

        this.performanceMetrics = {
            refreshCount: 0,
            dataProcessingTime: 0,
            lastRefreshTime: null,
            totalProcessedRecords: 0,
        };

        this.dataBoundTimeout = null;

        // Debounce timer for insights updates
        this._insightsTimer = null;
        this._insightsDebounceMs = 500;
        // Debounce timer for conditional formatting
        this._formattingTimer = null;
        this._formattingDebounceMs = 300;

        // Get alert thresholds
        this.alertThresholds = window.ProductAlertThresholds || {
            roasCritical: 1.5,
            roasLow: 2.0,
            roasGood: 3.0,
            tacosHigh: 30,
            tacosMedium: 20,
            lowSalesThreshold: 5,
            highCostPerOrderThreshold: 100,
        };

        // ✅ Heatmap thresholds for creative performance
        this.heatmapThresholds = {
            roasEmergency: 1.0,
            roasCritical: this.alertThresholds.roasCritical,
            roasLow: this.alertThresholds.roasLow,
            roasGood: this.alertThresholds.roasGood,
            roasExcellent: 5.0,
            colors: {
                emergency: { bg: '#d32f2f', color: '#ffffff' },
                critical: { bg: '#f44336', color: '#ffffff' },
                low: { bg: '#ff9800', color: '#ffffff' },
                warning: { bg: '#ffc107', color: '#212121' },
                good: { bg: '#4caf50', color: '#ffffff' },
                excellent: { bg: '#2e7d32', color: '#ffffff' },
                veryHigh: { bg: '#1b5e20', color: '#ffffff' },
            },
        };
    }

    // ✅ Initial method
    async initial() {
        const dataSource = await this.extractPivotDataOptimized(['creative']);

        this.fullDataSource = dataSource;

        // Get filtered values based on permissions
        const filteredValues = await this.getCurrencyValuesWithAggregations();

        this.pivotTableObj = new ej.pivotview.PivotView({
            dataSourceSettings: {
                dataSource: dataSource,
                allowLabelFilter: false,
                allowValueFilter: false,
                allowMemberFilter: false,
                enableSorting: true,
                allowCalculatedField: true,

                // ✅ Row hierarchy: BC => Shop => Campaign => TtAccountName (Tên kênh) => Product => Video
                rows: [
                    {
                        name: 'BusinessCenterName',
                        caption: 'Trung tâm kinh doanh',
                        showSubTotals: true,
                    },
                    {
                        name: 'StoreName',
                        caption: 'Tên Shop',
                        showSubTotals: true,
                    },
                    {
                        name: 'CampaignName',
                        caption: 'Campaign',
                        showSubTotals: true,
                    },
                    {
                        name: 'TtAccountName',
                        caption: 'Tên kênh',
                        showSubTotals: true,
                    },
                    {
                        name: 'ProductName',
                        caption: 'Sản phẩm',
                        showSubTotals: true,
                    },
                    {
                        name: 'Title',
                        caption: 'Video/Creative',
                        showSubTotals: false,
                    },
                ],

                columns: this.getSmartTimeColumns(),

                // ✅ Creative metrics with dynamic currency and per-column aggregation
                values: filteredValues,

                // ✅ Filters
                filters: [
                    { name: 'CreativeType', caption: 'Loại Creative' },
                    { name: 'ShopContentType', caption: 'Loại nội dung' },
                    { name: 'CreativeDeliveryStatus', caption: 'Trạng thái' },
                ],

                // ✅ Number formatting with currency support
                formatSettings: this.getCurrencyFormatSettings(),

                // ✅ Calculated fields for creative analysis
                calculatedFieldSettings: [
                    {
                        name: 'Revenue_Per_Order',
                        formula:
                            '"Orders" > 0 ? ("GrossRevenue" / "Orders") : 0',
                        caption: 'Doanh thu mỗi đơn',
                    },
                    {
                        name: 'Creative_Efficiency_Score',
                        formula:
                            '("ROAS" > 3 && "Orders" > 10) ? 100 : (("ROAS" > 2 && "Orders" > 5) ? 70 : (("ROAS" > 1.5) ? 40 : 20))',
                        caption: 'Điểm hiệu quả creative',
                    },
                ],

                expandAll: false,

                // ✅ Hide unwanted values by default
                excludeFields: [
                    'NetCost',
                    'NetCostVND',
                    'CostPerOrderVND',
                    'CPMVND',
                    'CPCVND',
                ],

                // ✅ Sort by performance metrics
                sortSettings: [
                    { name: 'ROAS', order: 'Descending' },
                    { name: 'Orders', order: 'Descending' },
                ],
            },

            locale: 'vi-VN',
            height: 1000,
            width: '100%',
            showGroupingBar: false,
            showFieldList: true,
            allowExcelExport: true,
            allowPdfExport: false,
            showToolbar: true,

            showValuesButton: false,
            showRowSubTotals: false,
            showColumnSubTotals: false,
            showGrandTotals: false,
            gridSettings: {
                layout: 'Tabular',
                columnWidth: 140,
                allowSelection: false,
                selectionSettings: { mode: 'Cell', type: 'Multiple' },
                enableVirtualization: true,
                allowPaging: true,
                pageSize: 100,
            },

            // ✅ Toolbar for creative analysis
            toolbar: [
                'SubTotal',
                'GrandTotal',
                'FieldList',
                'ConditionalFormatting',
                'NumberFormatting',
            ],

            // ✅ Creative performance heatmap
            conditionalFormatSettings: [],
            allowConditionalFormatting: true,

            // ✅ Event handlers
            cellClick: (args) => {
                this.handleCellClick(args);
            },

            dataBound: () => {
                if (this.dataBoundTimeout) {
                    clearTimeout(this.dataBoundTimeout);
                }
                this.dataBoundTimeout = setTimeout(() => {
                    this.updateCreativeInsights();
                }, 200);
            },

            // Chart settings for creative analysis
            chartSettings: {
                chartSeries: {
                    type: 'Column',
                    animation: { enable: true },
                },
                primaryYAxis: {
                    title: 'Doanh thu (USD)',
                    labelFormat: 'C0',
                },
            },
        });

        // Render to container
        this.pivotTableObj.appendTo(`#${this.containerId}`);

        await this.applyInitialValuesConfiguration();
    }

    // ✅ Apply initial values configuration
    async applyInitialValuesConfiguration() {
        let savedConfig = localStorage.getItem('creativeColumnAggregations');
        let desiredValues;

        if (savedConfig) {
            try {
                const config = JSON.parse(savedConfig);
                if (config.selectedFields && config.aggregations) {
                    desiredValues = config.selectedFields;
                } else {
                    desiredValues = [
                        'GrossRevenue',
                        'Cost',
                        'CostPerOrder',
                        'ROAS',
                        'Orders',
                        'ProductImpressions',
                        'ProductClicks',
                    ];
                }
            } catch (e) {
                console.warn(
                    '⚠️ Error parsing saved configuration, using defaults:',
                    e
                );
                desiredValues = [
                    'GrossRevenue',
                    'Cost',
                    'CostPerOrder',
                    'ROAS',
                    'Orders',
                    'ProductImpressions',
                    'ProductClicks',
                ];
            }
        } else {
            desiredValues = [
                'GrossRevenue',
                'Cost',
                'CostPerOrder',
                'ROAS',
                'Orders',
                'ProductImpressions',
                'ProductClicks',
            ];
        }

        if (desiredValues && desiredValues.length > 0) {
            const currentAggregations = this.getStoredColumnAggregations();
            const currencyValues = await this.getCurrencyValues(
                currentAggregations
            );
            const newValues = currencyValues.filter((value) =>
                desiredValues.includes(value.name)
            );

            this.pivotTableObj.dataSourceSettings.values = newValues;
        } else {
            console.warn('⚠️ No valid values to apply, keeping default values');
        }
    }

    // ✅ Extract pivot data optimized
    async extractPivotDataOptimized(includes = ['creative'], dataToUse = null) {
        let sourceData = dataToUse;

        if (!sourceData) {
            // ✅ Use data from global scope
            sourceData = window.initialGmvMaxCreativeDataForGmvMax;
        }

        // ✅ DEBUG: Log source data structure
        console.log('🔍 Creative Pivot - Source Data:', sourceData);
        console.log(
            '🔍 Creative Pivot - Has factGmvMaxProductCreatives?',
            !!sourceData?.factGmvMaxProductCreatives
        );
        console.log(
            '🔍 Creative Pivot - Facts count:',
            sourceData?.factGmvMaxProductCreatives?.length
        );

        if (!sourceData?.factGmvMaxProductCreatives) {
            console.warn('❌ No creative data found, using empty dataset');
            return [];
        }

        const facts = sourceData.factGmvMaxProductCreatives;
        if (!facts || facts.length === 0) {
            console.warn('❌ FactGmvMaxProductCreatives array is empty');
            return [];
        }

        console.log(
            '✅ Creative Pivot - Processing',
            facts.length,
            'creative records'
        );
        console.log('🔍 Creative Pivot - Sample fact:', facts[0]);

        const transformedData = [];
        const BATCH_SIZE = 500;
        const lookups = this.createCachedLookups(sourceData);
        const processingStart = performance.now();

        for (let i = 0; i < facts.length; i += BATCH_SIZE) {
            const batch = facts.slice(i, i + BATCH_SIZE);

            // ✅ DEBUG: Log first batch
            if (i === 0) {
                console.log(
                    '🔍 Creative Pivot - First batch sample fact:',
                    batch[0]
                );
            }

            const batchResults = batch.map((fact) => {
                const dateInfo = lookups.date[fact.dimDateId] || {};
                const adAccountInfo =
                    lookups.adAccount[fact.dimAdAccountId] || {};
                const businessCenterInfo =
                    lookups.businessCenter[fact.dimBusinessCenterId] || {};
                const campaignInfo = lookups.campaign[fact.dimCampaignId] || {};
                const storeInfo = lookups.store[fact.dimStoreId] || {};
                const productInfo = fact.dimProductId
                    ? lookups.product[fact.dimProductId]
                    : null;
                const ttAccountInfo = fact.dimTTAccountId
                    ? lookups.ttAccount[fact.dimTTAccountId]
                    : null;

                const transformedRecord = {
                    // Core creative identification
                    ItemId: fact.itemId || '',
                    ItemGroupId: fact.itemGroupId || '',
                    Title: fact.title || 'Untitled Creative',
                    CreativeType: fact.creativeType || 'UNKNOWN',
                    ShopContentType: fact.shopContentType || 'UNKNOWN',
                    CreativeDeliveryStatus:
                        fact.creativeDeliveryStatus || 'UNKNOWN',

                    // Product info
                    ProductId: fact.productId || '',
                    ProductName: productInfo?.productName || 'Unknown Product',

                    // Business context
                    BusinessCenterName:
                        businessCenterInfo.bcName || 'Unknown BC',
                    BusinessCenterId: businessCenterInfo.bcId || '',
                    AdAccountName: adAccountInfo.advertiserName || '',
                    AdAccountId: adAccountInfo.advertiserId || '',
                    TtAccountName: ttAccountInfo?.ttAccountName || '',

                    // Campaign and Store info
                    CampaignName:
                        campaignInfo.campaignName || 'Unknown Campaign',
                    CampaignId: fact.campaignId,
                    StoreName: storeInfo.storeName || 'Unknown Store',
                    StoreId: fact.storeId,

                    // Date context
                    Date: dateInfo.fullDate || fact.date,
                    DateFormatted:
                        dateInfo.dateFormat_DDMMYYYY || formatDate(fact.date),
                    DateKey: fact.dimDateId,
                    Year: dateInfo.year || new Date(fact.date).getFullYear(),
                    Month: dateInfo.month || new Date(fact.date).getMonth() + 1,
                    MonthName: dateInfo.monthName || getMonthName(fact.date),
                    WeekDay: getVietnameseWeekday(new Date(fact.date)),
                    WeekOfYear: getWeekOfYear(fact.date),
                    WeekOfMonth: getWeekOfMonth(fact.date),
                    WeekStartDate: getWeekStartDate(fact.date),
                    WeekEndDate: getWeekEndDate(fact.date),
                    Quarter: getQuarter(fact.date),

                    // ✅ Formatted fields for smart grouping
                    WeekMonthYear: getWeekMonthYear(fact.date),
                    MonthYear: getMonthYear(fact.date),
                    YearFormatted: getYearFormatted(fact.date),

                    // Financial metrics with currency support
                    Cost: this.getCurrencyValue(fact, 'cost'),
                    GrossRevenue: this.getCurrencyValue(fact, 'grossRevenue'),

                    // Performance metrics
                    Orders: fact.orders || 0,
                    CostPerOrder: this.getCurrencyValue(fact, 'costPerOrder'),
                    ROAS: fact.roas || 0,
                    TACOS: fact.tacos || 0,

                    // ✅ Creative-specific metrics
                    ProductImpressions: fact.productImpressions || 0,
                    ProductClicks: fact.productClicks || 0,
                    ProductClickRate: fact.productClickRate || 0,
                    AdClickRate: fact.adClickRate || 0,
                    AdConversionRate: fact.adConversionRate || 0,
                    AdVideoViewRate2s: fact.adVideoViewRate2s || 0,
                    AdVideoViewRate6s: fact.adVideoViewRate6s || 0,
                    AdVideoViewRateP25: fact.adVideoViewRateP25 || 0,
                    AdVideoViewRateP50: fact.adVideoViewRateP50 || 0,
                    AdVideoViewRateP75: fact.adVideoViewRateP75 || 0,
                    AdVideoViewRateP100: fact.adVideoViewRateP100 || 0,

                    Currency: fact.currency || 'USD',
                };

                this.calculateCreativeMetricsInline(transformedRecord);
                this.addCreativeClassificationsInline(transformedRecord);

                return transformedRecord;
            });

            transformedData.push(...batchResults);

            if (i + BATCH_SIZE < facts.length) {
                await new Promise((resolve) => setTimeout(resolve, 0));
            }
        }

        // ✅ Store processing metrics
        const processingDuration = performance.now() - processingStart;
        this.performanceMetrics.dataProcessingTime = processingDuration;
        this.performanceMetrics.totalProcessedRecords = transformedData.length;

        console.log('✅ Creative Pivot - Transformation complete:', {
            inputRecords: facts.length,
            outputRecords: transformedData.length,
            processingTime: processingDuration.toFixed(2) + 'ms',
        });
        console.log(
            '🔍 Creative Pivot - Sample transformed record:',
            transformedData[0]
        );

        return transformedData;
    }

    // ✅ Calculate creative metrics inline
    calculateCreativeMetricsInline(record) {
        const productClicks = record.ProductClicks || 0;
        const productImpressions = record.ProductImpressions || 0;
        const grossRevenue = record.GrossRevenue || 0;
        const cost = record.Cost || 0;
        const orders = record.Orders || 0;

        // Conversion rate
        record.ConversionRate =
            productClicks > 0
                ? parseFloat(((orders / productClicks) * 100).toFixed(2))
                : 0;

        // Revenue per impression
        record.RevenuePerImpression =
            productImpressions > 0
                ? parseFloat((grossRevenue / productImpressions).toFixed(4))
                : 0;

        // Profit margin
        record.ProfitMargin =
            grossRevenue > 0
                ? parseFloat(
                      (((grossRevenue - cost) / grossRevenue) * 100).toFixed(2)
                  )
                : 0;
    }

    // ✅ Add creative classifications inline
    addCreativeClassificationsInline(record) {
        const roas = record.ROAS || 0;
        const orders = record.Orders || 0;

        record.ROASStatus =
            roas >= this.alertThresholds.roasGood
                ? 'Excellent'
                : roas >= this.alertThresholds.roasLow
                ? 'Good'
                : roas >= this.alertThresholds.roasCritical
                ? 'Warning'
                : 'Critical';

        record.CreativePerformance =
            roas >= 3.0 && orders >= 10
                ? 'Excellent'
                : roas >= 2.0 && orders >= 5
                ? 'Good'
                : roas >= 1.5
                ? 'Fair'
                : 'Poor';

        record.AlertLevel =
            roas < 1.5 || orders < 5
                ? 'High'
                : roas < 2.0 || orders < 10
                ? 'Medium'
                : 'Low';
    }

    // ✅ Helper methods
    batchedRefresh(changes = {}) {
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
        }

        if (changes.values && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.values = changes.values;
        }
        if (changes.dataSource && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.dataSource =
                changes.dataSource;
        }
        if (changes.formatSettings && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.formatSettings =
                changes.formatSettings;
        }
        if (changes.conditionalFormatSettings && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.conditionalFormatSettings =
                changes.conditionalFormatSettings;
        }
        if (changes.filterSettings && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.filterSettings =
                changes.filterSettings;
        }

        this.refreshTimeout = setTimeout(() => {
            if (this.pivotTableObj && !this.pendingRefresh) {
                this.pendingRefresh = true;

                const refreshStart = performance.now();
                this.performanceMetrics.refreshCount++;
                this.performanceMetrics.lastRefreshTime = Date.now();

                this.pivotTableObj.refresh();

                setTimeout(() => {
                    this.pendingRefresh = false;
                }, 100);
            }
        }, 50);
    }

    generateDataHash(data) {
        if (!data) return null;

        const factCount = data.factGmvMaxProductCreatives?.length || 0;
        const dimCounts = [
            data.dimAdAccounts?.length || 0,
            data.dimBusinessCenters?.length || 0,
            data.dimCampaigns?.length || 0,
            data.dimStores?.length || 0,
            data.dimProducts?.length || 0,
            data.dimDates?.length || 0,
            data.dimTTAccounts?.length || 0,
        ].join('-');

        return `${factCount}_${dimCounts}`;
    }

    createCachedLookups(data) {
        if (!data) return {};

        const dataHash = this.generateDataHash(data);

        if (this.lastDataHash === dataHash && this.cachedLookups) {
            return this.cachedLookups;
        }

        // ✅ DEBUG: Log dim arrays
        console.log('🔍 Creative Pivot - Dim Arrays:', {
            adAccounts: data.dimAdAccounts?.length || 0,
            businessCenters: data.dimBusinessCenters?.length || 0,
            campaigns: data.dimCampaigns?.length || 0,
            stores: data.dimStores?.length || 0,
            products: data.dimProducts?.length || 0,
            dates: data.dimDates?.length || 0,
            ttAccounts: data.dimTTAccounts?.length || 0,
        });
        console.log(
            '🔍 Creative Pivot - Sample dimAdAccount:',
            data.dimAdAccounts?.[0]
        );
        console.log(
            '🔍 Creative Pivot - Sample dimTTAccount:',
            data.dimTTAccounts?.[0]
        );

        this.cachedLookups = {
            adAccount: this.createLookup(data.dimAdAccounts, 'id'),
            businessCenter: this.createLookup(data.dimBusinessCenters, 'id'),
            campaign: this.createLookup(data.dimCampaigns, 'id'),
            store: this.createLookup(data.dimStores, 'id'),
            product: this.createLookup(data.dimProducts, 'id'),
            date: this.createLookup(data.dimDates, 'id'),
            ttAccount: this.createLookup(data.dimTTAccounts, 'id'),
        };

        console.log('🔍 Creative Pivot - Created lookups:', {
            adAccountKeys: Object.keys(this.cachedLookups.adAccount).length,
            businessCenterKeys: Object.keys(this.cachedLookups.businessCenter)
                .length,
            campaignKeys: Object.keys(this.cachedLookups.campaign).length,
            storeKeys: Object.keys(this.cachedLookups.store).length,
            productKeys: Object.keys(this.cachedLookups.product).length,
            dateKeys: Object.keys(this.cachedLookups.date).length,
            ttAccountKeys: Object.keys(this.cachedLookups.ttAccount).length,
        });

        this.lastDataHash = dataHash;
        return this.cachedLookups;
    }

    createLookup(array, keyField) {
        if (!array) return {};
        const lookup = {};
        array.forEach((item) => {
            lookup[item[keyField]] = item;
        });
        return lookup;
    }

    getCurrencyValue(fact, fieldName) {
        if (this.currentCurrency === 'VND') {
            const vndVal = fact[`${fieldName}VND`];
            if (vndVal !== undefined) return vndVal || 0;
        } else {
            const usdVal = fact[`${fieldName}USD`];
            if (usdVal !== undefined) return usdVal || 0;
        }
        const fallback = fact[fieldName];
        return fallback || 0;
    }

    getCurrencyFormatSettings() {
        const isVnd = this.currentCurrency === 'VND';
        const suffix = isVnd ? ' đ' : ' $';
        return [
            { name: 'Cost', format: 'N0', suffix },
            { name: 'GrossRevenue', format: 'N0', suffix },
            { name: 'CostPerOrder', format: 'N2', suffix },
            { name: 'ROAS', format: 'N2', suffix: 'x' },
            { name: 'TACOS', format: 'P2' },
            { name: 'ProductClickRate', format: 'P2' },
            { name: 'AdClickRate', format: 'P2' },
            { name: 'AdConversionRate', format: 'P2' },
        ];
    }

    async getCurrencyValues(columnAggregations = null) {
        const currency = this.currentCurrency;
        const currencySymbol = currency === 'VND' ? '₫' : '$';
        const aggregations =
            columnAggregations || this.getStoredColumnAggregations();

        // Get permissions
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('creative');

        const allValues = [
            // Revenue Metrics
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu (${currencySymbol})`,
                type: aggregations['GrossRevenue'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },

            // Cost Metrics
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo (${currencySymbol})`,
                type: aggregations['Cost'] || 'Sum',
                showSubTotals: true,
                category: 'spending',
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn (${currencySymbol})`,
                type: aggregations['CostPerOrder'] || 'Avg',
                showSubTotals: false,
                category: 'spending',
            },

            // Performance Metrics
            {
                name: 'ROAS',
                caption: 'ROI (Tỷ lệ hoàn vốn quảng cáo)',
                type: aggregations['ROAS'] || 'Avg',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'Orders',
                caption: 'Số đơn hàng',
                type: aggregations['Orders'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'TACOS',
                caption: 'TACOS (Tỷ lệ chi phí/doanh thu)',
                type: aggregations['TACOS'] || 'Avg',
                showSubTotals: true,
                category: 'restricted',
            },

            // Creative-specific metrics
            {
                name: 'ProductImpressions',
                caption: 'Lượt hiển thị sản phẩm',
                type: aggregations['ProductImpressions'] || 'Sum',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'ProductClicks',
                caption: 'Lượt click sản phẩm',
                type: aggregations['ProductClicks'] || 'Sum',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'ProductClickRate',
                caption: 'Tỷ lệ click sản phẩm (%)',
                type: aggregations['ProductClickRate'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdClickRate',
                caption: 'Tỷ lệ click quảng cáo (%)',
                type: aggregations['AdClickRate'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdConversionRate',
                caption: 'Tỷ lệ chuyển đổi (%)',
                type: aggregations['AdConversionRate'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRate2s',
                caption: 'Tỷ lệ xem video 2s (%)',
                type: aggregations['AdVideoViewRate2s'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRate6s',
                caption: 'Tỷ lệ xem video 6s (%)',
                type: aggregations['AdVideoViewRate6s'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP25',
                caption: 'Tỷ lệ xem video 25% (%)',
                type: aggregations['AdVideoViewRateP25'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP50',
                caption: 'Tỷ lệ xem video 50% (%)',
                type: aggregations['AdVideoViewRateP50'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP75',
                caption: 'Tỷ lệ xem video 75% (%)',
                type: aggregations['AdVideoViewRateP75'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP100',
                caption: 'Tỷ lệ xem video 100% (%)',
                type: aggregations['AdVideoViewRateP100'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
        ];

        // Filter values based on permissions
        return allValues.filter((value) => {
            if (permissions.viewAll || permissions.viewAllAdvertisers)
                return true;
            if (
                value.category === 'spending' &&
                (permissions.viewSpending || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'metrics' &&
                (permissions.viewMetrics || permissions.viewAllAdvertisers)
            )
                return true;
            if (value.category === 'restricted') return false; // Restricted fields chỉ hiển thị với ViewAll/ViewAllAdvertisers
            return false;
        });
    }

    async getCurrencyValuesWithAggregations() {
        const currentAggregations = this.getStoredColumnAggregations();
        return await this.getCurrencyValues(currentAggregations);
    }

    getStoredColumnAggregations() {
        const stored = localStorage.getItem('creativeColumnAggregations');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (e) {
                console.warn(
                    'Failed to parse stored column aggregations, using defaults'
                );
            }
        }

        return {
            Cost: 'Sum',
            GrossRevenue: 'Sum',
            Orders: 'Sum',
            ROAS: 'Avg',
            CostPerOrder: 'Avg',
            ProductImpressions: 'Sum',
            ProductClicks: 'Sum',
            ProductClickRate: 'Avg',
            AdClickRate: 'Avg',
            AdConversionRate: 'Avg',
            AdVideoViewRate2s: 'Avg',
            AdVideoViewRate6s: 'Avg',
            AdVideoViewRateP25: 'Avg',
            AdVideoViewRateP50: 'Avg',
            AdVideoViewRateP75: 'Avg',
            AdVideoViewRateP100: 'Avg',
        };
    }

    storeColumnAggregations(aggregations) {
        localStorage.setItem(
            'creativeColumnAggregations',
            JSON.stringify(aggregations)
        );
    }

    getSmartTimeColumns() {
        try {
            let from = null;
            let to = null;

            const pickerEl = document.getElementById(
                'campaign-date-range-picker'
            );
            if (
                pickerEl &&
                pickerEl.ej2_instances &&
                pickerEl.ej2_instances[0]
            ) {
                const picker = pickerEl.ej2_instances[0];
                from = picker.startDate ? new Date(picker.startDate) : null;
                to = picker.endDate ? new Date(picker.endDate) : null;
            }

            if (
                (!from || !to) &&
                window.gmvMaxConfig &&
                window.gmvMaxConfig.dateRange
            ) {
                from = new Date(window.gmvMaxConfig.dateRange.from);
                to = new Date(window.gmvMaxConfig.dateRange.to);
            }

            if (from && to) {
                return getSmartTimeGrouping({ from, to });
            }
            return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
        } catch (error) {
            console.error('Error in getSmartTimeColumns:', error);
            return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
        }
    }

    handleCellClick(args) {
        if (args.currentCell && args.data) {
            const cellData = args.data[0];
            if (!cellData) return;

            if (
                cellData.CreativePerformance === 'Poor' ||
                cellData.ROASStatus === 'Critical'
            ) {
                this.showCreativeAlert(cellData);
            }
        }
    }

    showCreativeAlert(data) {
        const message = `
            <strong>⚠️ Cảnh báo hiệu suất creative!</strong><br>
            Creative: ${data.Title}<br>
            ROAS: ${data.ROAS}x (${data.ROASStatus})<br>
            Đơn hàng: ${data.Orders}<br>
            <em>Cần tối ưu hóa hoặc dừng creative!</em>
        `;
        showToast('warning', message);
    }

    updateCreativeInsights() {
        // Dashboard is now independent of pivot table
    }

    async refreshData(newData) {
        if (this.pivotTableObj && newData) {
            this.pivotTableObj.dataSourceSettings.dataSource =
                await this.extractPivotDataOptimized(['creative'], newData);
            this.pivotTableObj.refresh();
        }
    }

    exportToExcel(fileName = 'TikTok_GMV_Max_Creative_Analysis') {
        if (this.pivotTableObj) {
            this.pivotTableObj.excelExport({
                fileName: `${fileName}_${new Date()
                    .toISOString()
                    .slice(0, 10)}.xlsx`,
                includeHeader: true,
            });
        }
    }

    exportToPdf(fileName = 'TikTok_GMV_Max_Creative_Report') {
        if (this.pivotTableObj) {
            this.pivotTableObj.pdfExport({
                fileName: `${fileName}_${new Date()
                    .toISOString()
                    .slice(0, 10)}.pdf`,
                includeHeader: true,
            });
        }
    }

    showChart() {
        if (this.pivotTableObj) {
            this.pivotTableObj.displayOption.view = 'Chart';
            this.pivotTableObj.chartSettings.chartSeries.type = 'Column';
            this.pivotTableObj.refresh();
        }
    }

    showGrid() {
        if (this.pivotTableObj) {
            this.pivotTableObj.displayOption.view = 'Grid';
            this.pivotTableObj.refresh();
        }
    }

    async updatePivotTableWithColumnAggregations() {
        if (!this.pivotTableObj) return;

        try {
            const currentAggregations = this.getStoredColumnAggregations();
            const config = JSON.parse(
                localStorage.getItem('creativeColumnAggregations') || '{}'
            );
            const selectedValues = config.selectedFields || [
                'GrossRevenue',
                'Cost',
                'CostPerOrder',
                'ROAS',
                'Orders',
                'ProductImpressions',
                'ProductClicks',
            ];

            if (selectedValues.length === 0) {
                return;
            }

            const allValues = await this.getCurrencyValues(currentAggregations);
            const newValues = allValues.filter((v) =>
                selectedValues.includes(v.name)
            );

            this.pivotTableObj.dataSourceSettings.values = newValues;
            this.pivotTableObj.refresh();
        } catch (error) {
            console.error(
                'Failed to update pivot table with aggregations:',
                error
            );
        }
    }
}
