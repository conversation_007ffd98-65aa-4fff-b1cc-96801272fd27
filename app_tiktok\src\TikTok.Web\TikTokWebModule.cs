﻿using Custom.ElsaWorkflow;
using Elsa.Persistence.EntityFramework.Core.Services;
using Elsa.Services;
using Hangfire;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
// using Module.Notifications.Web; // removed, incorrect namespace
using OpenIddict.Validation.AspNetCore;
using Rebus.Config;
using System;
using System.IO;
using System.Threading.Tasks;
using TikTok.Activities;
using TikTok.Activities.Extensions;
using TikTok.EntityFrameworkCore;
using TikTok.Localization;
using TikTok.MultiTenancy;
using TikTok.Web.CustomTopbar;
using TikTok.Web.Menus;
using TikTok.Web.Middleware;
using TikTok.Web.Notification.ContextProviders;
using TikTok.Application.Contracts.SystemNotification.Realtime;
using TikTok.Web.Notification.Realtime;
using Tsp.Zalo.Web;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.FileSystem;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Hangfire;
using Volo.Abp.Identity.Web;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement.Web;
using Volo.Abp.Security.Claims;
using Volo.Abp.SettingManagement.Web;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Microsoft.AspNetCore.SignalR;
using TikTok.Web.Hubs;
using Module.Notifications.Web;

namespace TikTok.Web;

[DependsOn(
    typeof(TikTokHttpApiModule),
    typeof(TikTokApplicationModule),
    typeof(TikTokEntityFrameworkCoreModule),
    typeof(TiktokActivityModule),
    typeof(AbpAutofacModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpSettingManagementWebModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpSwashbuckleModule),
    typeof(ZaloWebModule),
    typeof(AbpBlobStoringFileSystemModule),
    typeof(NotificationsWebModule)
    )]
public class TikTokWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(TikTokResource),
                typeof(TikTokDomainModule).Assembly,
                typeof(TikTokDomainSharedModule).Assembly,
                typeof(TikTokApplicationModule).Assembly,
                typeof(TikTokApplicationContractsModule).Assembly,
                typeof(TikTokWebModule).Assembly
            );
        });

        PreConfigure<OpenIddictBuilder>(builder =>
        {
            builder.AddValidation(options =>
            {
                options.AddAudiences("TikTok");
                options.UseLocalServer();
                options.UseAspNetCore();
            });
        });

        // if (!hostingEnvironment.IsDevelopment())
        // {
        //     PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
        //     {
        //         options.AddDevelopmentEncryptionAndSigningCertificate = false;
        //     });

        //     PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
        //     {
        //         serverBuilder.AddProductionEncryptionAndSigningCertificate("openiddict.pfx", "0b096df3-67ba-4fe6-b3c5-a7015a365fd1");
        //     });
        // }
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        ConfigureAuthentication(context);
        ConfigureUrls(configuration);
        ConfigureBundles();
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        ConfigureSwaggerServices(context.Services);
        ConfigureLocalization();
        ConfigureHangfire(context, configuration);
        ConfigureElsa(context, configuration);
        ConfigureBlobStorage(context);
        ConfigureCustomTopbar(context);
        ConfigureCors(context, configuration);
        ConfigureNotifications(context);
    }

    private void ConfigureNotifications(ServiceConfigurationContext context)
    {
        context.Services.AddScoped<SystemNotificationContextProvider>();
        // Bridge Application -> SignalR (debounced wrapper)
        context.Services.AddTransient<SignalRRealtimeNotificationBroadcaster>();
        context.Services.AddTransient<IRealtimeNotificationBroadcaster, DebouncedRealtimeNotificationBroadcaster>();
    }

    private void ConfigureHangfire(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddHangfire(config =>
        {
            var connectionString = configuration.GetConnectionString("Default");
            config.UseSqlServerStorage(connectionString, new Hangfire.SqlServer.SqlServerStorageOptions()
            {
                // === CLEANUP CONFIGURATION ===
                JobExpirationCheckInterval = TimeSpan.FromHours(1), // Kiểm tra sau mỗi 1 giờ
                DeleteExpiredBatchSize = 1000, // Xóa 1000 records/lần
                CountersAggregateInterval = TimeSpan.FromMinutes(10), // Tổng hợp counter mỗi 10 phút

                //QueuePollInterval = TimeSpan.FromSeconds(10),
                DashboardJobListLimit = 50000,
            });

        });
    }

    private void ConfigureLocalization()
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Languages.Add(new LanguageInfo("vi", "vi", "Tiếng Việt"));
            options.Languages.Add(new LanguageInfo("en", "en", "English"));
        });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context)
    {
        context.Services.ForwardIdentityAuthenticationForBearer(OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });

        // Configure cookie authentication for automatic login redirects
        context.Services.ConfigureApplicationCookie(options =>
        {
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";
            options.AccessDeniedPath = "/Account/AccessDenied";
            options.ReturnUrlParameter = "returnUrl";
            options.ExpireTimeSpan = TimeSpan.FromDays(30);
            options.SlidingExpiration = true;
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = Microsoft.AspNetCore.Http.CookieSecurePolicy.SameAsRequest;
        });
    }



    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                    bundle.AddFiles("/libs/tsp/jslibs/syncfusions/styles/custom-query-builder.css");
                    bundle.AddFiles("/libs/tsp/jslibs/syncfusions/javascript/dist/bootstrap5.css");
                    bundle.AddFiles("/libs/tsp/jslibs/fluentui/fluentui-system-icons/fonts/FluentSystemIcons-Regular.css");
                    bundle.AddFiles("/libs/tsp/jslibs/fluentui/fluentui-system-icons/fonts/FluentSystemIcons-Filled.css");
                    bundle.AddFiles("/libs/tsp/jslibs/styles/common.css");
                }
            );
            options.ScriptBundles.Configure(
               LeptonXLiteThemeBundles.Scripts.Global,
               bundle =>
               {
                   // SignalR client must be loaded before any page script using window.signalR
                   bundle.AddFiles("/libs/signalr/signalr.min.js");
                   bundle.AddFiles("/libs/tsp/jslibs/syncfusions/javascript/dist/ej2.min.js");
                   bundle.AddFiles("/libs/tsp/jslibs/syncfusions/register-syncfusion.js");
                   bundle.AddFiles("/libs/tsp/jslibs/syncfusions/register-culture.js");
                   bundle.AddFiles("/libs/tsp/jslibs/libCommon.js");
                   bundle.AddFiles("/libs/tsp/jslibs/libselect.js");
                   bundle.AddFiles("/libs/jquery/jquery.js");
                   bundle.AddFiles("/libs/bootstrap/js/bootstrap.bundle.js");
                   bundle.AddFiles("/js/auth-utils.js"); // Add authentication utilities
               }
           );
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<TikTokWebModule>();
        });
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        if (hostingEnvironment.IsDevelopment())
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.ReplaceEmbeddedByPhysical<TikTokDomainSharedModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TikTok.Domain.Shared"));
                options.FileSets.ReplaceEmbeddedByPhysical<TikTokDomainModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TikTok.Domain"));
                options.FileSets.ReplaceEmbeddedByPhysical<TikTokApplicationContractsModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TikTok.Application.Contracts"));
                options.FileSets.ReplaceEmbeddedByPhysical<TikTokApplicationModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TikTok.Application"));
                options.FileSets.ReplaceEmbeddedByPhysical<TikTokWebModule>(hostingEnvironment.ContentRootPath);
            });
        }
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new TikTokMenuContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(TikTokApplicationModule).Assembly);
        });
    }

    private void ConfigureCustomTopbar(ServiceConfigurationContext context)
    {
        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new CustomTopbarToolbarContributor());
        });
    }

    private void ConfigureSwaggerServices(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(
            options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "TikTok API", Version = "v1" });
                options.DocInclusionPredicate((docName, description) => true);
                options.CustomSchemaIds(type => type.FullName);
            }
        );
    }

    private void ConfigureElsa(ServiceConfigurationContext context, IConfiguration configuration)
    {
        var elsaConfigs = configuration.GetSection("Elsa").Get<ElsaConfigs>();
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        context.Services.AddCustomElsa2(elsaConfigs, new WorkflowDefinitionsOptions()
        {
            DefinitionLocation = hostingEnvironment.ContentRootPath,
            CurrentFolder = "Tiktok.WDfs"
        }, (builder) =>
        {
            builder.AddTiktokActivities();
        });
        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.AutoValidate = false;
            options.AutoValidateIgnoredHttpMethods.Add("POST");
        });
    }
    private void ConfigureBlobStorage(ServiceConfigurationContext context)
    {
        var env = context.Services.GetHostingEnvironment();

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(container =>
            {
                container.UseFileSystem(fileSystem =>
                {
                    // read appsettings.json to get the base path
                    var options = context.Services.GetConfiguration();
                    string? basePath = options["BlobStorage:BasePath"];
                    if (string.IsNullOrEmpty(basePath))
                    {
                        basePath = "./BlobFile";
                    }

                    fileSystem.BasePath = basePath;
                });
            });
        });
    }

    private void ConfigureCors(ServiceConfigurationContext context, IConfiguration configuration)
    {
        var corsOrigins = configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? new string[] { "http://localhost:3006" };

        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder.WithOrigins(corsOrigins);
                builder.AllowAnyHeader()
                       .AllowAnyMethod()
                       .AllowCredentials();
            });
        });
    }

    public override async Task OnApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
        }

        app.UseCorrelationId();
        app.UseStaticFiles();
        app.UseCors();
        app.UseRouting();
        app.UseAuthentication();
        app.UseAbpOpenIddictValidation();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseHttpActivities();
        app.UseUnitOfWork();
        app.UseDynamicClaims();

        // Add custom return URL middleware after routing but before authorization
        app.UseMiddleware<ReturnUrlMiddleware>();

        app.UseAuthorization();
        if (env.IsDevelopment())
        {
            app.UseSwagger();
            app.UseAbpSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "TikTok API");
            });
        }
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();
        app.UseHangfireDashboard();
        app.UseHangfireServer();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHangfireDashboard(new DashboardOptions()
            {
                AsyncAuthorization = new[] { new AbpHangfireAuthorizationFilter() }
            });
            // Elsa API Endpoints are implemented as regular ASP.NET Core API controllers.
            endpoints.MapControllers();
            endpoints.MapHub<NotificationHub>("/notificationHub");
        });
    }
}
