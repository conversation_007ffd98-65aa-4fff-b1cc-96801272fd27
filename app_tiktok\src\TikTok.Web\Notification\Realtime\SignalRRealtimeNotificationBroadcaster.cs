using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using TikTok.Application.Contracts.SystemNotification.Realtime;
using TikTok.Web.Hubs;

namespace TikTok.Web.Notification.Realtime
{
    public class SignalRRealtimeNotificationBroadcaster : IRealtimeNotificationBroadcaster
    {
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly ILogger<SignalRRealtimeNotificationBroadcaster> _logger;

        public SignalRRealtimeNotificationBroadcaster(
            IHubContext<NotificationHub> hubContext,
            ILogger<SignalRRealtimeNotificationBroadcaster> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task BroadcastUserUpdatedAsync(Guid userId, string? context = null, string? objectId = null)
        {
            var groupName = $"User_{userId}";
            await _hubContext.Clients.Group(groupName).SendAsync("ReceiveNotification", null);
            _logger.LogDebug("Broadcasted ReceiveNotification to {Group} (context={Context}, objectId={ObjectId})", groupName, context, objectId);
        }

        public async Task BroadcastUserSummaryAsync(Guid userId, NotificationSummaryUpdatedDto summary)
        {
            var groupName = $"User_{userId}";
            await _hubContext.Clients.Group(groupName).SendAsync("NotificationSummaryUpdated", summary);
            _logger.LogDebug("Broadcasted NotificationSummaryUpdated to {Group} (delta={Delta}, count={Count})", groupName, summary.UnreadDelta, summary.UnreadCount);
        }
    }
}


