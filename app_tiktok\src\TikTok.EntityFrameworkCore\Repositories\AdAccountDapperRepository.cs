using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.Entities;
using TikTok.EntityFrameworkCore;
using TikTok.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.Repositories
{
    /// <summary>
    /// Dapper repository implementation cho AdAccount
    /// </summary>
    public class AdAccountDapperRepository : DapperRepository<RawAdAccountEntity>, IAdAccountDapperRepository
    {
        public AdAccountDapperRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) 
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// Lấy tất cả ad accounts với SQL query (không bị giới hạn 1000)
        /// </summary>
        public async Task<List<RawAdAccountEntity>> GetAllAdAccountsAsync(string? filter = null, string? bcId = null, string? advertiserId = null)
        {
            // Input validation để tránh SQL injection và performance issues
            if (!string.IsNullOrEmpty(filter) && filter.Length > 100)
            {
                throw new ArgumentException("Filter text cannot exceed 100 characters", nameof(filter));
            }
            
            if (!string.IsNullOrEmpty(bcId) && bcId.Length > 50)
            {
                throw new ArgumentException("BC ID cannot exceed 50 characters", nameof(bcId));
            }
            
            if (!string.IsNullOrEmpty(advertiserId) && advertiserId.Length > 50)
            {
                throw new ArgumentException("Advertiser ID cannot exceed 50 characters", nameof(advertiserId));
            }

            var sql = @"
                SELECT 
                    Id,
                    AdvertiserId,
                    OwnerBcId,
                    Status,
                    Role,
                    RejectionReason,
                    Name,
                    Timezone,
                    DisplayTimezone,
                    Company,
                    CompanyNameEditable,
                    Industry,
                    Address,
                    Country,
                    AdvertiserAccountType,
                    Currency,
                    Contacter,
                    Email,
                    CellphoneNumber,
                    TelephoneNumber,
                    Language,
                    LicenseNo,
                    LicenseUrl,
                    Description,
                    Balance,
                    CreateTime,
                    IsRemoved,
                    RemovedAt,
                    CreationTime,
                    LastModificationTime,
                    CreatorId,
                    LastModifierId
                FROM Raw_RawAdAccounts 
                WHERE 1=1";

            var parameters = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(filter))
            {
                // Sanitize filter để tránh SQL injection
                var sanitizedFilter = filter.Trim().Replace("'", "''").Replace(";", "").Replace("--", "");
                sql += " AND (Name LIKE @Filter OR AdvertiserId LIKE @Filter)";
                parameters.Add("Filter", $"%{sanitizedFilter}%");
            }

            if (!string.IsNullOrEmpty(bcId))
            {
                sql += " AND OwnerBcId = @BcId";
                parameters.Add("BcId", bcId.Trim());
            }

            if (!string.IsNullOrEmpty(advertiserId))
            {
                sql += " AND AdvertiserId = @AdvertiserId";
                parameters.Add("AdvertiserId", advertiserId.Trim());
            }

            sql += " ORDER BY Name, AdvertiserId";

            var results = await QueryAsync<RawAdAccountEntity>(sql, parameters);
            return results.ToList();
        }

        /// <summary>
        /// Lấy ad accounts theo advertiser IDs (batch query)
        /// </summary>
        public async Task<List<RawAdAccountEntity>> GetAdAccountsByAdvertiserIdsAsync(List<string> advertiserIds)
        {
            if (!advertiserIds.Any())
                return new List<RawAdAccountEntity>();

            var sql = @"
                SELECT 
                    Id,
                    AdvertiserId,
                    OwnerBcId,
                    Status,
                    Role,
                    RejectionReason,
                    Name,
                    Timezone,
                    DisplayTimezone,
                    Company,
                    CompanyNameEditable,
                    Industry,
                    Address,
                    Country,
                    AdvertiserAccountType,
                    Currency,
                    Contacter,
                    Email,
                    CellphoneNumber,
                    TelephoneNumber,
                    Language,
                    LicenseNo,
                    LicenseUrl,
                    Description,
                    Balance,
                    CreateTime,
                    IsRemoved,
                    RemovedAt,
                    CreationTime,
                    LastModificationTime,
                    CreatorId,
                    LastModifierId
                FROM Raw_RawAdAccounts 
                WHERE AdvertiserId IN @AdvertiserIds
                ORDER BY Name, AdvertiserId";

            var parameters = new { AdvertiserIds = advertiserIds };

            var results = await QueryAsync<RawAdAccountEntity>(sql, parameters);
            return results.ToList();
        }

        /// <summary>
        /// Lấy ad accounts hợp lệ (chỉ những ad accounts có BC ID tồn tại trong hệ thống)
        /// </summary>
        public async Task<List<RawAdAccountEntity>> GetValidAdAccountsAsync(string? filter = null, string? bcId = null, string? advertiserId = null)
        {
            // Input validation để tránh SQL injection và performance issues
            if (!string.IsNullOrEmpty(filter) && filter.Length > 100)
            {
                throw new ArgumentException("Filter text cannot exceed 100 characters", nameof(filter));
            }
            
            if (!string.IsNullOrEmpty(bcId) && bcId.Length > 50)
            {
                throw new ArgumentException("BC ID cannot exceed 50 characters", nameof(bcId));
            }
            
            if (!string.IsNullOrEmpty(advertiserId) && advertiserId.Length > 50)
            {
                throw new ArgumentException("Advertiser ID cannot exceed 50 characters", nameof(advertiserId));
            }

            var sql = @"
                SELECT 
                    a.Id,
                    a.AdvertiserId,
                    a.OwnerBcId,
                    a.Status,
                    a.Role,
                    a.RejectionReason,
                    a.Name,
                    a.Timezone,
                    a.DisplayTimezone,
                    a.Company,
                    a.CompanyNameEditable,
                    a.Industry,
                    a.Address,
                    a.Country,
                    a.AdvertiserAccountType,
                    a.Currency,
                    a.Contacter,
                    a.Email,
                    a.CellphoneNumber,
                    a.TelephoneNumber,
                    a.Language,
                    a.LicenseNo,
                    a.LicenseUrl,
                    a.Description,
                    a.Balance,
                    a.CreateTime,
                    a.IsRemoved,
                    a.RemovedAt,
                    a.CreationTime,
                    a.LastModificationTime,
                    a.CreatorId,
                    a.LastModifierId
                FROM Raw_RawAdAccounts a
                INNER JOIN Raw_RawBusinessCenters bc ON a.OwnerBcId = bc.BcId
                WHERE 1=1";

            var parameters = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(filter))
            {
                // Sanitize filter để tránh SQL injection
                var sanitizedFilter = filter.Trim().Replace("'", "''").Replace(";", "").Replace("--", "");
                sql += " AND (a.Name LIKE @Filter OR a.AdvertiserId LIKE @Filter)";
                parameters.Add("Filter", $"%{sanitizedFilter}%");
            }

            if (!string.IsNullOrEmpty(bcId))
            {
                sql += " AND a.OwnerBcId = @BcId";
                parameters.Add("BcId", bcId.Trim());
            }

            if (!string.IsNullOrEmpty(advertiserId))
            {
                sql += " AND a.AdvertiserId = @AdvertiserId";
                parameters.Add("AdvertiserId", advertiserId.Trim());
            }

            sql += " ORDER BY a.Name, a.AdvertiserId";

            var results = await QueryAsync<RawAdAccountEntity>(sql, parameters);
            return results.ToList();
        }
    }
}
