using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application service cho GMV Max Campaign Posts Sync
    /// </summary>
    public class GmvMaxCampaignPostsSyncApplicationAppService : ApplicationService, IGmvMaxCampaignPostsSyncApplicationAppService
    {
        private readonly IGmvMaxCampaignPostsSyncService _gmvMaxCampaignPostsSyncService;
        private readonly ILogger<GmvMaxCampaignPostsSyncApplicationAppService> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GmvMaxCampaignPostsSyncApplicationAppService(
            IGmvMaxCampaignPostsSyncService gmvMaxCampaignPostsSyncService,
            ILogger<GmvMaxCampaignPostsSyncApplicationAppService> logger)
        {
            _gmvMaxCampaignPostsSyncService = gmvMaxCampaignPostsSyncService;
            _logger = logger;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo Business Center ID
        /// Tự động truy vấn tất cả campaignId và advertiserId từ bảng RawGmvMaxCampaignsEntity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsAsync(string bcId)
        {
            _logger.LogInformation("Bắt đầu đồng bộ GMV Max Campaign Posts cho BC: {BcId}", bcId);

            try
            {
                var result = await _gmvMaxCampaignPostsSyncService.SyncGmvMaxCampaignPostsAsync(bcId);

                _logger.LogInformation("Hoàn thành đồng bộ GMV Max Campaign Posts cho BC: {BcId}. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, Thời gian: {Duration}s",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho BC: {BcId}", bcId);
                throw;
            }
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts cho nhiều Campaign của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="campaignIds">Danh sách Campaign IDs (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncManyGmvMaxCampaignPostsAsync(string advertiserId, string bcId, List<string>? campaignIds = null)
        {
            _logger.LogInformation("Bắt đầu đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}, BC: {BcId}, Campaign Count: {CampaignCount}",
                advertiserId, bcId, campaignIds?.Count ?? 0);

            try
            {
                var result = await _gmvMaxCampaignPostsSyncService.SyncManyGmvMaxCampaignPostsAsync(advertiserId, bcId, campaignIds);

                _logger.LogInformation("Hoàn thành đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, " +
                    "Campaign: {CampaignCount}, Thời gian: {Duration}s",
                    advertiserId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords,
                    result.CampaignCount, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}", advertiserId);
                throw;
            }
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign Posts cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncAllGmvMaxCampaignPostsAsync()
        {
            _logger.LogInformation("Bắt đầu đồng bộ tất cả GMV Max Campaign Posts cho tất cả BC");

            try
            {
                var result = await _gmvMaxCampaignPostsSyncService.SyncAllGmvMaxCampaignPostsAsync();

                _logger.LogInformation("Hoàn thành đồng bộ tất cả GMV Max Campaign Posts. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, " +
                    "BC: {BcCount}, Advertiser: {AdvertiserCount}, Campaign: {CampaignCount}, Thời gian: {Duration}s",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords,
                    result.BcCount, result.AdvertiserCount, result.CampaignCount, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả GMV Max Campaign Posts");
                throw;
            }
        }
    }
}
