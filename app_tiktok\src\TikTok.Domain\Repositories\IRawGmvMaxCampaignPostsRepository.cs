using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository interface cho RawGmvMaxCampaignPostsEntity
    /// </summary>
    public interface IRawGmvMaxCampaignPostsRepository : IRepository<RawGmvMaxCampaignPostsEntity, Guid>
    {
        /// <summary>
        /// L<PERSON><PERSON> danh sách posts theo Campaign ID
        /// </summary>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByCampaignIdAsync(
            string campaignId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// L<PERSON><PERSON> danh s<PERSON>ch posts theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts theo Identity ID
        /// </summary>
        /// <param name="identityId">ID của Identity</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByIdentityIdAsync(
            string identityId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts theo Identity Type
        /// </summary>
        /// <param name="identityType">Loại Identity</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByIdentityTypeAsync(
            string identityType,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts theo Video Definition
        /// </summary>
        /// <param name="videoDefinition">Độ phân giải video</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoDefinitionAsync(
            string videoDefinition,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts theo Video Format
        /// </summary>
        /// <param name="videoFormat">Định dạng video</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoFormatAsync(
            string videoFormat,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts có video duration trong khoảng
        /// </summary>
        /// <param name="minDuration">Thời lượng tối thiểu (giây)</param>
        /// <param name="maxDuration">Thời lượng tối đa (giây)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoDurationRangeAsync(
            double minDuration,
            double maxDuration,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách posts có video size trong khoảng
        /// </summary>
        /// <param name="minSize">Kích thước tối thiểu (bytes)</param>
        /// <param name="maxSize">Kích thước tối đa (bytes)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách posts</returns>
        Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoSizeRangeAsync(
            long minSize,
            long maxSize,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy post theo Item ID
        /// </summary>
        /// <param name="itemId">ID của Item</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Post entity hoặc null</returns>
        Task<RawGmvMaxCampaignPostsEntity?> GetByItemIdAsync(
            string itemId,
            string campaignId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy post theo Video ID
        /// </summary>
        /// <param name="videoId">ID của Video</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Post entity hoặc null</returns>
        Task<RawGmvMaxCampaignPostsEntity?> GetByVideoIdAsync(
            string videoId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra post có tồn tại không
        /// </summary>
        /// <param name="itemId">ID của Item</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>True nếu tồn tại</returns>
        Task<bool> ExistsAsync(
            string itemId,
            string campaignId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Xóa tất cả posts của một Campaign
        /// </summary>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Số lượng bản ghi đã xóa</returns>
        Task<int> DeleteByCampaignIdAsync(
            string campaignId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Xóa tất cả posts của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Số lượng bản ghi đã xóa</returns>
        Task<int> DeleteByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);
    }
}
