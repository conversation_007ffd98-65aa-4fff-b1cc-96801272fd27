using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Facts.FactGmvMaxCampaign;
using TikTok.Facts.FactGmvMaxProduct;

namespace TikTok.Domain.Repositories
{
    public interface IFactGmvMaxProductDapperRepository
    {
        Task<IEnumerable<FactGmvMaxProductEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        
        // ✅ NEW: Enhanced method with additional filters for better performance
        Task<IEnumerable<FactGmvMaxProductEntity>> GetListByDateRangeWithFiltersAsync(
            DateTime from, 
            DateTime to, 
            List<string>? allowedAdvertiserIds = null, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? creativeType = null,
            string? productId = null);
        Task<IEnumerable<GmvMaxProductTrendDto>> GetTrendsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null);
        Task<IEnumerable<GmvMaxProductTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit, List<string>? allowedAdvertiserIds = null);
        Task<GmvMaxProductDashboardDto> GetDashboardAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        
        // ✅ NEW: Section-specific methods for independent loading
        Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD", 
            List<string>? allowedAdvertiserIds = null,
            string? creativeType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null,
            string? productId = null);
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null);
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
    }
}


