﻿using TikTok.EntityFrameworkCore;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;
using Microsoft.Extensions.DependencyInjection;
using TikTok.Data;
using TikTok.DbMigrator.Data.Dims;
using TikTok.DbMigrator.Data.Facts;

namespace TikTok.DbMigrator;

[DependsOn(
    typeof(AbpAutofacModule),
    typeof(TikTokEntityFrameworkCoreModule),
    typeof(TikTokApplicationContractsModule)
    )]
public class TikTokDbMigratorModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Register all custom data seeders with ordering support
        // These implement ICustomDataSeedContributor which provides Order control
        // Dimensions
        context.Services.AddTransient<ICustomDataSeedContributor, DimDateDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimAdAccountDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimBusinessCenterDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimCampaignDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimDateDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimProductDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimStoreDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, DimTTAccountDataSeeder>();
        // Facts
        context.Services.AddTransient<ICustomDataSeedContributor, FactBalanceDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, FactCampaignDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, FactDailySpendDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, FactGmvMaxProductDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, FactGmvMaxCampaignDataSeeder>();
        context.Services.AddTransient<ICustomDataSeedContributor, FactGmvMaxProductCreativeDataSeeder>();
    }
}
