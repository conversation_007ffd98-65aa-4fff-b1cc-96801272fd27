using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho tài sản (Asset)
    /// </summary>
    public class RawAssetEntity : AuditedEntity<Guid>
    {
        /// <summary>
        /// ID duy nhất của tài sản
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AssetId { get; set; }

        /// <summary>
        /// Tên của tài sản
        /// </summary>
        [Required]
        [StringLength(255)]
        public string AssetName { get; set; }

        /// <summary>
        /// Loại tài sản
        /// </summary>
        [Required]
        public AssetType AssetType { get; set; }

        /// <summary>
        /// ID của Business Center sở hữu tài sản
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Loại tài khoản quảng cáo
        /// </summary>
        public AdAccountType? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với tài khoản quảng cáo
        /// </summary>
        public AdvertiserRole? AdvertiserRole { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với danh mục
        /// </summary>
        public CatalogRole? CatalogRole { get; set; }

        /// <summary>
        /// Cho biết liệu danh mục có thể được sử dụng trong quảng cáo
        /// </summary>
        public AdCreationEligible? AdCreationEligible { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với TikTok Shop
        /// </summary>
        public StoreRole? StoreRole { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với tài khoản TikTok
        /// </summary>
        public List<TtAccountRole>? TtAccountRoles { get; set; }

        /// <summary>
        /// Tên của Business Center sở hữu tài sản này
        /// </summary>
        public string? OwnerBcName { get; set; }

        /// <summary>
        /// Cờ đánh dấu đã bị xóa khỏi Business Center
        /// </summary>
        public bool IsRemoved { get; set; }

        /// <summary>
        /// Thời gian xóa khỏi Business Center (UTC)
        /// </summary>
        public DateTime? RemovedAt { get; set; }

        /// <summary>
        /// Loại quan hệ giữa Business Center và tài sản
        /// </summary>
        public RelationType? RelationType { get; set; }

        /// <summary>
        /// Trạng thái quan hệ giữa tài khoản Business Center và tài sản
        /// Trường này sẽ là null khi asset_type là CATALOG, LEAD, hoặc TT_ACCOUNT
        /// </summary>
        public RelationStatus? RelationStatus { get; set; }

        /// <summary>
        /// Trạng thái tài khoản quảng cáo (sử dụng enum AdAccountStatus)
        /// </summary>
        public AdAccountStatus? AdvertiserStatus { get; set; }

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public RawAssetEntity()
        {
            TtAccountRoles = new List<TtAccountRole>();
        }

        /// <summary>
        /// Constructor với ID
        /// </summary>
        /// <param name="id">ID của entity</param>
        public RawAssetEntity(Guid id) : base(id)
        {
            TtAccountRoles = new List<TtAccountRole>();
        }

        /// <summary>
        /// Kiểm tra xem entity có thay đổi so với entity khác không
        /// </summary>
        /// <param name="other">Entity khác để so sánh</param>
        /// <returns>True nếu có thay đổi, False nếu không có thay đổi</returns>
        public bool HasIsChanged(RawAssetEntity other)
        {
            if (other == null) return true;

            return AssetId != other.AssetId ||
                   AssetName != other.AssetName ||
                   AssetType != other.AssetType ||
                   BcId != other.BcId ||
                   AdvertiserAccountType != other.AdvertiserAccountType ||
                   AdvertiserRole != other.AdvertiserRole ||
                   CatalogRole != other.CatalogRole ||
                   AdCreationEligible != other.AdCreationEligible ||
                   StoreRole != other.StoreRole ||
                   OwnerBcName != other.OwnerBcName ||
                   IsRemoved != other.IsRemoved ||
                   RemovedAt != other.RemovedAt ||
                   RelationType != other.RelationType ||
                   RelationStatus != other.RelationStatus ||
                   AdvertiserStatus != other.AdvertiserStatus ||
                   !TtAccountRolesEqual(TtAccountRoles, other.TtAccountRoles);
        }

        /// <summary>
        /// Cập nhật dữ liệu từ entity khác
        /// </summary>
        /// <param name="source">Entity nguồn để cập nhật dữ liệu</param>
        public void UpdateFrom(RawAssetEntity source)
        {
            if (source == null) return;

            AssetId = source.AssetId;
            AssetName = source.AssetName;
            AssetType = source.AssetType;
            BcId = source.BcId;
            AdvertiserAccountType = source.AdvertiserAccountType;
            AdvertiserRole = source.AdvertiserRole;
            CatalogRole = source.CatalogRole;
            AdCreationEligible = source.AdCreationEligible;
            StoreRole = source.StoreRole;
            OwnerBcName = source.OwnerBcName;
            IsRemoved = source.IsRemoved;
            RemovedAt = source.RemovedAt;
            RelationType = source.RelationType;
            RelationStatus = source.RelationStatus;
            AdvertiserStatus = source.AdvertiserStatus;

            // Cập nhật TtAccountRoles
            TtAccountRoles = source.TtAccountRoles != null
                ? new List<TtAccountRole>(source.TtAccountRoles)
                : new List<TtAccountRole>();
        }

        /// <summary>
        /// So sánh hai danh sách TtAccountRole
        /// </summary>
        /// <param name="list1">Danh sách thứ nhất</param>
        /// <param name="list2">Danh sách thứ hai</param>
        /// <returns>True nếu bằng nhau, False nếu khác nhau</returns>
        private bool TtAccountRolesEqual(List<TtAccountRole>? list1, List<TtAccountRole>? list2)
        {
            if (list1 == null && list2 == null) return true;
            if (list1 == null || list2 == null) return false;
            if (list1.Count != list2.Count) return false;

            for (int i = 0; i < list1.Count; i++)
            {
                if (list1[i] != list2[i]) return false;
            }

            return true;
        }
    }
}