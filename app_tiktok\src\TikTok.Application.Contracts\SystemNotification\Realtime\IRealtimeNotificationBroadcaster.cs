using System;
using System.Threading.Tasks;

namespace TikTok.Application.Contracts.SystemNotification.Realtime
{
    /// <summary>
    /// Abstraction to push realtime user notifications to connected clients (e.g., via SignalR).
    /// Kept in Contracts to avoid direct dependency on Web/SignalR from Application layer.
    /// </summary>
    public interface IRealtimeNotificationBroadcaster
    {
        /// <summary>
        /// Notify a specific user that new notifications are available.
        /// </summary>
        /// <param name="userId">Target user id (Identity GUID)</param>
        /// <param name="context">Optional notification context (e.g., SystemNotification)</param>
        /// <param name="objectId">Optional related object id</param>
        Task BroadcastUserUpdatedAsync(Guid userId, string? context = null, string? objectId = null);

        /// <summary>
        /// Notify a specific user with a summary payload to avoid extra API calls on client.
        /// </summary>
        Task BroadcastUserSummaryAsync(Guid userId, NotificationSummaryUpdatedDto summary);
    }
}


