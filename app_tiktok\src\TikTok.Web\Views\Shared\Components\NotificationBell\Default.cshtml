@* Notification Bell Component *@
@using Microsoft.Extensions.Localization
@using TikTok.Localization
@inject IStringLocalizer<TikTokResource> L
<div class="notification-bell-container position-relative">
    <button class="btn btn-link notification-bell-btn"
            type="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            id="notificationBell">
        <i class="fa-solid fa-bell notification-icon"></i>
        <span class="notification-badge position-absolute top-0 start-100 translate-middle rounded-pill bg-danger"
              id="notificationCount">
            0
        </span>
    </button>

    <div class="dropdown-menu dropdown-menu-end notification-dropdown">
        <div class="dropdown-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">@L["Notifications"]</h6>
            <button class="btn btn-sm btn-outline-secondary" id="markAllRead">
                @L["MarkAllAsRead"]
            </button>
        </div>

        <div id="notificationList" class="notification-list">
            <div class="text-center text-muted py-3">
                <i class="fa-solid fa-bell-slash fa-2x mb-2"></i>
                <p class="mb-0">@L["NoNewNotifications"]</p>
            </div>
        </div>

        <div class="dropdown-divider"></div>
        <div class="text-center p-2 notification-footer-actions">
            <a href="/notifications" class="btn btn-sm btn-outline-primary">
                @L["ViewAllNotifications"]
            </a>
        </div>
    </div>
</div>

<style>
    .notification-bell-container .dropdown-menu { max-height: 75vh; width: 360px; max-width: calc(100vw - 16px); overflow: hidden;}
    .notification-list { max-height: calc(75vh - 150px); overflow: auto; }
    .notification-item { cursor: pointer; transition: background-color .15s ease-in-out; }
    .notification-item:hover { background-color: #f8f9fa; }
    .notification-item.unread { background-color: #f5f8ff; }
    .notification-dot { width: 8px; height: 8px; margin-left: .5rem; }
    .notification-time { color: #6c757d; font-size: .75rem; font-style: italic; text-align: right; display: block; }
    .notification-icon i { font-size: .85rem; }
    .notification-content { font-weight: 600; }
}</style>

<script>
(function () {
    'use strict';

    // Mirror backend enum SystemNotificationEntityType (use same code strings)
    const SystemNotificationEntityType = Object.freeze({
        RawGmvMaxProductCreativeReportEntity: 'RawGmvMaxProductCreativeReportEntity',
        RawGmvMaxProductCampaignReportEntity: 'RawGmvMaxProductCampaignReportEntity',
        RawGmvMaxLiveCampaignReportEntity: 'RawGmvMaxLiveCampaignReportEntity'
    });

    function initNotificationBell() {
        const bell = document.getElementById('notificationBell');
        const count = document.getElementById('notificationCount');
        const list = document.getElementById('notificationList');
        const markAllRead = document.getElementById('markAllRead');

        if (!bell || !count || !list) return;

        // Initial: fetch unread count once
        safeLoadNotificationCount();

        markAllRead?.addEventListener('click', markAllAsRead);

        // Fetch full list when dropdown is opened
        bell.addEventListener('shown.bs.dropdown', loadNotifications);


        // SignalR realtime
        try {
            if (window.signalR) {
                const connection = new signalR.HubConnectionBuilder()
                    .withUrl('/notificationHub')
                    .withAutomaticReconnect()
                    .build();

                // Summary-based update: adjust badge without API
                connection.on('NotificationSummaryUpdated', function (summary) {
                    try {
                        if (summary && typeof summary === 'object') {
                            if (typeof summary.unreadCount === 'number') {
                                setBadgeCount(summary.unreadCount);
                            } else if (typeof summary.unreadDelta === 'number') {
                                adjustBadge(summary.unreadDelta);
                            }
                        }
                    } catch (_) { }
                });

                connection.on('ReceiveNotification', function (notification) {
                    // Fallback: lightweight count refresh (throttled)
                    try { safeLoadNotificationCount(); } catch (_) { }
                });

                // After reconnect, force a refresh to catch up
                connection.onreconnected(function () {
                    try { safeLoadNotificationCount(); } catch (_) { }
                });

                connection.start().then(function () {
                    try {
                        // If you have user id on page, pass it; fallback will rely on server claims later
                        const currentUserId = (window.abp && abp.currentUser && abp.currentUser.id) || null;
                        if (currentUserId) {
                            connection.invoke('JoinUserGroup', currentUserId.toString());
                        }
                        // Connected successfully
                    } catch (_) { }
                }).catch(function (err) {
                    console.warn('SignalR start failed', err);
                    // No polling fallback by design
                });
            }
        } catch (e) { console.warn('SignalR not available', e); }

        // Reconcile when tab becomes visible
        document.addEventListener('visibilitychange', function () {
            if (!document.hidden) {
                safeLoadNotificationCount();
            }
        });
    }

    async function loadNotifications() {
        try {
            const currentUserId = (window.abp && abp.currentUser && abp.currentUser.id) || null;
            if (!currentUserId) { return; }
            const response = await fetch(`/api/Notifications/GetNotificationsUnReadByUser?userId=${currentUserId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const data = await response.json();
            updateNotificationDisplay(data);
        } catch (error) {
            console.error('Error loading notifications:', error);
            updateNotificationDisplay([]);
        }
    }

    // Fetch only unread count and update badge (one-shot or on push)
    async function loadNotificationCount() {
        try {
            const currentUserId = (window.abp && abp.currentUser && abp.currentUser.id) || null;
            if (!currentUserId) { return; }
            const response = await fetch(`/api/Notifications/GetNotificationsUnReadByUser?userId=${currentUserId}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const data = await response.json();
            const countEl = document.getElementById('notificationCount');
            const unreadCount = Array.isArray(data) ? data.filter(n => !n.isRead).length : 0;
            if (countEl) {
                if (unreadCount > 0) {
                    countEl.textContent = unreadCount > 99 ? '99+' : unreadCount;
                    countEl.classList.add('show');
                } else {
                    countEl.textContent = '0';
                    countEl.classList.remove('show');
                }
            }
        } catch (error) {
            console.warn('Error loading notification count:', error);
        }
    }

    // Throttle/in-flight guard for count fetching
    let _countInFlight = false;
    let _lastCountAt = 0;
    const COUNT_THROTTLE_MS = 2000;
    function safeLoadNotificationCount() {
        const now = Date.now();
        if (_countInFlight) return;
        if (now - _lastCountAt < COUNT_THROTTLE_MS) return;
        if (document.hidden) return;
        _countInFlight = true;
        loadNotificationCount().finally(() => { _lastCountAt = Date.now(); _countInFlight = false; });
    }

    function setBadgeCount(value) {
        const countEl = document.getElementById('notificationCount');
        if (!countEl) return;
        const n = Math.max(0, Number(value) || 0);
        if (n > 0) {
            countEl.textContent = n > 99 ? '99+' : n;
            countEl.classList.add('show');
        } else {
            countEl.textContent = '0';
            countEl.classList.remove('show');
        }
    }

    function adjustBadge(delta) {
        const countEl = document.getElementById('notificationCount');
        if (!countEl) return;
        const current = parseInt(countEl.textContent || '0', 10) || 0;
        const next = Math.max(0, current + (Number(delta) || 0));
        setBadgeCount(next);
    }

    function updateNotificationDisplay(notifications) {
        const count = document.getElementById('notificationCount');
        const list = document.getElementById('notificationList');
        if (!count || !list) return;

        const unreadCount = notifications.filter(n => !n.isRead).length;
        if (unreadCount > 0) {
            count.textContent = unreadCount > 99 ? '99+' : unreadCount;
            count.classList.add('show');
        } else {
            count.textContent = '0';
            count.classList.remove('show');
        }

        if (notifications.length === 0) {
            list.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fa-solid fa-bell-slash fa-2x mb-2"></i>
                    <p class="mb-0">@L["NoNewNotifications"]</p>
                </div>
            `;
            return;
        }

        list.innerHTML = notifications.map(n => createItemHTML(n)).join('');

        list.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', async () => {
                const id = item.dataset.id;
                const url = item.dataset.url;
                await debouncedMarkAsRead(id);
                if (url && url !== '#') window.location.href = url;
            });
        });
    }

    function formatDisplayTime(n) {
        try {
            // Use only 'created' as the source of time
            const raw = n.created;
            if (!raw) return '';
            const date = new Date(raw);
            if (isNaN(date.getTime())) return '';

            const now = new Date();
            const diffMs = now - date;
            const diffSec = Math.floor(diffMs / 1000);
            const diffMin = Math.floor(diffSec / 60);
            const diffHr = Math.floor(diffMin / 60);
            const diffDay = Math.floor(diffHr / 24);

            if (diffSec < 60) return '@L["JustNow"]';
            if (diffMin < 60) return `${diffMin} @L["MinutesAgo"]`;
            if (diffHr < 24) return `${diffHr} @L["HoursAgo"]`;
            if (diffDay < 7) return `${diffDay} @L["DaysAgo"]`;

            return date.toLocaleString();
        } catch { return ''; }
    }

    function createItemHTML(n) {
        const unreadClass = !n.isRead ? 'unread' : '';
        const iconClass = getIconByContextOrType(n.context || n.type);
        const timeText = formatDisplayTime(n);
        const url = getUrlForNotification(n);
        return `
            <div class="notification-item p-3 border-bottom ${unreadClass}" data-id="${n.id}" data-url="${url}">
                <div class="d-flex align-items-start">
                    <div class="notification-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-1 small notification-content">${n.content || ''}</p>
                        ${timeText ? `<span class=\"notification-time fst-italic d-block text-end\">${timeText}</span>` : ''}
                    </div>
                    ${!n.isRead ? '<div class="notification-dot bg-primary rounded-circle"></div>' : ''}
                </div>
            </div>
        `;
    }

    // Build deep-link using payload.EntityType to match backend mapping
    function getUrlForNotification(n) {
        if (n.url && n.url !== '#') return n.url;
        // Parse payload if available
        let payload = null;
        try {
            if (n.payload && typeof n.payload === 'string') {
                payload = JSON.parse(n.payload);
            } else if (typeof n.payload === 'object') {
                payload = n.payload;
            }
        } catch (_) { payload = null; }

        const entityType = payload?.EntityType || payload?.Metadata?.EntityType || '';
        const campaignId = payload?.CampaignId || payload?.campaignId || payload?.ObjectId || payload?.Metadata?.CampaignId || '';

        switch (entityType) {
            // ProductCampaign and LiveCampaign → GmvMax with tab=campaign
            case SystemNotificationEntityType.RawGmvMaxProductCampaignReportEntity:
            case SystemNotificationEntityType.RawGmvMaxLiveCampaignReportEntity:
                if (campaignId) {
                    return `/GmvMax?tab=campaign&campaignId=${encodeURIComponent(campaignId)}`;
                }
                return '/GmvMax?tab=campaign';

            // productCreatives → GmvMax with tab=video
            case SystemNotificationEntityType.RawGmvMaxProductCreativeReportEntity:
                if (campaignId) {
                    return `/GmvMax?tab=video&campaignId=${encodeURIComponent(campaignId)}`;
                }
                return '/GmvMax?tab=video';

            default:
                // legacy or unknown: try context mapping
                const context = (n.context || n.type || '').toString();
                if (context === 'GmvMaxCreativeStatusChange') {
                    if (campaignId) {
                        return `/GmvMax?tab=video&campaignId=${encodeURIComponent(campaignId)}`;
                    }
                    return '/GmvMax?tab=video';
                }
                return '#';
        }
    }

    function getIconByContextOrType(value) {
        const map = {
            // GMV Max Creative specific context/type
            'GmvMaxCreativeStatusChange': 'fa-solid fa-bell text-primary',
            // fallbacks
            'success': 'fa-solid fa-circle-check text-success',
            'warning': 'fa-solid fa-triangle-exclamation text-warning',
            'error': 'fa-solid fa-circle-xmark text-danger',
            'info': 'fa-solid fa-circle-info text-info'
        };
        return map[value] || 'fa-solid fa-bell text-secondary';
    }

    async function markAsRead(id) {
        try {
            await fetch(`/api/Notifications/SetReadNotification?id=${id}`, { method: 'PATCH', headers: { 'Content-Type': 'application/json' } });
        } catch (e) {
            console.error('markAsRead failed', e);
        }
    }

    async function markAllAsRead() {
        try {
            const currentUserId = (window.abp && abp.currentUser && abp.currentUser.id) || null;
            if (!currentUserId) { return; }
            await fetch(`/api/Notifications/MarkAllAsRead?userId=${currentUserId}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' } });
            loadNotifications();
        } catch (e) {
            console.error('markAllAsRead failed', e);
        }
    }

    // Simple debounce helper
    function debounce(fn, wait) {
        let t;
        return (...args) => {
            clearTimeout(t);
            t = setTimeout(() => fn.apply(null, args), wait);
        };
    }

    const debouncedMarkAsRead = debounce(markAsRead, 250);

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNotificationBell);
    } else {
        initNotificationBell();
    }
})();
</script>
