using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities.Dim;

/// <summary>
/// Dimension table cho TikTok Account
/// Lưu trữ thông tin các TikTok account được sử dụng trong creative campaigns
/// Hỗ trợ SCD Type 2 để theo dõi thay đổi thông tin account theo thời gian
/// </summary>
[Table("Dim_TTAccount")]
public class DimTTAccountEntity : AuditedEntity<Guid>
{
    public DimTTAccountEntity()
    {
        
    }

    public DimTTAccountEntity(Guid key): base(key)
    {

    }

    /// <summary>
    /// Business Key - ID TikTok account
    /// </summary>
    [Required]
    [StringLength(100)]
    public string TtAccountId { get; set; }

    /// <summary>
    /// Business Key - Tên TikTok account (có thể là 0 hoặc -1 nếu thiếu quyền)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.TtAccountName
    /// </summary>
    [Required]
    public string TtAccountName { get; set; }

    /// <summary>
    /// URL hình đại diện TikTok account
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.TtAccountProfileImageUrl
    /// </summary>
    public string? TtAccountProfileImageUrl { get; set; }

    /// <summary>
    /// Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.TtAccountAuthorizationType
    /// </summary>
    [StringLength(20)]
    public string? TtAccountAuthorizationType { get; set; }


    /// <summary>
    /// Bản ghi hiện tại (SCD Type 2)
    /// </summary>
    public bool IsCurrent { get; set; }

    /// <summary>
    /// Phiên bản bản ghi (SCD Type 2)
    /// </summary>
    public int RowVersion { get; set; }

    /// <summary>
    /// Kiểm tra xem có thay đổi so với entity khác không
    /// </summary>
    /// <param name="other">Entity để so sánh</param>
    /// <returns>True nếu có thay đổi</returns>
    public bool HasChange(DimTTAccountEntity other)
    {
        return this.TtAccountName != other.TtAccountName ||
            this.TtAccountProfileImageUrl != other.TtAccountProfileImageUrl ||
            this.TtAccountAuthorizationType != other.TtAccountAuthorizationType;
    }

    /// <summary>
    /// Cập nhật thông tin từ entity khác
    /// </summary>
    /// <param name="other">Entity nguồn</param>
    public void UpdateFrom(DimTTAccountEntity other)
    {
        this.TtAccountName = other.TtAccountName;
        this.TtAccountProfileImageUrl = other.TtAccountProfileImageUrl;
        this.TtAccountAuthorizationType = other.TtAccountAuthorizationType;
        this.IsCurrent = other.IsCurrent;
        this.RowVersion = other.RowVersion;
    }
}
