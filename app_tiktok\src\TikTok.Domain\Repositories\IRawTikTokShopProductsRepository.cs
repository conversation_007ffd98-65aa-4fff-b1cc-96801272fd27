using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository interface cho RawTikTokShopProductsEntity
    /// </summary>
    public interface IRawTikTokShopProductsRepository : IRepository<RawTikTokShopProductsEntity, Guid>
    {
        /// <summary>
        /// Lấy danh sách products theo Store ID
        /// </summary>
        /// <param name="storeId">ID của Store</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products</returns>
        Task<List<RawTikTokShopProductsEntity>> GetByStoreIdAsync(
            string storeId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// L<PERSON>y danh sách products theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products</returns>
        Task<List<RawTikTokShopProductsEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách products theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products</returns>
        Task<List<RawTikTokShopProductsEntity>> GetByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách products theo trạng thái
        /// </summary>
        /// <param name="status">Trạng thái sản phẩm</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products</returns>
        Task<List<RawTikTokShopProductsEntity>> GetByStatusAsync(
            string status,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách products có sẵn (status = AVAILABLE)
        /// </summary>
        /// <param name="storeId">ID của Store (optional)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products có sẵn</returns>
        Task<List<RawTikTokShopProductsEntity>> GetAvailableProductsAsync(
            string? storeId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách products theo GMV Max Ads Status
        /// </summary>
        /// <param name="gmvMaxAdsStatus">Trạng thái GMV Max Ads</param>
        /// <param name="storeId">ID của Store (optional)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products</returns>
        Task<List<RawTikTokShopProductsEntity>> GetByGmvMaxAdsStatusAsync(
            string gmvMaxAdsStatus,
            string? storeId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách products đang chạy custom shop ads
        /// </summary>
        /// <param name="storeId">ID của Store (optional)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách products đang chạy custom shop ads</returns>
        Task<List<RawTikTokShopProductsEntity>> GetRunningCustomShopAdsAsync(
            string? storeId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy product theo Item Group ID
        /// </summary>
        /// <param name="itemGroupId">ID nhóm sản phẩm</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Product entity hoặc null</returns>
        Task<RawTikTokShopProductsEntity?> GetByItemGroupIdAsync(
            string itemGroupId,
            string storeId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra product có tồn tại không
        /// </summary>
        /// <param name="itemGroupId">ID nhóm sản phẩm</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>True nếu tồn tại</returns>
        Task<bool> ExistsAsync(
            string itemGroupId,
            string storeId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Xóa tất cả products của một Store
        /// </summary>
        /// <param name="storeId">ID của Store</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Số lượng bản ghi đã xóa</returns>
        Task<int> DeleteByStoreIdAsync(
            string storeId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Xóa tất cả products của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Số lượng bản ghi đã xóa</returns>
        Task<int> DeleteByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);
    }
}
