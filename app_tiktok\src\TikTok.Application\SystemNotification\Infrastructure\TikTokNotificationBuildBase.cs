using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Consts;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.SystemNotification
{
    /// <summary>
    /// Abstract base class for TikTok notification builders
    /// Simplified approach without GenericFactory dependency
    /// </summary>
    public abstract class TikTokNotificationBuildBase : ITikTokNotificationBuildProvider
    {
        protected readonly IAbpLazyServiceProvider LazyServiceProvider;
        protected readonly ILogger Logger;

        protected TikTokNotificationBuildBase(IAbpLazyServiceProvider abpLazyServiceProvider)
        {
            LazyServiceProvider = abpLazyServiceProvider;
            Logger = LazyServiceProvider.LazyGetRequiredService<ILogger<TikTokNotificationBuildBase>>();
        }

        public abstract string Context { get; }

        public abstract IEnumerable<TikTokUserType> AllowSendToUserTypes { get; }

        public abstract Task<string> GetTemplateTitle(TikTokUserType userType, TikTokEntityType? entityType = null);

        public virtual async Task<IEnumerable<TikTokNotificationDto>> BuildNotifications(string objectId)
        {
            try
            {
                var entityData = await GetEntityData(objectId);
                if (entityData == null)
                {
                    Logger.LogWarning("Entity not found: {ObjectId}", objectId);
                    return Enumerable.Empty<TikTokNotificationDto>();
                }

                var users = await GetNotificationUsers(entityData);

                return await BuildNotificationByUsers(objectId, users);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error building notifications for {ObjectId}", objectId);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TikTokNotificationDto>> BuildNotificationByUsers(string objectId, IEnumerable<TikTokNotificationUserDto> users)
        {
            try
            {
                var entityData = await GetEntityData(objectId);
                if (entityData == null)
                {
                    return Enumerable.Empty<TikTokNotificationDto>();
                }

                var notifications = new List<TikTokNotificationDto>();

                foreach (var user in users.Where(u => AllowSendToUserTypes.Contains(u.UserType)))
                {
                    var title = await BuildNotificationTitle(entityData, user.UserType);
                    var content = await BuildNotificationContent(entityData, user.UserType);
                    var payload = await BuildNotificationPayload(entityData, user.UserType);

                    notifications.Add(new TikTokNotificationDto
                    {
                        UserId = user.UserId,
                        UserType = user.UserType,
                        ObjectId = objectId,
                        Context = GetNotificationContext(),
                        Title = title,
                        Content = content,
                        Payload = payload,
                        PhoneNumber = user.PhoneNumber,
                        AdAccountId = user.AdAccountId,
                        BcId = user.BcId
                    });
                }

                return notifications;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error building notifications for users");
                throw;
            }
        }

        protected virtual Task<string> BuildNotificationTitle(object entityData, TikTokUserType userType)
        {
            return GetTemplateTitle(userType);
        }

        protected abstract Task<object> GetEntityData(string objectId);

        protected abstract Task<IEnumerable<TikTokNotificationUserDto>> GetNotificationUsers(object entityData);

        protected virtual Task<string> BuildNotificationContent(object entityData, TikTokUserType userType)
        {
            return Task.FromResult($"Notification content for {userType}");
        }

        protected virtual Task<string> BuildNotificationPayload(object entityData, TikTokUserType userType)
        {
            return Task.FromResult("{}");
        }

        protected abstract string GetNotificationContext();

        protected string ReplaceTemplatePlaceholders(string template, Dictionary<string, string> replacements)
        {
            var result = template;
            foreach (var replacement in replacements)
            {
                result = result.Replace(replacement.Key, replacement.Value);
            }
            return result;
        }
    }
}


