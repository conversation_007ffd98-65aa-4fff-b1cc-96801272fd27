/**
 * Constants and Configuration for System Notification Rules
 * Centralized configuration values used across the application
 */

// Entity Types - Now using centralized configuration
// This will be populated by entityTypesConfig.js
const ENTITY_TYPES = window.SystemNotificationEntityTypes || {};

// Status Types
const STATUS_TYPES = {
    ACTIVE: 1,
    INACTIVE: 2,
    SUSPENDED: 3,
    PENDING: 4
};

// Status Badge Classes
const STATUS_BADGE_CLASSES = {
    [STATUS_TYPES.ACTIVE]: 'bg-success',
    [STATUS_TYPES.INACTIVE]: 'bg-warning',
    [STATUS_TYPES.SUSPENDED]: 'bg-danger',
    [STATUS_TYPES.PENDING]: 'bg-info',
    DEFAULT: 'bg-secondary'
};

// Status Text
const STATUS_TEXT = {
    [STATUS_TYPES.ACTIVE]: 'ACTIVE',
    [STATUS_TYPES.INACTIVE]: 'INACTIVE',
    [STATUS_TYPES.SUSPENDED]: 'SUSPENDED',
    [STATUS_TYPES.PENDING]: 'PENDING',
    DEFAULT: 'UNKNOWN'
};

// Data Type Mappings
const DATA_TYPE_MAPPINGS = {
    string: 'string',
    number: 'number',
    decimal: 'number',
    boolean: 'boolean',
    datetime: 'date',
    enum: 'string',
};

// Input Type Mappings
const INPUT_TYPE_MAPPINGS = {
    string: 'text',
    number: 'number',
    boolean: 'checkbox',
    date: 'datetime-local',
};

// Operator Mappings
const OPERATOR_MAPPINGS = {
    equal: 'equal',
    equals: 'equal',
    'not equals': 'notequal',
    notequals: 'notequal',
    contains: 'contains',
    startswith: 'startswith',
    endswith: 'endswith',
    greaterthan: 'greaterthan',
    lessthan: 'lessthan',
    greaterthanorequal: 'greaterthanorequal',
    lessthanorequal: 'lessthanorequal',
    between: 'between',
    in: 'in',
    notin: 'notin',
};

// API Configuration
const API_CONFIG = {
    MAX_RESULT_COUNT: 50,
    DEFAULT_SKIP_COUNT: 0,
    DEFAULT_SORTING: 'name'
};

// View Modes
const VIEW_MODES = {
    SIMPLE: 'simple',
    DETAIL: 'detail',
    TABLE: 'table'
};

// Default View Mode
const DEFAULT_VIEW_MODE = VIEW_MODES.DETAIL;

// Modal IDs
const MODAL_IDS = {
    SYSTEM_NOTIFICATION_RULE: 'SystemNotificationRuleModal',
    SYSTEM_NOTIFICATION_RULE_CONSUMERS: 'systemNotificationRuleConsumersModal',
    SELECT_AD_ACCOUNTS: 'selectSystemNotificationAdAccountsModal',
    ENTITY_CHANGE_WARNING: 'entityChangeWarningModal'
};

// Element IDs
const ELEMENT_IDS = {
    TABLE: 'SystemNotificationRulesTable',
    FILTER_ENTITY_TYPE: 'filterEntityType',
    FILTER_RULE_NAME: 'filterRuleName',
    FILTER_IS_DEFAULT: 'filterIsDefault',
    CLEAR_FILTERS: 'clearFilters',
    NEW_RULE_BUTTON: 'NewRuleButton',
    SAVE_RULE_BUTTON: 'saveRuleButton',
    EDIT_RULE_ID: 'editRuleId',
    EDIT_RULE_NAME: 'editRuleName',
    EDIT_ENTITY_TYPE: 'editEntityType',
    EDIT_IS_DEFAULT: 'editIsDefault',
    EDIT_IS_PUBLIC: 'editIsPublic',
    EDIT_QUERYBUILDER: 'editQuerybuilder',
    EDIT_CONDITIONS_JSON: 'editConditionsJson'
};

// Local Storage Keys
const STORAGE_KEYS = {
    CONSUMERS_VIEW_MODE: 'systemNotificationConsumersDataViewMode'
};

// Export to global scope
window.SystemNotificationConstants = {
    ENTITY_TYPES,
    STATUS_TYPES,
    STATUS_BADGE_CLASSES,
    STATUS_TEXT,
    DATA_TYPE_MAPPINGS,
    INPUT_TYPE_MAPPINGS,
    OPERATOR_MAPPINGS,
    API_CONFIG,
    VIEW_MODES,
    DEFAULT_VIEW_MODE,
    MODAL_IDS,
    ELEMENT_IDS,
    STORAGE_KEYS
};
