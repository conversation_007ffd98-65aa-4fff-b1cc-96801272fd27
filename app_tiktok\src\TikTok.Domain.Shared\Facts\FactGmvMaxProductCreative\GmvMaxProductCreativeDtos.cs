using System;
using System.Collections.Generic;
using TikTok.Facts.FactGmvMaxProduct;

namespace TikTok.Facts.FactGmvMaxProductCreative
{
    /// <summary>
    /// DTO cho xu hướng ProductCreative theo thời gian
    /// </summary>
    public class GmvMaxProductCreativeTrendDto
    {
        public int DateId { get; set; }
        public decimal TotalGrossRevenue { get; set; }
        public decimal AverageROAS { get; set; }
        public decimal AverageTACOS { get; set; }
        public int TotalOrders { get; set; }
        public int CreativeCount { get; set; } // Thay vì ProductCount
        public int StoreCount { get; set; }
        public int AccountCount { get; set; } // Thêm TikTok Account count
    }

    /// <summary>
    /// DTO cho top creative bán chạy
    /// </summary>
    public class GmvMaxProductCreativeTopSellingDto
    {
        public string ItemGroupId { get; set; } = string.Empty; // Product Group ID
        public string ItemId { get; set; } = string.Empty; // Creative ID
        public string Title { get; set; } = string.Empty; // Creative Title
        public string CreativeType { get; set; } = string.Empty; // ADS_AND_ORGANIC, ORGANIC, etc.
        public string TtAccountName { get; set; } = string.Empty; // TikTok Account
        public int TotalOrders { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageROAS { get; set; }
        public decimal AverageCost { get; set; } // Thêm Cost tracking
    }

    /// <summary>
    /// DTO cho Dashboard ProductCreative - tương tự GmvMaxProductDashboardDto nhưng cho Creative level
    /// </summary>
    public class GmvMaxProductCreativeDashboardDto
    {
        public DashboardMonthInfo CurrentMonth { get; set; } = new();
        public DashboardMonthInfo LastMonth { get; set; } = new();
        public List<DashboardWeekly> WeeklyData { get; set; } = new();
        public List<DashboardMonthly> MonthlyData { get; set; } = new();
        public List<DashboardShopRanking> CurrentWeekShopRanking { get; set; } = new();
        public List<DashboardShopRanking> TwoWeeksAgoShopRanking { get; set; } = new();
        public List<DashboardShopRanking> OneWeekAgoShopRanking { get; set; } = new();
        // Cost rankings
        public List<DashboardShopCostRanking> CurrentWeekShopCostRanking { get; set; } = new();
        public List<DashboardShopCostRanking> TwoWeeksAgoShopCostRanking { get; set; } = new();
        public List<DashboardShopCostRanking> OneWeekAgoShopCostRanking { get; set; } = new();
        // ✅ NEW: Creative-specific rankings
        public List<DashboardCreativeRanking> CurrentWeekCreativeRanking { get; set; } = new();
        public List<DashboardAccountRanking> CurrentWeekAccountRanking { get; set; } = new();
    }

    /// <summary>
    /// DTO cho xếp hạng Creative theo hiệu suất
    /// </summary>
    public class DashboardCreativeRanking
    {
        public string ItemGroupId { get; set; } = string.Empty; // Product Group
        public string ItemId { get; set; } = string.Empty; // Creative ID
        public string Title { get; set; } = string.Empty; // Creative Title
        public string CreativeType { get; set; } = string.Empty; // ADS_AND_ORGANIC, ORGANIC, etc.
        public string TtAccountName { get; set; } = string.Empty; // TikTok Account
        public decimal TotalRevenue { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageROAS { get; set; }
        public decimal AverageCost { get; set; }
        public string ShopContentType { get; set; } = string.Empty; // VIDEO, PRODUCT_CARD
    }

    /// <summary>
    /// DTO cho xếp hạng TikTok Account theo hiệu suất
    /// </summary>
    public class DashboardAccountRanking
    {
        public string TtAccountName { get; set; } = string.Empty;
        public string TtAccountAuthorizationType { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageROAS { get; set; }
        public decimal AverageCost { get; set; }
        public int CreativeCount { get; set; } // Số lượng creative
        public int ProductCount { get; set; } // Số lượng sản phẩm
    }

    /// <summary>
    /// DTO cho Creative Performance Analysis - phân tích hiệu suất creative
    /// </summary>
    public class GmvMaxProductCreativePerformanceDto
    {
        public string ItemGroupId { get; set; } = string.Empty;
        public string ItemId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string CreativeType { get; set; } = string.Empty;
        public string ShopContentType { get; set; } = string.Empty;
        public string CreativeDeliveryStatus { get; set; } = string.Empty;
        public string TtAccountName { get; set; } = string.Empty;
        public string StoreName { get; set; } = string.Empty;
        public decimal Cost { get; set; }
        public decimal GrossRevenue { get; set; }
        public int Orders { get; set; }
        public decimal ROAS { get; set; }
        public decimal TACOS { get; set; }
        public decimal CostPerOrder { get; set; }
        public long? ProductImpressions { get; set; }
        public long? ProductClicks { get; set; }
        public decimal? ProductClickRate { get; set; }
        public decimal? AdClickRate { get; set; }
        public decimal? AdConversionRate { get; set; }
        public decimal? AdVideoViewRate2s { get; set; }
        public decimal? AdVideoViewRate6s { get; set; }
        public decimal? AdVideoViewRateP25 { get; set; }
        public decimal? AdVideoViewRateP50 { get; set; }
        public decimal? AdVideoViewRateP75 { get; set; }
        public decimal? AdVideoViewRateP100 { get; set; }
        public DateTime Date { get; set; }
        public string Currency { get; set; } = "USD";
    }

    /// <summary>
    /// DTO cho Creative Delivery Status Analysis - phân tích trạng thái creative
    /// </summary>
    public class GmvMaxProductCreativeDeliveryDto
    {
        public string CreativeDeliveryStatus { get; set; } = string.Empty;
        public int CreativeCount { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageROAS { get; set; }
        public decimal AverageCost { get; set; }
        public decimal PerformanceScore { get; set; } // Điểm hiệu suất 0-100
    }

    /// <summary>
    /// DTO cho Content Type Analysis - phân tích loại nội dung
    /// </summary>
    public class GmvMaxProductCreativeContentTypeDto
    {
        public string ShopContentType { get; set; } = string.Empty; // VIDEO, PRODUCT_CARD
        public int CreativeCount { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageROAS { get; set; }
        public decimal AverageCost { get; set; }
        public decimal AverageVideoViewRate { get; set; } // Chỉ áp dụng cho VIDEO
        public decimal AverageClickRate { get; set; }
        public decimal AverageConversionRate { get; set; }
    }

    // ✅ Reuse existing DTOs from FactGmvMaxCampaign namespace
    // DashboardMonthInfo, DashboardWeekly, DashboardMonthly, DashboardShopRanking, DashboardShopCostRanking
    // SummaryCardsDto, OverviewSectionDto, ChartsDataDto, DetailedChartsDto, RankingsDataDto
    // MonthDataDto, WeeklyDataDto, MonthlyDataDto, FinancialAnalysisDto, OrdersAnalysisDto, LivePerformanceDto
    // DailyDataDto, StoreRankingDto, StoreCostRankingDto, DashboardSummaryDto
}

