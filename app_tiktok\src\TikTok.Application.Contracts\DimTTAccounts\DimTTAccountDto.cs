using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimTTAccounts
{
    /// <summary>
    /// DTO cho DimTTAccountEntity - TikTok Account dimension
    /// </summary>
    public class DimTTAccountDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Business Key - ID TikTok account
        /// </summary>
        public string TtAccountId { get; set; } = string.Empty;

        /// <summary>
        /// Business Key - Tên TikTok account
        /// </summary>
        public string TtAccountName { get; set; } = string.Empty;

        /// <summary>
        /// URL hình đại diện TikTok account
        /// </summary>
        public string? TtAccountProfileImageUrl { get; set; }

        /// <summary>
        /// Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)
        /// </summary>
        public string? TtAccountAuthorizationType { get; set; }

        /// <summary>
        /// Bản ghi hiện tại (SCD Type 2)
        /// </summary>
        public bool IsCurrent { get; set; }

        /// <summary>
        /// Phiên bản bản ghi (SCD Type 2)
        /// </summary>
        public int RowVersion { get; set; }
    }
}
