using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Identity;
using Volo.Abp.PermissionManagement;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Application.Contracts.SystemNotification;

namespace TikTok.Application.SystemNotification
{
    /// <summary>
    /// Service để render templates và resolve recipients cho system notifications
    /// Xử lý cả standard và cross-entity notifications với flexible recipient configuration
    /// </summary>
    public class SystemTemplateRenderer : ITransientDependency
    {
        private readonly IIdentityUserRepository _userRepository;
        private readonly IPermissionManager _permissionManager;

        public SystemTemplateRenderer(
            IIdentityUserRepository userRepository,
            IPermissionManager permissionManager)
        {
            _userRepository = userRepository;
            _permissionManager = permissionManager;
        }

        /// <summary>
        /// Build notification request từ rule và entity
        /// </summary>
        public async Task<SystemNotificationRequest> BuildNotification<T>(
            SystemNotificationRule rule, 
            T entity)
        {
            var entityId = GetEntityId(entity);
            var entityName = GetEntityDisplayName(entity);
            object? fieldValue = null;
            var title = GenerateDefaultTitle(rule, entityName, fieldValue);
            var message = GenerateDefaultMessage(rule, entityName, fieldValue);

            var recipients = await GetRecipients(rule, entityId, new List<object> { entity! });

            return new SystemNotificationRequest
            {
                Title = title,
                Message = message,
                Recipients = recipients,
                EntityType = "FactGmvMaxCampaignEntity", // Always Campaign level
                EntityId = entityId,
                RuleId = rule.Id,
                RuleName = rule.RuleName,
                NotificationType = "SystemNotification",
                Icon = GetNotificationIcon(rule),
                Color = GetNotificationColor(rule),
                RedirectUrl = GenerateRedirectUrl(rule, entityId),
                Data = new Dictionary<string, object>
                {
                    ["ConditionsJson"] = rule.ConditionsJson ?? string.Empty,
                    ["EntityName"] = entityName,
                    ["RuleId"] = rule.Id,
                    ["RuleName"] = rule.RuleName
                }
            };
        }

        /// <summary>
        /// Build cross-entity notification request
        /// </summary>
        public async Task<SystemNotificationRequest> BuildCrossEntityNotification(
            SystemNotificationRule rule, 
            string notificationEntityId, 
            List<object> matchingEntities)
        {
            // System auto-generates notification content
            var title = GenerateCrossEntityDefaultTitle(rule, notificationEntityId, matchingEntities.Count);
            var message = GenerateCrossEntityDefaultMessage(rule, notificationEntityId, matchingEntities.Count);

            var recipients = await GetRecipients(rule, notificationEntityId, matchingEntities);

            return new SystemNotificationRequest
            {
                Title = title,
                Message = message,
                Recipients = recipients,
                EntityType = "FactGmvMaxCampaignEntity", // Always Campaign level
                EntityId = notificationEntityId,
                RuleId = rule.Id,
                RuleName = rule.RuleName,
                NotificationType = "SystemNotification",
                Icon = GetNotificationIcon(rule),
                Color = GetNotificationColor(rule),
                RedirectUrl = GenerateRedirectUrl(rule, notificationEntityId),
                Data = new Dictionary<string, object>
                {
                    ["SourceEntityType"] = rule.EntityType ?? string.Empty,
                    ["NotificationEntityType"] = "FactGmvMaxCampaignEntity", // Always Campaign level
                    ["MatchingCount"] = matchingEntities.Count,
                    ["ConditionsJson"] = rule.ConditionsJson ?? string.Empty,
                    ["CrossEntityField"] = "CampaignId", // Always group by CampaignId
                    ["RuleId"] = rule.Id,
                    ["RuleName"] = rule.RuleName
                }
            };
        }

        /// <summary>
        /// Resolve recipients based on rule configuration
        /// Note: RecipientType has been removed; now using SystemNotificationRuleConsumerEntity
        /// For backward compatibility, return rule creator as default recipient
        /// </summary>
        private Task<List<string>> GetRecipients(
            SystemNotificationRule rule, 
            string entityId, 
            List<object> entities)
        {
            return Task.FromResult(new List<string> { rule.CreatorId?.ToString() ?? string.Empty });
        }


        /// <summary>
        /// Render template với placeholders
        /// </summary>
        private Task<string> RenderTemplate<T>(
            string template, 
            SystemNotificationRule rule, 
            T entity, 
            string entityId, 
            string entityName, 
            object? fieldValue)
        {
            var rendered = template;

            // Replace common placeholders
            rendered = rendered.Replace("{EntityName}", entityName ?? "");
            rendered = rendered.Replace("{EntityId}", entityId ?? "");
            rendered = rendered.Replace("{RuleName}", rule.RuleName ?? "");

            // Replace entity-specific placeholders
            rendered = ReplaceEntityPlaceholders(rendered, entity);

            return Task.FromResult(rendered);
        }

        /// <summary>
        /// Render cross-entity template
        /// </summary>
        private Task<string> RenderCrossEntityTemplate(
            string template, 
            SystemNotificationRule rule, 
            string notificationEntityId, 
            List<object> matchingEntities)
        {
            var rendered = template;

            // Replace common placeholders
            rendered = rendered.Replace("{CampaignId}", notificationEntityId ?? "");
            rendered = rendered.Replace("{NotificationEntityId}", notificationEntityId ?? "");
            rendered = rendered.Replace("{MatchingCount}", matchingEntities.Count.ToString());
            rendered = rendered.Replace("{RuleName}", rule.RuleName ?? "");
            rendered = rendered.Replace("{SourceEntityType}", rule.EntityType ?? "");
            rendered = rendered.Replace("{NotificationEntityType}", "FactGmvMaxCampaignEntity");

            return Task.FromResult(rendered);
        }

        /// <summary>
        /// Replace entity-specific placeholders
        /// </summary>
        private string ReplaceEntityPlaceholders<T>(string template, T entity)
        {
            var rendered = template;

            // Get all properties from entity
            var properties = typeof(T).GetProperties();
            foreach (var property in properties)
            {
                var placeholder = $"{{{property.Name}}}";
                if (rendered.Contains(placeholder))
                {
                    var value = property.GetValue(entity);
                    rendered = rendered.Replace(placeholder, value?.ToString() ?? "");
                }
            }

            return rendered;
        }

        /// <summary>
        /// Generate default title
        /// </summary>
        private string GenerateDefaultTitle(SystemNotificationRule rule, string entityName, object? fieldValue)
        {
            // Friendlier, concise title with entity type context: "<EntityType> - <RuleName> • <EntityName>"
            var safeEntity = string.IsNullOrWhiteSpace(entityName) ? "Item" : entityName;
            var safeRule = string.IsNullOrWhiteSpace(rule.RuleName) ? "Rule" : rule.RuleName;
            var entityTypeContext = GetEntityTypeDisplayName(rule.EntityType);
            return $"{entityTypeContext} - {safeRule} • {safeEntity}";
        }

        /// <summary>
        /// Generate default message
        /// </summary>
        private string GenerateDefaultMessage(SystemNotificationRule rule, string entityName, object? fieldValue)
        {
            // Friendlier, action-oriented message with entity type context
            var safeEntity = string.IsNullOrWhiteSpace(entityName) ? "this item" : entityName;
            var safeRule = string.IsNullOrWhiteSpace(rule.RuleName) ? "the rule" : $"'{rule.RuleName}'";
            var entityTypeContext = GetEntityTypeDisplayName(rule.EntityType);
            return $"{entityTypeContext}: {safeRule} has been triggered for {safeEntity}. Review details and take action if needed.";
        }

        /// <summary>
        /// Generate cross-entity default title
        /// </summary>
        private string GenerateCrossEntityDefaultTitle(SystemNotificationRule rule, string notificationEntityId, int matchingCount)
        {
            var entityTypeContext = GetEntityTypeDisplayName(rule.EntityType);
            return $"Campaign {notificationEntityId} - {matchingCount} {entityTypeContext} match rule '{rule.RuleName}'";
        }

        /// <summary>
        /// Generate cross-entity default message
        /// </summary>
        private string GenerateCrossEntityDefaultMessage(SystemNotificationRule rule, string notificationEntityId, int matchingCount)
        {
            var entityTypeContext = GetEntityTypeDisplayName(rule.EntityType);
            return $"Rule '{rule.RuleName}' triggered for Campaign {notificationEntityId}. {matchingCount} {entityTypeContext} matched. Review details and take action if needed.";
        }

        /// <summary>
        /// Get notification icon based on rule
        /// </summary>
        private string? GetNotificationIcon(SystemNotificationRule rule)
        {
            // Simple default icon. Customize if needed based on rule.Priority or NotificationEntityType
            return "🔔";
        }

        /// <summary>
        /// Get notification color based on rule priority
        /// Note: Priority field has been removed, so return default color
        /// </summary>
        private string? GetNotificationColor(SystemNotificationRule rule)
        {
            // Priority field has been removed, so return default color
            return "#666666"; // Default - gray
        }

        /// <summary>
        /// Generate redirect URL based on entity type
        /// </summary>
        private string? GenerateRedirectUrl(SystemNotificationRule rule, string entityId)
        {
            var entityType = "FactGmvMaxCampaignEntity"; // Always Campaign level
            
            return entityType switch
            {
                "FactGmvMaxCampaignEntity" => $"/Campaigns/Details/{entityId}",
                "FactGmvMaxProductEntity" => $"/Products/Details/{entityId}",
                _ => null
            };
        }

        /// <summary>
        /// Get entity ID
        /// </summary>
        private string GetEntityId<T>(T entity)
        {
            // Try to get Id property first
            var idProperty = typeof(T).GetProperty("Id");
            if (idProperty != null)
            {
                var idValue = idProperty.GetValue(entity);
                return idValue?.ToString() ?? "";
            }

            // Try to get CampaignId or ProductId
            var campaignIdProperty = typeof(T).GetProperty("CampaignId");
            if (campaignIdProperty != null)
            {
                var campaignIdValue = campaignIdProperty.GetValue(entity);
                return campaignIdValue?.ToString() ?? "";
            }

            var productIdProperty = typeof(T).GetProperty("ProductId");
            if (productIdProperty != null)
            {
                var productIdValue = productIdProperty.GetValue(entity);
                return productIdValue?.ToString() ?? "";
            }

            return "";
        }

        /// <summary>
        /// Get entity display name
        /// </summary>
        private string GetEntityDisplayName<T>(T entity)
        {
            // Try to get CampaignName or ProductName
            var campaignNameProperty = typeof(T).GetProperty("CampaignName");
            if (campaignNameProperty != null)
            {
                var campaignNameValue = campaignNameProperty.GetValue(entity);
                if (!string.IsNullOrEmpty(campaignNameValue?.ToString()))
                    return campaignNameValue.ToString() ?? string.Empty;
            }

            var productNameProperty = typeof(T).GetProperty("ProductName");
            if (productNameProperty != null)
            {
                var productNameValue = productNameProperty.GetValue(entity);
                if (!string.IsNullOrEmpty(productNameValue?.ToString()))
                    return productNameValue.ToString() ?? string.Empty;
            }

            // Fallback to entity type
            return typeof(T).Name;
        }

        /// <summary>
        /// Get entity type display name
        /// </summary>
        private string GetEntityTypeDisplayName(string entityType)
        {
            return entityType switch
            {
                "RawGmvMaxProductCreativeReportEntity" => "GMV Max Product Creative",
                "RawGmvMaxProductCampaignReportEntity" => "GMV Max Product Campaign",
                "RawGmvMaxLiveCampaignReportEntity" => "GMV Max Live Campaign",
                "Campaign" => "Campaign",
                "Product" => "Product",
                _ => entityType
            };
        }
    }
}
