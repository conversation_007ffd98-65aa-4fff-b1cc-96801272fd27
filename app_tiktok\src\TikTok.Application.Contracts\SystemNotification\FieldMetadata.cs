using System;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Metadata cho field discovery từ C# entities
    /// Đ<PERSON><PERSON>c sử dụng bởi FieldDefinitionService để cung cấp thông tin về các fields có thể sử dụng trong rules
    /// </summary>
    public record FieldMetadata
    {
        /// <summary>
        /// Tên property trong C# entity (ví dụ: "ROAS", "Cost", "Orders")
        /// </summary>
        public string Name { get; init; }

        /// <summary>
        /// Tên hiển thị cho user (ví dụ: "ROAS", "Cost", "Orders")
        /// </summary>
        public string DisplayName { get; init; }

        /// <summary>
        /// Loại dữ liệu: "String", "Number", "Decimal", "Boolean", "DateTime", "Enum"
        /// </summary>
        public string DataType { get; init; }

        /// <summary>
        /// Category để group fields: "Performance", "Basic Info", "Budget", "Creative", "Schedule", "Other"
        /// </summary>
        public string Category { get; init; }

        /// <summary>
        /// Mô tả chi tiết về field
        /// </summary>
        public string? Description { get; init; }

        /// <summary>
        /// Các giá trị được phép cho field (dành cho enum hoặc string fields có giới hạn)
        /// </summary>
        public string[]? AllowedValues { get; init; }




        public FieldMetadata(
            string name,
            string displayName,
            string dataType,
            string category,
            string? description = null,
            string[]? allowedValues = null)
        {
            Name = name;
            DisplayName = displayName;
            DataType = dataType;
            Category = category;
            Description = description;
            AllowedValues = allowedValues;
        }
    }
}
