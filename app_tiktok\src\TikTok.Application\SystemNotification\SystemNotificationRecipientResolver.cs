using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Domain.Repositories;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using TikTok.Permissions;
using Volo.Abp.Authorization.Permissions;
using TikTok.Entities;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Identity;

namespace TikTok.Application.SystemNotification
{
    public interface ISystemNotificationRecipientResolver : ITransientDependency
    {
        Task<List<string>> ResolveRecipientsForRuleAsync(SystemNotificationRule rule);
        Task<List<string>> FilterByNotificationPermissionAsync(List<string> recipientIds);
    }

    /// <summary>
    /// Resolve danh sách người nhận cho SystemNotificationRule dựa trên consumers (BC/AdAccount).
    /// Tối giản: hiện fallback về Creator nếu chưa có ánh xạ consumer -> user.
    /// </summary>
    public class SystemNotificationRecipientResolver : ISystemNotificationRecipientResolver
    {
        private readonly ISystemNotificationRuleConsumerRepository _consumerRepository;
        private readonly IRepository<RawAdAccountEntity, Guid> _rawAdAccountRepository;
        private readonly IAdAccountResourceProvider _adAccountResourceProvider;
        private readonly ILogger<SystemNotificationRecipientResolver> _logger;
        private readonly IPermissionGrantRepository _permissionGrantRepository;
            private readonly IIdentityUserRepository _identityUserRepository;

        public SystemNotificationRecipientResolver(
            ISystemNotificationRuleConsumerRepository consumerRepository,
            IRepository<RawAdAccountEntity, Guid> rawAdAccountRepository,
            IAdAccountResourceProvider adAccountResourceProvider,
            ILogger<SystemNotificationRecipientResolver> logger,
            IPermissionGrantRepository permissionGrantRepository,
            IIdentityUserRepository identityUserRepository)
        {
            _consumerRepository = consumerRepository;
            _rawAdAccountRepository = rawAdAccountRepository;
            _adAccountResourceProvider = adAccountResourceProvider;
            _logger = logger;
            _permissionGrantRepository = permissionGrantRepository;
            _identityUserRepository = identityUserRepository;
        }

        public async Task<List<string>> ResolveRecipientsForRuleAsync(SystemNotificationRule rule)
        {
            var recipients = new List<string>();
            try
            {
                // Lấy consumers theo rule (BC/AdAccount)
                var consumers = await _consumerRepository.GetConsumersByRuleIdAsync(rule.Id);

                // 1) Ánh xạ AdAccount consumers -> advertiserIds
                var adAccountIds = consumers
                    .Where(c => c.AdAccountId.HasValue)
                    .Select(c => c.AdAccountId!.Value)
                    .Distinct()
                    .ToList();

                if (adAccountIds.Any())
                {
                    var rawAdAccounts = await _rawAdAccountRepository.GetListAsync(x => adAccountIds.Contains(x.Id));
                    var advertiserIds = rawAdAccounts
                        .Select(a => a.AdvertiserId)
                        .Where(id => !string.IsNullOrWhiteSpace(id))
                        .Distinct()
                        .ToList();

                    if (advertiserIds.Any())
                    {
                        var assignPerms = await _adAccountResourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto
                        {
                            ResourceIds = advertiserIds,
                            Permissions = new List<string>
                            {
                                TikTokPermissions.AdAccounts.Default,
                                TikTokPermissions.AdAccounts.Edit
                            }
                        });

                        var userIds = assignPerms
                            .Select(p => p.UserId.ToString())
                            .Where(s => !string.IsNullOrWhiteSpace(s))
                            .Distinct();

                        recipients.AddRange(userIds);
                    }
                }

                // 2) Ánh xạ BusinessCenter consumers -> userIds thông qua ad accounts thuộc BC
                var bcIds = consumers
                    .Where(c => c.BcId.HasValue)
                    .Select(c => c.BcId!.Value)
                    .Distinct()
                    .ToList();

                if (bcIds.Any())
                {
                    // Lấy tất cả ad accounts thuộc các BC này
                    var bcIdStrings = bcIds.Select(id => id.ToString()).ToList();
                    var bcAdAccounts = await _rawAdAccountRepository.GetListAsync(x => !string.IsNullOrEmpty(x.OwnerBcId) && bcIdStrings.Contains(x.OwnerBcId));
                    var bcAdvertiserIds = bcAdAccounts
                        .Select(a => a.AdvertiserId)
                        .Where(id => !string.IsNullOrWhiteSpace(id))
                        .Distinct()
                        .ToList();

                    if (bcAdvertiserIds.Any())
                    {
                        var assignPermsForBc = await _adAccountResourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto
                        {
                            ResourceIds = bcAdvertiserIds,
                            Permissions = new List<string>
                            {
                                TikTokPermissions.AdAccounts.Default,
                                TikTokPermissions.AdAccounts.Edit
                            }
                        });

                        var userIdsForBc = assignPermsForBc
                            .Select(p => p.UserId.ToString())
                            .Where(s => !string.IsNullOrWhiteSpace(s))
                            .Distinct();

                        recipients.AddRange(userIdsForBc);
                    }
                }

                if (!recipients.Any() && rule.CreatorId.HasValue)
                {
                    recipients.Add(rule.CreatorId.Value.ToString());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving recipients for rule {RuleId}", rule.Id);
                if (!recipients.Any() && rule.CreatorId.HasValue)
                {
                    recipients.Add(rule.CreatorId.Value.ToString());
                }
            }

            // Lọc theo quyền nhận thông báo nếu có cấu hình
            var distinct = recipients.Distinct().ToList();
            var filtered = new List<string>();
            foreach (var id in distinct)
            {
                if (!Guid.TryParse(id, out var userGuid))
                {
                    continue;
                }

                // 1) Direct user grant
                var userGrant = await _permissionGrantRepository.FindAsync(
                    TikTokPermissions.ReceiveNotification.Default,
                    UserPermissionValueProvider.ProviderName,
                    userGuid.ToString());

                if (userGrant != null)
                {
                    filtered.Add(id);
                    continue;
                }

                // 2) Role-based grants: need to check each role NAME as provider key
                var roles = await _identityUserRepository.GetRolesAsync(userGuid);
                foreach (var role in roles)
                {
                    var roleGrant = await _permissionGrantRepository.FindAsync(
                        TikTokPermissions.ReceiveNotification.Default,
                        RolePermissionValueProvider.ProviderName,
                        role.Name);
                    if (roleGrant != null)
                    {
                        filtered.Add(id);
                        break;
                    }
                }
            }

            // Fallback: nếu sau lọc rỗng, thử thêm creator nếu có và có quyền
            if (!filtered.Any() && rule.CreatorId.HasValue)
            {
                var creatorId = rule.CreatorId.Value;
                var creatorStr = creatorId.ToString();

                var creatorUserGrant = await _permissionGrantRepository.FindAsync(
                    TikTokPermissions.ReceiveNotification.Default,
                    UserPermissionValueProvider.ProviderName,
                    creatorStr);

                if (creatorUserGrant != null)
                {
                    filtered.Add(creatorStr);
                }
                else
                {
                    var creatorRoles = await _identityUserRepository.GetRolesAsync(creatorId);
                    foreach (var role in creatorRoles)
                    {
                        var creatorRoleGrant = await _permissionGrantRepository.FindAsync(
                            TikTokPermissions.ReceiveNotification.Default,
                            RolePermissionValueProvider.ProviderName,
                            role.Name);
                        if (creatorRoleGrant != null)
                        {
                            filtered.Add(creatorStr);
                            break;
                        }
                    }
                }
            }

            return filtered;
        }

        /// <summary>
        /// Public method to filter recipients by ReceiveNotification permission
        /// Can be used by other services (e.g., for default rules)
        /// </summary>
        public async Task<List<string>> FilterByNotificationPermissionAsync(List<string> recipientIds)
        {
            var distinct = recipientIds.Distinct().ToList();
            var filtered = new List<string>();
            
            foreach (var id in distinct)
            {
                if (!Guid.TryParse(id, out var userGuid))
                {
                    continue;
                }

                // Check direct user grant
                var userGrant = await _permissionGrantRepository.FindAsync(
                    TikTokPermissions.ReceiveNotification.Default,
                    UserPermissionValueProvider.ProviderName,
                    userGuid.ToString());

                if (userGrant != null)
                {
                    filtered.Add(id);
                    continue;
                }

                // Check role-based grants
                var roles = await _identityUserRepository.GetRolesAsync(userGuid);
                foreach (var role in roles)
                {
                    var roleGrant = await _permissionGrantRepository.FindAsync(
                        TikTokPermissions.ReceiveNotification.Default,
                        RolePermissionValueProvider.ProviderName,
                        role.Name);
                    
                    if (roleGrant != null)
                    {
                        filtered.Add(id);
                        break;
                    }
                }
            }

            _logger.LogDebug("Filtered {FilteredCount} recipients from {TotalCount} based on ReceiveNotification permission", 
                filtered.Count, recipientIds.Count);

            return filtered;
        }
    }
}


