(function () {
    'use strict';

    const L10N = window.notificationsL10n || {};
    const BATCH_SIZE = 20;
    let notifications = [];
    let renderedCount = 0;
    let observer = null;

    function normalizeI18nLabel(label, fallback) {
        const text = (label && String(label).trim()) || fallback;
        if (!text) return '';
        if (text.indexOf(' ') >= 0) return text;
        // Insert spaces before capital letters (e.g., JustNow -> Just Now)
        return text.replace(/([a-z])([A-Z])/g, '$1 $2');
    }

    async function loadAllNotifications() {
        try {
            const currentUserId =
                (window.abp && abp.currentUser && abp.currentUser.id) || null;
            if (!currentUserId) {
                console.warn(
                    '[Notifications] No currentUserId; skip loadAllNotifications'
                );
                return;
            }
            const res = await fetch(
                `/api/Notifications/GetNotificationsUnReadByUser?userId=${currentUserId}`
            );
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            notifications = await res.json();
            renderInitial();
        } catch (e) {
            console.error('[Notifications] Failed to load notifications', e);
            notifications = [];
            renderInitial();
        }
    }

    function renderInitial() {
        const container = document.getElementById('notificationsContainer');
        const sentinel = document.getElementById('lazySentinel');
        if (!container || !sentinel) return;

        container.innerHTML = '';
        renderedCount = 0;

        if (notifications.length === 0) {
            const emptyText = normalizeI18nLabel(
                L10N.NoNewNotifications,
                'No notifications'
            );
            container.innerHTML = `<div class="text-center text-muted py-4">\n                <i class=\"fa-solid fa-bell-slash fa-2x mb-2\"></i>\n                <p class=\"mb-0\">${emptyText}</p>\n            </div>`;
            sentinel.style.display = 'none';
            return;
        }

        sentinel.style.display = 'block';
        appendNextBatch();
        setupObserver();
    }

    function appendNextBatch() {
        const container = document.getElementById('notificationsContainer');
        const next = notifications.slice(
            renderedCount,
            renderedCount + BATCH_SIZE
        );
        if (next.length === 0) return;
        const html = next.map((n) => createItemHTML(n)).join('');
        container.insertAdjacentHTML('beforeend', html);
        renderedCount += next.length;

        container.querySelectorAll('.notification-item').forEach((item) => {
            if (item.dataset.bound === '1') return;
            item.dataset.bound = '1';
            item.addEventListener('click', async () => {
                const id = item.dataset.id;
                const url = item.dataset.url;
                await markAsRead(id);
                if (url && url !== '#') window.location.href = url;
            });
        });
    }

    function setupObserver() {
        const sentinel = document.getElementById('lazySentinel');
        if (!sentinel) return;
        if (observer) observer.disconnect();
        observer = new IntersectionObserver((entries) => {
            for (const entry of entries) {
                if (entry.isIntersecting) {
                    appendNextBatch();
                    if (renderedCount >= notifications.length) {
                        sentinel.style.display = 'none';
                        observer.disconnect();
                    }
                }
            }
        });
        observer.observe(sentinel);
    }

    // Simple HTML escaper for text nodes
    function escapeHtml(value) {
        return String(value)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    function createItemHTML(n) {
        const isUnread = !n.isRead;
        const unreadClass = isUnread ? 'unread' : '';
        const iconClass = getIconByContextOrType(n.context || n.type);
        const timeText = formatDisplayTime(n);
        const url = getUrlForNotification(n);

        const timeHtml = timeText
            ? `<span class="notification-time">${timeText}</span>`
            : '';
        const dotHtml = isUnread
            ? '<div class="notification-dot bg-primary rounded-circle"></div>'
            : '';

        const contentHtml = escapeHtml(n.content || '');

        return [
            `<div class="notification-item p-3 border-bottom ${unreadClass}" data-id="${n.id}" data-url="${url}">`,
            '  <div class="d-flex align-items-start">',
            '    <div class="notification-icon me-3">',
            `      <i class="${iconClass}"></i>`,
            '    </div>',
            '    <div class="flex-grow-1">',
            `      <p class="mb-1 small notification-content">${contentHtml}</p>`,
            `      ${timeHtml}`,
            '    </div>',
            `    ${dotHtml}`,
            '  </div>',
            '</div>',
        ].join('\n');
    }

    // Build deep-link using payload.EntityType when server doesn't provide URL
    function getUrlForNotification(n) {
        if (n.url && n.url !== '#') return n.url;

        // Try parse payload
        let payload = null;
        try {
            payload =
                typeof n.payload === 'string'
                    ? JSON.parse(n.payload)
                    : n.payload || {};
        } catch (_) {
            payload = null;
        }

        const entityType =
            payload?.EntityType || payload?.Metadata?.EntityType || '';
        const campaignId =
            payload?.CampaignId ||
            payload?.campaignId ||
            payload?.ObjectId ||
            payload?.Metadata?.CampaignId ||
            '';

        // Map entity types to GmvMax tabs
        if (
            entityType === 'RawGmvMaxProductCampaignReportEntity' ||
            entityType === 'RawGmvMaxLiveCampaignReportEntity'
        ) {
            return campaignId
                ? `/GmvMax?tab=campaign&campaignId=${encodeURIComponent(
                      campaignId
                  )}`
                : '/GmvMax?tab=campaign';
        }

        if (entityType === 'RawGmvMaxProductCreativeReportEntity') {
            return campaignId
                ? `/GmvMax?tab=video&campaignId=${encodeURIComponent(
                      campaignId
                  )}`
                : '/GmvMax?tab=video';
        }

        // Legacy context mapping fallback
        const context = (n.context || n.type || '').toString();
        if (context === 'GmvMaxCreativeStatusChange') {
            return campaignId
                ? `/GmvMax?tab=video&campaignId=${encodeURIComponent(
                      campaignId
                  )}`
                : '/GmvMax?tab=video';
        }
        return '#';
    }

    function formatDisplayTime(n) {
        try {
            const raw = n.created;
            if (!raw) return '';
            const date = new Date(raw);
            if (isNaN(date.getTime())) return '';
            const now = new Date();
            const diffMs = now - date;
            const diffSec = Math.floor(diffMs / 1000);
            const diffMin = Math.floor(diffSec / 60);
            const diffHr = Math.floor(diffMin / 60);
            const diffDay = Math.floor(diffHr / 24);
            if (diffSec < 60)
                return normalizeI18nLabel(L10N.JustNow, 'Just now');
            if (diffMin < 60)
                return `${diffMin} ${normalizeI18nLabel(
                    L10N.MinutesAgo,
                    'minutes ago'
                )}`;
            if (diffHr < 24)
                return `${diffHr} ${normalizeI18nLabel(
                    L10N.HoursAgo,
                    'hours ago'
                )}`;
            if (diffDay < 7)
                return `${diffDay} ${normalizeI18nLabel(
                    L10N.DaysAgo,
                    'days ago'
                )}`;
            return date.toLocaleString();
        } catch {
            return '';
        }
    }

    function getIconByContextOrType(value) {
        const map = {
            GmvMaxCreativeStatusChange:
                'fa-solid fa-wand-magic-sparkles text-primary',
            success: 'fa-solid fa-circle-check text-success',
            warning: 'fa-solid fa-triangle-exclamation text-warning',
            error: 'fa-solid fa-circle-xmark text-danger',
            info: 'fa-solid fa-circle-info text-info',
        };
        return map[value] || 'fa-solid fa-bell text-secondary';
    }

    async function markAsRead(id) {
        try {
            const res = await fetch(
                `/api/Notifications/SetReadNotification?id=${id}`,
                {
                    method: 'PATCH',
                    headers: { 'Content-Type': 'application/json' },
                }
            );
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            // update local state for the item
            notifications = notifications.map((n) =>
                n.id === id ? { ...n, isRead: true } : n
            );
        } catch (e) {
            console.warn('[Notifications] markAsRead failed', e);
        }
    }

    async function performMarkAllRead(btnRef) {
        try {
            const currentUserId =
                (window.abp && abp.currentUser && abp.currentUser.id) || null;
            if (!currentUserId) {
                console.warn(
                    '[Notifications] No currentUserId; aborting markAllRead'
                );
                return;
            }

            let btn = btnRef || document.getElementById('markAllRead');
            let originalText;
            if (btn) {
                if (!btn.dataset.originalText) {
                    btn.dataset.originalText = btn.innerHTML;
                }
                originalText = btn.dataset.originalText;
                btn.disabled = true;
                btn.classList.add('disabled');
                btn.innerHTML =
                    '<span class="spinner-border spinner-border-sm me-1"></span>' +
                    originalText;
            }

            const res = await fetch(
                `/api/Notifications/MarkAllAsRead?userId=${currentUserId}`,
                {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                }
            );
            if (!res.ok) throw new Error(`HTTP ${res.status}`);

            // Optimistically mark all as read in UI
            notifications = notifications.map((n) => ({ ...n, isRead: true }));
            document
                .querySelectorAll('.notification-item.unread')
                .forEach((el) => el.classList.remove('unread'));
            document
                .querySelectorAll('.notification-dot')
                .forEach((el) => el.remove());

            // Reload from server to sync
            await loadAllNotifications();
        } catch (e) {
            console.warn('[Notifications] performMarkAllRead failed', e);
        } finally {
            const btn = btnRef || document.getElementById('markAllRead');
            if (btn && btn.dataset.originalText) {
                btn.disabled = false;
                btn.classList.remove('disabled');
                btn.innerHTML = btn.dataset.originalText;
            }
        }
    }

    function bindMarkAllRead(attempt = 1) {
        const btn = document.getElementById('markAllRead');
        if (!btn) {
            if (attempt <= 3) {
                console.warn(
                    `[Notifications] markAllRead button not found. Retrying (${attempt})...`
                );
                setTimeout(() => bindMarkAllRead(attempt + 1), 200);
            } else {
                console.warn(
                    '[Notifications] markAllRead button not found after retries'
                );
            }
            return;
        }
        if (btn.dataset.bound === '1') return;
        btn.dataset.bound = '1';
        console.log('[Notifications] markAllRead bound');
        btn.addEventListener('click', () => performMarkAllRead(btn));
    }

    // Expose for direct call from Razor if needed
    window.NotificationsPage = window.NotificationsPage || {};
    window.NotificationsPage.markAllRead = function markAllRead() {
        performMarkAllRead();
    };

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            bindMarkAllRead();
            loadAllNotifications();
        });
    } else {
        bindMarkAllRead();
        loadAllNotifications();
    }
})();
