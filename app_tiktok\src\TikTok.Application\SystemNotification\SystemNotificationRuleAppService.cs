using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Domain.Repositories;
using TikTok.Application.Contracts.SystemNotification;
using System.Text.Json;
using Volo.Abp;

namespace TikTok.Application.SystemNotification
{
    /// <summary>
    /// Application service cho System Notification Rule management
    /// Cung cấp CRUD operations và business logic cho system notification rules
    /// </summary>
    public class SystemNotificationRuleAppService : ApplicationService, ISystemNotificationRuleAppService
    {
        private readonly IRepository<SystemNotificationRule, Guid> _ruleRepository;
        private readonly ISystemNotificationRuleRepository _systemRuleRepository;
        private readonly IIdentityUserRepository _userRepository;
        private readonly SystemNotificationFieldService _fieldService;
        private readonly SystemNotificationTriggerService _triggerService;

        public SystemNotificationRuleAppService(
            IRepository<SystemNotificationRule, Guid> ruleRepository,
            ISystemNotificationRuleRepository systemRuleRepository,
            IIdentityUserRepository userRepository,
            SystemNotificationFieldService fieldService,
            SystemNotificationTriggerService triggerService)
        {
            _ruleRepository = ruleRepository;
            _systemRuleRepository = systemRuleRepository;
            _userRepository = userRepository;
            _fieldService = fieldService;
            _triggerService = triggerService;
        }


        /// <summary>
        /// Get all system notification rules với pagination
        /// </summary>
        public async Task<SystemNotificationRuleListDto> GetListAsync(SystemNotificationRuleSearchDto input)
        {
            var searchCriteria = new SystemNotificationRuleSearchCriteria
            {
                EntityType = input.EntityType,
                IsActive = input.IsActive,
                // REMOVED: HasCustomTemplate - system auto-generates notification content
                CreatedAfter = input.CreatedAfter,
                CreatedBefore = input.CreatedBefore,
                CreatorId = input.CreatorId,
                SearchText = input.SearchText,
                IsDefault = input.IsDefault,
                IsPublic = input.IsPublic,
                SkipCount = input.SkipCount,
                MaxResultCount = input.MaxResultCount
            };

            var rules = await _systemRuleRepository.SearchRulesAsync(searchCriteria);
            var totalCount = await _systemRuleRepository.GetCountAsync(searchCriteria);

            var ruleDtos = new List<SystemNotificationRuleDto>();
            foreach (var rule in rules)
            {
                ruleDtos.Add(await MapToDtoAsync(rule));
            }

            return new SystemNotificationRuleListDto
            {
                Items = ruleDtos,
                TotalCount = totalCount,
                SkipCount = input.SkipCount,
                MaxResultCount = input.MaxResultCount,
                HasMore = input.SkipCount + input.MaxResultCount < totalCount
            };
        }

        /// <summary>
        /// Get system notification rule by ID
        /// </summary>
        public async Task<SystemNotificationRuleDto> GetAsync(Guid id)
        {
            var rule = await _ruleRepository.GetAsync(id);
            return await MapToDtoAsync(rule);
        }

        /// <summary>
        /// Create new system notification rule
        /// </summary>
        public async Task<SystemNotificationRuleDto> CreateAsync(CreateSystemNotificationRuleDto input)
        {
            // Validate input
            var validation = await ValidateRuleAsync(input);
            if (!validation.IsValid)
            {
                throw new UserFriendlyException($"Validation failed: {string.Join(", ", validation.Errors)}");
            }

            // Create rule entity using AutoMapper
            var rule = ObjectMapper.Map<CreateSystemNotificationRuleDto, SystemNotificationRule>(input);

            // Save to database
            await _ruleRepository.InsertAsync(rule);

            Logger.LogInformation("System notification rule created: {RuleName} by user {UserId}", 
                rule.RuleName, CurrentUser.Id);

            return await MapToDtoAsync(rule);
        }

        /// <summary>
        /// Update existing system notification rule
        /// </summary>
        public async Task<SystemNotificationRuleDto> UpdateAsync(Guid id, UpdateSystemNotificationRuleDto input)
        {
            // Get existing rule
            var rule = await _ruleRepository.GetAsync(id);

            // Validate input
            var validation = await ValidateRuleAsync(input);
            if (!validation.IsValid)
            {
                throw new UserFriendlyException($"Validation failed: {string.Join(", ", validation.Errors)}");
            }

            // Update rule properties using AutoMapper
            ObjectMapper.Map(input, rule);

            // Save to database
            await _ruleRepository.UpdateAsync(rule);

            Logger.LogInformation("System notification rule updated: {RuleName} by user {UserId}", 
                rule.RuleName, CurrentUser.Id);

            return await MapToDtoAsync(rule);
        }

        /// <summary>
        /// Delete system notification rule
        /// </summary>
        public async Task DeleteAsync(Guid id)
        {
            var rule = await _ruleRepository.GetAsync(id);
            await _ruleRepository.DeleteAsync(rule);

            Logger.LogInformation("System notification rule deleted: {RuleName} by user {UserId}", 
                rule.RuleName, CurrentUser.Id);
        }

        /// <summary>
        /// Toggle rule active status
        /// </summary>
        public async Task<SystemNotificationRuleDto> ToggleActiveAsync(Guid id)
        {
            var rule = await _ruleRepository.GetAsync(id);
            rule.IsActive = !rule.IsActive;
            await _ruleRepository.UpdateAsync(rule);

            Logger.LogInformation("System notification rule {Action}: {RuleName} by user {UserId}", 
                rule.IsActive ? "activated" : "deactivated", rule.RuleName, CurrentUser.Id);

            return await MapToDtoAsync(rule);
        }

        /// <summary>
        /// Get system notification rule statistics
        /// </summary>
        public async Task<SystemNotificationRuleStatsDto> GetStatisticsAsync()
        {
            var stats = await _systemRuleRepository.GetRuleStatisticsAsync();
            
            return new SystemNotificationRuleStatsDto
            {
                TotalRules = stats.TotalRules,
                ActiveRules = stats.ActiveRules,
                InactiveRules = stats.InactiveRules,
                StandardRules = stats.StandardRules,
                // REMOVED: RulesWithCustomTemplates - system auto-generates notification content
                TotalTriggerCount = stats.TotalTriggerCount,
                LastTriggerTime = stats.LastTriggerTime,
                OldestRuleCreated = stats.OldestRuleCreated,
                NewestRuleCreated = stats.NewestRuleCreated,
                RulesByEntityTypeBreakdown = stats.RulesByEntityTypeBreakdown,
                RulesByRecipientTypeBreakdown = stats.RulesByRecipientTypeBreakdown,
                RulesByOperatorBreakdown = stats.RulesByOperatorBreakdown,
                RulesByFieldBreakdown = stats.RulesByFieldBreakdown
            };
        }

        /// <summary>
        /// Validate system notification rule
        /// </summary>
        public async Task<SystemNotificationRuleValidationDto> ValidateRuleAsync(object input)
        {
            var validation = new SystemNotificationRuleValidationDto { IsValid = true };

            try
            {
                string entityType = "";
                bool isDefault = false;
                
                if (input is CreateSystemNotificationRuleDto createDto)
                {
                    entityType = createDto.EntityType;
                    isDefault = createDto.IsDefault;
                }
                else if (input is UpdateSystemNotificationRuleDto updateDto)
                {
                    entityType = updateDto.EntityType;
                    isDefault = updateDto.IsDefault;
                }

                if (isDefault)
                {
                    validation.Warnings.Add("⚠️ Default rule will be automatically applied to all entities of this type without requiring specific consumers.");
                    validation.Warnings.Add("📢 Notifications will be sent to all users with permissions to ad accounts.");
                    Logger.LogInformation("Validating default rule for entity type {EntityType}", entityType);
                }

                // If using ConditionsJson, validate rule tree instead of single field
                if (!string.IsNullOrWhiteSpace((input as CreateSystemNotificationRuleDto)?.ConditionsJson
                    ?? (input as UpdateSystemNotificationRuleDto)?.ConditionsJson))
                {
                    var condJson = (input as CreateSystemNotificationRuleDto)?.ConditionsJson
                        ?? (input as UpdateSystemNotificationRuleDto)?.ConditionsJson;
                    await ValidateConditionsJsonAsync(entityType, condJson!, validation);
                }
                else
                {
                    validation.IsValid = false;
                    validation.Errors.Add("ConditionsJson is required");
                }
            }
            catch (Exception ex)
            {
                validation.IsValid = false;
                validation.Errors.Add($"Validation error: {ex.Message}");
                Logger.LogError(ex, "Error validating system notification rule");
            }

            return validation;
        }

        /// <summary>
        /// Get fields for entity type
        /// </summary>
        public async Task<List<FieldMetadata>> GetFieldsForEntityTypeAsync(string entityType)
        {
            return await _fieldService.GetFieldsForEntityTypeAsync(entityType);
        }


        /// <summary>
        /// Trigger rule check manually for testing
        /// </summary>
        public async Task TriggerRuleCheckAsync(Guid id)
        {
            var rule = await _ruleRepository.GetAsync(id);
            var sampleEntities = await GetSampleEntitiesAsync(rule.EntityType);
            
            if (sampleEntities.Any())
            {
                await _triggerService.TriggerRuleCheck(sampleEntities, rule.EntityType);
                Logger.LogInformation("Manual rule check triggered for rule: {RuleName}", rule.RuleName);
            }
        }

        #region Private Methods

        /// <summary>
        /// Map SystemNotificationRule entity to DTO
        /// </summary>
        private async Task<SystemNotificationRuleDto> MapToDtoAsync(SystemNotificationRule rule)
        {
            var dto = ObjectMapper.Map<SystemNotificationRule, SystemNotificationRuleDto>(rule);


            // Recipient management is now handled via SystemNotificationRuleConsumerEntity

            // Get user names
            if (rule.CreatorId.HasValue)
            {
                var creator = await _userRepository.FindAsync(rule.CreatorId.Value);
                dto.CreatorName = creator?.UserName;
            }

            if (rule.LastModifierId.HasValue)
            {
                var modifier = await _userRepository.FindAsync(rule.LastModifierId.Value);
                dto.LastModifierName = modifier?.UserName;
            }

            // Get display names
            dto.EntityTypeDisplayName = GetEntityTypeDisplayName(rule.EntityType);

            return dto;
        }

        /// <summary>
        /// Validate field value based on data type and operator
        /// </summary>
        private bool ValidateFieldValue(string dataType, string value, string operatorType)
        {
            if (string.IsNullOrEmpty(value))
                return operatorType == "IsEmpty";

            switch (dataType.ToLower())
            {
                case "int":
                case "long":
                    return operatorType == "In" || int.TryParse(value.Split(',')[0].Trim(), out _);
                case "decimal":
                case "double":
                case "float":
                    return operatorType == "In" || operatorType == "Between" || decimal.TryParse(value.Split(',')[0].Trim(), out _);
                case "datetime":
                    return operatorType == "In" || DateTime.TryParse(value, out _);
                case "bool":
                    return bool.TryParse(value, out _);
                case "string":
                default:
                    return true; // String values are always valid
            }
        }

        private async Task ValidateConditionsJsonAsync(string entityType, string conditionsJson, SystemNotificationRuleValidationDto validation)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(conditionsJson))
                {
                    validation.IsValid = false;
                    validation.Errors.Add("ConditionsJson is empty");
                    return;
                }

                using var doc = System.Text.Json.JsonDocument.Parse(conditionsJson);
                await ValidateRuleNodeAsync(entityType, doc.RootElement, validation);
            }
            catch (Exception ex)
            {
                validation.IsValid = false;
                validation.Errors.Add($"Invalid ConditionsJson: {ex.Message}");
            }
        }

        private async Task ValidateRuleNodeAsync(string entityType, System.Text.Json.JsonElement node, SystemNotificationRuleValidationDto validation)
        {
            if (node.TryGetProperty("rules", out var rulesProp))
            {
                foreach (var child in rulesProp.EnumerateArray())
                {
                    await ValidateRuleNodeAsync(entityType, child, validation);
                    if (!validation.IsValid) return;
                }
                return;
            }

            var field = node.TryGetProperty("field", out var f) ? f.GetString() ?? string.Empty : string.Empty;
            var op = node.TryGetProperty("operator", out var o) ? o.GetString() ?? string.Empty : string.Empty;
            var fields = await _fieldService.GetFieldsForEntityTypeAsync(entityType);
            var meta = fields.FirstOrDefault(x => x.Name == field);
            if (meta == null)
            {
                validation.IsValid = false;
                validation.Errors.Add($"Field '{field}' is not available for entity type '{entityType}'");
                return;
            }
            // Value validation by data type and allowed values
            if (!node.TryGetProperty("value", out var valueProp))
            {
                validation.IsValid = false;
                validation.Errors.Add($"Value is required for field '{field}'");
                return;
            }

            var dataType = (meta.DataType ?? "string").ToLowerInvariant();
            var opKey = (op ?? string.Empty).Trim().ToLowerInvariant().Replace(" ", string.Empty);

            bool IsBetween(string key) => key == "between" || key == "notbetween";

            try
            {
                if (dataType == "number" || dataType == "decimal")
                {
                    if (IsBetween(opKey))
                    {
                        if (valueProp.ValueKind != JsonValueKind.Array || valueProp.GetArrayLength() != 2)
                        {
                            validation.IsValid = false;
                            validation.Errors.Add($"Between requires two numeric values for '{field}'");
                            return;
                        }
                        foreach (var v in valueProp.EnumerateArray())
                        {
                            if (!decimal.TryParse(v.ToString(), out _))
                            {
                                validation.IsValid = false;
                                validation.Errors.Add($"Between values must be numeric for '{field}'");
                                return;
                            }
                        }
                    }
                    else
                    {
                        if (!decimal.TryParse(valueProp.ToString(), out _))
                        {
                            validation.IsValid = false;
                            validation.Errors.Add($"Value for '{field}' must be numeric");
                            return;
                        }
                    }
                }
                else if (dataType == "boolean")
                {
                    var s = valueProp.ToString().Trim().ToLowerInvariant();
                    if (!(s == "true" || s == "false"))
                    {
                        validation.IsValid = false;
                        validation.Errors.Add($"Value for '{field}' must be boolean");
                        return;
                    }
                }
                else if (dataType == "datetime" || dataType == "date")
                {
                    if (IsBetween(opKey))
                    {
                        if (valueProp.ValueKind != JsonValueKind.Array || valueProp.GetArrayLength() != 2)
                        {
                            validation.IsValid = false;
                            validation.Errors.Add($"Between requires two dates for '{field}'");
                            return;
                        }
                        foreach (var v in valueProp.EnumerateArray())
                        {
                            if (!DateTime.TryParse(v.ToString(), out _))
                            {
                                validation.IsValid = false;
                                validation.Errors.Add($"Between values must be valid dates for '{field}'");
                                return;
                            }
                        }
                    }
                    else
                    {
                        if (!DateTime.TryParse(valueProp.ToString(), out _))
                        {
                            validation.IsValid = false;
                            validation.Errors.Add($"Value for '{field}' must be a valid date");
                            return;
                        }
                    }
                }
                else if (dataType == "string")
                {
                    if (meta.AllowedValues != null && meta.AllowedValues.Length > 0)
                    {
                        var s = valueProp.ToString();
                        if (!meta.AllowedValues.Contains(s))
                        {
                            validation.IsValid = false;
                            validation.Errors.Add($"Value for '{field}' must be one of allowed values");
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                validation.IsValid = false;
                validation.Errors.Add($"Value validation error for '{field}': {ex.Message}");
                return;
            }
        }

        /// <summary>
        /// Get sample entities for testing
        /// </summary>
        private async Task<List<object>> GetSampleEntitiesAsync(string entityType)
        {
            // For now, return empty list
            // In a real scenario, you would query actual data from the database based on entityType
            // This would require access to different repositories for different entity types
            return await Task.FromResult(new List<object>());
        }


        /// <summary>
        /// Get entity type display name
        /// </summary>
        private string GetEntityTypeDisplayName(string entityType)
        {
            return entityType switch
            {
                "RawGmvMaxProductCreativeReportEntity" => "GMV Max Product Creative",
                "RawGmvMaxProductCampaignReportEntity" => "GMV Max Product Campaign",
                "RawGmvMaxLiveCampaignReportEntity" => "GMV Max Live Campaign",
                "Campaign" => "Campaign",
                "Product" => "Product",
                _ => entityType
            };
        }



        #endregion

    }
}
