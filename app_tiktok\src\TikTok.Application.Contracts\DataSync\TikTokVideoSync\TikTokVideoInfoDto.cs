using System;

namespace TikTok.Application.Contracts.DataSync.TikTokVideoSync
{
    /// <summary>
    /// DTO chứa thông tin video TikTok
    /// </summary>
    public class TikTokVideoInfoDto
    {
        /// <summary>
        /// ID của video
        /// </summary>
        public string VideoId { get; set; }

        /// <summary>
        /// URL của video
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// Tiêu đề của video
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Mô tả của video
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Tên người dùng TikTok
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Số lượt like
        /// </summary>
        public long LikeCount { get; set; }

        /// <summary>
        /// S<PERSON> lượ<PERSON> comment
        /// </summary>
        public long CommentCount { get; set; }

        /// <summary>
        /// <PERSON><PERSON> lượt share
        /// </summary>
        public long ShareCount { get; set; }

        /// <summary>
        /// S<PERSON> lượt view
        /// </summary>
        public long ViewCount { get; set; }

        /// <summary>
        /// Thời gian tạo video
        /// </summary>
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// Thời gian cập nhật cuối
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// Thời gian lấy dữ liệu
        /// </summary>
        public DateTime SyncTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Trạng thái thành công
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Thông báo lỗi
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Dữ liệu thô từ API response
        /// </summary>
        public object RawData { get; set; }

        /// <summary>
        /// ID của DimTTAccount
        /// </summary>
        public Guid? DimTTAccountId { get; set; }
    }
}
