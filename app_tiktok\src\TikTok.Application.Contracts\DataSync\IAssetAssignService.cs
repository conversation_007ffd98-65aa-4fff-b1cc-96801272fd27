using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Interface cho service cấp quyền tài sản
    /// </summary>
    public interface IAssetAssignService : IScopedDependency
    {
        /// <summary>
        /// Cấp quyền tài sản cho người dùng
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <param name="advertiserRole">Vai trò advertiser (bắt buộc khi asset_type là ADVERTISER)</param>
        /// <param name="catalogRole">Vai trò catalog (bắt buộc khi asset_type là CATALOG)</param>
        /// <param name="formLibraryRole">Vai trò form library (bắt buộc khi asset_type là LEAD)</param>
        /// <param name="ttAccountRoles">Vai trò TikTok account (bắt buộc khi asset_type là TT_ACCOUNT)</param>
        /// <param name="storeRole">Vai trò store (bắt buộc khi asset_type là TIKTOK_SHOP)</param>
        /// <returns>Kết quả cấp quyền</returns>
        Task<AssetAssignResult> AssignAssetToUserAsync(
            string bcId,
            string assetId,
            string userId = "",
            string assetType = "ADVERTISER",
            string advertiserRole = "ADMIN",
            string? catalogRole = null,
            string? formLibraryRole = null,
            List<string>? ttAccountRoles = null,
            string? storeRole = null);
    }
}
