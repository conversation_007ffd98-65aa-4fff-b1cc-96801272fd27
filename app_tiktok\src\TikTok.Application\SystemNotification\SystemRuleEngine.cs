using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Reflection;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Application.Contracts.SystemNotification;

namespace TikTok.Application.SystemNotification
{
    public class SystemRuleEngine : ISystemRuleEngine
    {
        /// <summary>
        /// Evaluate standard rules cho entities
        /// </summary>
        public async Task<List<SystemNotificationRequest>> EvaluateRules<T>(
            IEnumerable<T> entities, 
            string entityType,
            List<SystemNotificationRule> rules)
        {
            var notifications = new List<SystemNotificationRequest>();

            foreach (var rule in rules.Where(r => r.IsActive && r.EntityType == entityType))
            {
                var matchingEntities = entities.Where(entity => EvaluateRule(entity, rule)).ToList();
                
                if (matchingEntities.Any())
                {
                    foreach (var entity in matchingEntities)
                    {
                        var notification = await BuildNotification(rule, entity);
                        notifications.Add(notification);
                    }
                }
            }

            return notifications;
        }

        public Task<List<SystemNotificationRequest>> EvaluateRules<T>(
            IEnumerable<T> entities,
            SystemNotificationEntityType entityType,
            List<SystemNotificationRule> rules)
        {
            return EvaluateRules(entities, entityType.ToCode(), rules);
        }


        /// <summary>
        /// PUBLIC: Evaluate a rule for an entity (supports ConditionsJson or legacy single-field)
        /// </summary>
        public bool EvaluateRule<T>(T entity, SystemNotificationRule rule)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(rule.ConditionsJson))
                {
                    return EvaluateConditionsJson(entity, rule.ConditionsJson!);
                }
                // No legacy support: rule without ConditionsJson is considered non-match
                return false;
            }
            catch (Exception)
            {
                // Log error and skip this rule
                return false;
            }
        }

        /// <summary>
        /// Evaluate Syncfusion QueryBuilder JSON against an entity
        /// </summary>
        private bool EvaluateConditionsJson<T>(T entity, string conditionsJson)
        {
            if (string.IsNullOrWhiteSpace(conditionsJson)) return false;
            using var doc = JsonDocument.Parse(conditionsJson);
            return EvaluateRuleNode(entity, doc.RootElement);
        }

        private bool EvaluateRuleNode<T>(T entity, JsonElement node)
        {
            // Node can be a group { condition, rules: [...] } or a rule { field, operator, value }
            if (node.TryGetProperty("rules", out var rulesProp))
            {
                var condition = node.TryGetProperty("condition", out var condProp)
                    ? condProp.GetString()?.ToLowerInvariant() : "and";
                var isAnd = condition == "and";
                var result = isAnd;
                foreach (var child in rulesProp.EnumerateArray())
                {
                    var childResult = EvaluateRuleNode(entity, child);
                    if (isAnd) { result = result && childResult; if (!result) break; }
                    else { result = result || childResult; if (result) break; }
                }
                return result;
            }
            // leaf rule
            var field = node.TryGetProperty("field", out var f) ? f.GetString() ?? string.Empty : string.Empty;
            var op = node.TryGetProperty("operator", out var o) ? o.GetString() ?? string.Empty : string.Empty;
            string expected = string.Empty;
            if (node.TryGetProperty("value", out var v))
            {
                expected = v.ValueKind switch
                {
                    JsonValueKind.String => v.GetString() ?? string.Empty,
                    JsonValueKind.Number => v.ToString(),
                    JsonValueKind.Array => string.Join(',', v.EnumerateArray().Select(x => x.ToString())),
                    _ => v.ToString()
                };
            }
            var propertyValue = GetPropertyValue(entity, field);
            return EvaluateOperator(propertyValue, op, expected);
        }

        /// <summary>
        /// Evaluate operator với property value và expected value
        /// </summary>
        private bool EvaluateOperator(object? propertyValue, string operatorType, string expectedValue)
        {
            var key = (operatorType ?? string.Empty)
                .Trim()
                .ToLowerInvariant()
                .Replace(" ", string.Empty);

            if (propertyValue == null)
                return key == "isempty";

            switch (key)
            {
                case "equals":
                case "equal":
                    return propertyValue.ToString()?.Equals(expectedValue, StringComparison.OrdinalIgnoreCase) == true;

                case "contains":
                    return propertyValue.ToString()?.Contains(expectedValue, StringComparison.OrdinalIgnoreCase) == true;

                case "startswith":
                    return propertyValue.ToString()?.StartsWith(expectedValue, StringComparison.OrdinalIgnoreCase) == true;

                case "endswith":
                    return propertyValue.ToString()?.EndsWith(expectedValue, StringComparison.OrdinalIgnoreCase) == true;

                case "isempty":
                    return string.IsNullOrEmpty(propertyValue.ToString());

                case "isnotempty":
                    return !string.IsNullOrEmpty(propertyValue.ToString());

                case "greaterthan":
                    return CompareNumeric(propertyValue, expectedValue) > 0;

                case "lessthan":
                    return CompareNumeric(propertyValue, expectedValue) < 0;

                case "greaterthanorequal":
                    return CompareNumeric(propertyValue, expectedValue) >= 0;

                case "lessthanorequal":
                    return CompareNumeric(propertyValue, expectedValue) <= 0;

                case "between":
                    return EvaluateBetween(propertyValue, expectedValue);

                case "notbetween":
                    return !EvaluateBetween(propertyValue, expectedValue);

                case "in":
                    return EvaluateIn(propertyValue, expectedValue);

                case "notin":
                    return !EvaluateIn(propertyValue, expectedValue);

                case "istoday":
                    return IsToday(propertyValue);

                case "isyesterday":
                    return IsYesterday(propertyValue);

                case "isthisweek":
                    return IsThisWeek(propertyValue);

                case "isthismonth":
                    return IsThisMonth(propertyValue);

                default:
                    return false;
            }
        }

        /// <summary>
        /// Compare numeric values
        /// </summary>
        private int CompareNumeric(object? propertyValue, string expectedValue)
        {
            if (propertyValue == null || !decimal.TryParse(expectedValue, out var expected))
                return 0;

            if (propertyValue is decimal decimalValue)
                return decimal.Compare(decimalValue, expected);

            if (propertyValue is int intValue)
                return decimal.Compare(intValue, expected);

            if (propertyValue is long longValue)
                return decimal.Compare(longValue, expected);

            if (propertyValue is double doubleValue)
                return decimal.Compare((decimal)doubleValue, expected);

            if (propertyValue is float floatValue)
                return decimal.Compare((decimal)floatValue, expected);

            if (decimal.TryParse(propertyValue.ToString(), out var parsedValue))
                return decimal.Compare(parsedValue, expected);

            return 0;
        }

        /// <summary>
        /// Evaluate Between operator
        /// </summary>
        private bool EvaluateBetween(object? propertyValue, string expectedValue)
        {
            if (propertyValue == null)
                return false;

            var parts = expectedValue.Split(',');
            if (parts.Length != 2)
                return false;

            var minValue = parts[0].Trim();
            var maxValue = parts[1].Trim();

            var comparison = CompareNumeric(propertyValue, minValue);
            if (comparison < 0)
                return false;

            comparison = CompareNumeric(propertyValue, maxValue);
            return comparison <= 0;
        }

        /// <summary>
        /// Evaluate In operator
        /// </summary>
        private bool EvaluateIn(object? propertyValue, string expectedValue)
        {
            if (propertyValue == null)
                return false;

            var values = expectedValue.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(v => v.Trim())
                .ToArray();

            var propertyValueStr = propertyValue.ToString();
            return values.Contains(propertyValueStr, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Check if date is today
        /// </summary>
        private bool IsToday(object? propertyValue)
        {
            if (propertyValue is DateTime dateTime)
                return dateTime.Date == DateTime.Today;

            return false;
        }

        /// <summary>
        /// Check if date is yesterday
        /// </summary>
        private bool IsYesterday(object? propertyValue)
        {
            if (propertyValue is DateTime dateTime)
                return dateTime.Date == DateTime.Today.AddDays(-1);

            return false;
        }

        /// <summary>
        /// Check if date is this week
        /// </summary>
        private bool IsThisWeek(object? propertyValue)
        {
            if (propertyValue is DateTime dateTime)
            {
                var today = DateTime.Today;
                var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
                var endOfWeek = startOfWeek.AddDays(7);
                return dateTime.Date >= startOfWeek && dateTime.Date < endOfWeek;
            }

            return false;
        }

        /// <summary>
        /// Check if date is this month
        /// </summary>
        private bool IsThisMonth(object? propertyValue)
        {
            if (propertyValue is DateTime dateTime)
            {
                var today = DateTime.Today;
                return dateTime.Year == today.Year && dateTime.Month == today.Month;
            }

            return false;
        }

        /// <summary>
        /// Build notification cho standard rule
        /// </summary>
        private async Task<SystemNotificationRequest> BuildNotification<T>(SystemNotificationRule rule, T entity)
        {
            var entityId = GetEntityId(entity);
            var entityName = GetEntityDisplayName(entity);
            object? fieldValue = null;

            // System auto-generates notification content
            var title = GenerateDefaultTitle(rule, entityName, fieldValue);
            var message = GenerateDefaultMessage(rule, entityName, fieldValue);

            var req = new SystemNotificationRequest
            {
                Title = title ?? string.Empty,
                Message = message ?? string.Empty,
                EntityType = rule.EntityType,
                EntityId = entityId,
                RuleId = rule.Id,
                RuleName = rule.RuleName ?? string.Empty,
                Data = new Dictionary<string, object>
                {
                    ["ConditionsJson"] = rule.ConditionsJson ?? string.Empty,
                    ["EntityName"] = entityName ?? string.Empty
                }
            };

            return await Task.FromResult(req);
        }

        /// <summary>
        /// Generate default title cho standard rule
        /// </summary>
        private string GenerateDefaultTitle(SystemNotificationRule rule, string entityName, object? fieldValue)
        {
            return $"{entityName} - Rule '{rule.RuleName}' triggered";
        }

        /// <summary>
        /// Generate default message cho standard rule
        /// </summary>
        private string GenerateDefaultMessage(SystemNotificationRule rule, string entityName, object? fieldValue)
        {
            return $"Rule '{rule.RuleName}' triggered for {entityName}.";
        }
        /// <summary>
        /// Get entity ID từ entity
        /// </summary>
        private string GetEntityId<T>(T entity)
        {
            // Try to get Id property first
            var idProperty = typeof(T).GetProperty("Id");
            if (idProperty != null)
            {
                var idValue = idProperty.GetValue(entity);
                return idValue?.ToString() ?? Guid.NewGuid().ToString();
            }

            // Try to get CampaignId or ProductId
            var campaignIdProperty = typeof(T).GetProperty("CampaignId");
            if (campaignIdProperty != null)
            {
                var campaignIdValue = campaignIdProperty.GetValue(entity);
                return campaignIdValue?.ToString() ?? Guid.NewGuid().ToString();
            }

            var productIdProperty = typeof(T).GetProperty("ProductId");
            if (productIdProperty != null)
            {
                var productIdValue = productIdProperty.GetValue(entity);
                return productIdValue?.ToString() ?? Guid.NewGuid().ToString();
            }

            return Guid.NewGuid().ToString();
        }

        /// <summary>
        /// Get entity display name từ entity
        /// </summary>
        private string GetEntityDisplayName<T>(T entity)
        {
            // Try to get CampaignName or ProductName
            var campaignNameProperty = typeof(T).GetProperty("CampaignName");
            if (campaignNameProperty != null)
            {
                var campaignNameValue = campaignNameProperty.GetValue(entity);
                var str = campaignNameValue?.ToString();
                if (!string.IsNullOrEmpty(str))
                    return str;
            }

            var productNameProperty = typeof(T).GetProperty("ProductName");
            if (productNameProperty != null)
            {
                var productNameValue = productNameProperty.GetValue(entity);
                var str = productNameValue?.ToString();
                if (!string.IsNullOrEmpty(str))
                    return str;
            }

            // Fallback to entity type
            return typeof(T).Name;
        }

        /// <summary>
        /// PUBLIC: Get property value from entity using reflection
        /// </summary>
        public object? GetPropertyValue<T>(T entity, string propertyName)
        {
            if (entity == null || string.IsNullOrWhiteSpace(propertyName))
                return null;

            try
            {
                var runtimeType = entity.GetType();

                var property = runtimeType.GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase)
                               ?? runtimeType
                                    .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                    .FirstOrDefault(p => string.Equals(p.Name, propertyName, StringComparison.OrdinalIgnoreCase));
                if (property != null)
                {
                    return property.GetValue(entity);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}
