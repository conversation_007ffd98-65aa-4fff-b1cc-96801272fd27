/**
 * Main Entry Point for System Notification Rules
 * Initializes all components and sets up the application
 * 
 * Load order is important:
 * 1. entityTypesConfig.js - Must load first to provide centralized entity types
 * 2. Other core files (constants.js, helpers.js, validation.js)
 * 3. Components (table.js, modals.js, queryBuilder.js)
 * 4. Services (apiService.js)
 * 5. Consumers (adAccountSelector.js, consumersManager.js)
 */

document.addEventListener('DOMContentLoaded', function () {
    // Initialize all components
    try {
        // Initialize core components
        console.log('Initializing System Notification Rules...');
        
        // Initialize table manager
        if (window.TableManager) {
            window.TableManager.init();
            console.log('✅ TableManager initialized');
        }
        
        // Initialize modal manager
        if (window.ModalManager) {
            window.ModalManager.init();
            console.log('✅ ModalManager initialized');
        }
        
        // Initialize query builder manager
        if (window.QueryBuilderManager) {
            console.log('✅ QueryBuilderManager available');
        }
        
        // Initialize consumers manager
        if (window.ConsumersManager) {
            window.ConsumersManager.init();
            console.log('✅ ConsumersManager initialized');
        }
        
        // Initialize ad account selector
        if (window.AdAccountSelector) {
            window.AdAccountSelector.init();
            console.log('✅ AdAccountSelector initialized');
        }
        
        // Setup real-time validation
        if (window.setupRealTimeValidation) {
            window.setupRealTimeValidation();
            console.log('✅ Real-time validation setup');
        }
        
        console.log('🎉 System Notification Rules initialized successfully!');
        
    } catch (error) {
        console.error('❌ Error initializing System Notification Rules:', error);
    }
});

// Global error handler for unhandled errors
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
});

// Export for testing purposes
window.SystemNotificationRulesApp = {
    version: '1.0.0',
    components: {
        TableManager: window.TableManager,
        ModalManager: window.ModalManager,
        QueryBuilderManager: window.QueryBuilderManager,
        ConsumersManager: window.ConsumersManager,
        AdAccountSelector: window.AdAccountSelector
    }
};
