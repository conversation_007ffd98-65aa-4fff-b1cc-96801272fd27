using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Domain.Entities.SystemNotification
{
    /// <summary>
    /// Entity cho System Notification Rules - Đ<PERSON><PERSON> nghĩa các rule để gửi thông báo hệ thống
    /// Khác với Notification hiện tại (dành cho thông báo gửi thiết bị), đây là rule engine cho system notifications
    /// </summary>
    [Table("SystemNotificationRule")]
    public class SystemNotificationRule : FullAuditedEntity<Guid>
    {
        /// <summary>
        /// Tên rule - để user dễ nhận biết
        /// </summary>
        [Required]
        [StringLength(200)]
        public string RuleName { get; set; }

        /// <summary>
        /// Loại entity để áp dụng rule: "FactGmvMaxCampaign", "FactGmvMaxProduct"
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; }

        /// <summary>
        /// Điều kiện dạng QueryBuilder JSON (AND/OR, groups, rules)
        /// Ưu tiên sử dụng thay cho FieldName/Operator/Value nếu có giá trị
        /// </summary>
        [StringLength(4000)]
        public string? ConditionsJson { get; set; }

        /// <summary>
        /// Trạng thái hoạt động của rule
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Thời gian trigger lần cuối - để check cooldown
        /// </summary>
        public DateTime? LastTriggeredAt { get; set; }

        /// <summary>
        /// Số lần rule đã được trigger
        /// </summary>
        public long TriggerCount { get; set; } = 0;

        /// <summary>
        /// Quy tắc mặc định của hệ thống - áp dụng cho toàn bộ hệ thống
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// Quy tắc dùng chung - ai cũng có thể nhìn thấy nhưng người có quyền mới được thêm và sửa
        /// </summary>
        public bool IsPublic { get; set; } = false;

    }
}
