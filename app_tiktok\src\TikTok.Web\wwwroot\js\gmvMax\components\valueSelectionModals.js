/**
 * Value Selection Modals for GMV Max
 * Copy HOÀN TOÀN logic từ factGmvMaxProduct.js và factGmvMaxCampaign.js
 */

// ✅ COPY HOÀN TOÀN Value Selection Modal logic for Product từ factGmvMaxProduct.js
document.addEventListener('DOMContentLoaded', function () {
    // ✅ Product Value Selection Modal Logic
    function getStoredProductConfiguration() {
        const stored = localStorage.getItem('productColumnAggregations');
        if (stored) {
            try {
                const config = JSON.parse(stored);
                // Support new format with selectedFields and aggregations
                if (config.selectedFields && config.aggregations) {
                    return config;
                } else {
                    // Old format - treat as aggregations only
                    return {
                        selectedFields: defaultProductValues,
                        aggregations: config,
                    };
                }
            } catch (e) {}
        }

        // Return default configuration for products
        return {
            selectedFields: defaultProductValues,
            aggregations: {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROAS: 'Avg',
                ACOS: 'Avg',
                Impressions: 'Sum',
                Clicks: 'Sum',
                CTR: 'Avg',
                CostPerOrder: 'Avg',
                AdsRevenue: 'Sum',
                OrganicRevenue: 'Sum',
                ProductPrice: 'Avg',
                QuantitySold: 'Sum',
                TACOS: 'Avg',
                ConversionRate: 'Avg',
            },
        };
    }

    // Backward compatibility function
    function getStoredProductColumnAggregations() {
        return getStoredProductConfiguration().aggregations;
    }

    // Function to get value definitions with column aggregations for Product
    async function getProductValueDefsWithColumnAggregations(
        columnAggregations = null
    ) {
        const aggregations =
            columnAggregations || getStoredProductColumnAggregations();

        // Wait for ABP to be ready and get permissions
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('product');

        const allValueDefs = [
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu ($)`,
                type: aggregations['GrossRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'AdsRevenue',
                caption: `Doanh thu quảng cáo ($)`,
                type: aggregations['AdsRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'OrganicRevenue',
                caption: `Doanh thu tự nhiên ($)`,
                type: aggregations['OrganicRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo ($)`,
                type: aggregations['Cost'] || 'Sum',
                category: 'spending',
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn ($)`,
                type: aggregations['CostPerOrder'] || 'Avg',
                category: 'spending',
            },
            {
                name: 'ProductPrice',
                caption: `Giá sản phẩm ($)`,
                type: aggregations['ProductPrice'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'ROAS',
                caption: 'ROI',
                type: aggregations['ROAS'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'TACOS',
                caption: 'TACOS (%)',
                type: aggregations['TACOS'] || 'Avg',
                category: 'restricted',
            },
            {
                name: 'Orders',
                caption: 'Số đơn hàng',
                type: aggregations['Orders'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'QuantitySold',
                caption: 'Số lượng bán được',
                type: aggregations['QuantitySold'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ProductImpressions',
                caption: 'Lượt xem sản phẩm',
                type: aggregations['ProductImpressions'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ProductClicks',
                caption: 'Lượt click sản phẩm',
                type: aggregations['ProductClicks'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ConversionRate',
                caption: 'Tỷ lệ chuyển đổi (%)',
                type: aggregations['ConversionRate'] || 'Avg',
                category: 'metrics',
            },
        ];

        // Filter values based on permissions
        const filteredValues = allValueDefs.filter((value) => {
            if (permissions.viewAll || permissions.viewAllAdvertisers)
                return true;
            if (
                value.category === 'spending' &&
                (permissions.viewSpending || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'metrics' &&
                (permissions.viewMetrics || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'restricted' &&
                (permissions.viewAll || permissions.viewAllAdvertisers)
            )
                return true;
            return false;
        });

        if (filteredValues.length === 0) {
            console.warn(
                '⚠️ No values filtered by permissions - user has no access to any fields'
            );
            return [];
        }

        return filteredValues;
    }

    // Get current configuration from localStorage
    const defaultProductValues = [
        'GrossRevenue',
        'Cost',
        'CostPerOrder',
        'ROAS',
        'Orders',
    ];
    let currentProductConfig = getStoredProductConfiguration();
    let currentProductColumnAggregations = currentProductConfig.aggregations;
    let productValueSelectedState = currentProductConfig.selectedFields;

    // Initialize valueDefs with current column aggregations (will be updated when modal opens)
    let productValueDefs = [];

    // Function to render value checkboxes with individual aggregation dropdowns for Product
    async function renderProductValueCheckboxesWithAggregations(selected) {
        const container = document.getElementById(
            'product-value-checkbox-list'
        );
        container.innerHTML = '';

        // Get filtered value definitions based on permissions
        const filteredValueDefs =
            await getProductValueDefsWithColumnAggregations(
                currentProductColumnAggregations
            );

        filteredValueDefs.forEach((v) => {
            const col = document.createElement('div');
            col.className = 'col-6 col-md-4 mb-3';

            const currentAggregation =
                currentProductColumnAggregations[v.name] || 'Avg';

            col.innerHTML = `
                <div class='card border-light'>
                    <div class='card-body p-2'>
                        <div class='form-check d-flex align-items-center mb-2'>
                            <input class='form-check-input product-value-checkbox me-2' type='checkbox' value='${
                                v.name
                            }' id='cb-product-${v.name}' ${
                selected.includes(v.name) ? 'checked' : ''
            }>
                            <label class='form-check-label fw-bold' for='cb-product-${
                                v.name
                            }'>${v.caption}</label>
                        </div>
                        <div class='d-flex align-items-center'>
                            <label class='form-label small mb-0 me-2'>Chế độ:</label>
                            <select class='form-select form-select-sm product-column-aggregation' data-column='${
                                v.name
                            }' style='width: auto;'>
                                <option value='Sum' ${
                                    currentAggregation === 'Sum'
                                        ? 'selected'
                                        : ''
                                }>Tổng (Sum)</option>
                                <option value='Avg' ${
                                    currentAggregation === 'Avg'
                                        ? 'selected'
                                        : ''
                                }>Trung bình (Avg)</option>
                                <option value='Max' ${
                                    currentAggregation === 'Max'
                                        ? 'selected'
                                        : ''
                                }>Lớn nhất (Max)</option>
                                <option value='Min' ${
                                    currentAggregation === 'Min'
                                        ? 'selected'
                                        : ''
                                }>Nhỏ nhất (Min)</option>
                                <option value='First' ${
                                    currentAggregation === 'First'
                                        ? 'selected'
                                        : ''
                                }>Bản ghi đầu (First)</option>
                                <option value='Last' ${
                                    currentAggregation === 'Last'
                                        ? 'selected'
                                        : ''
                                }>Bản ghi cuối (Last)</option>
                                <option value='Count' ${
                                    currentAggregation === 'Count'
                                        ? 'selected'
                                        : ''
                                }>Đếm (Count)</option>
                                <option value='DistinctCount' ${
                                    currentAggregation === 'DistinctCount'
                                        ? 'selected'
                                        : ''
                                }>Đếm duy nhất (Distinct)</option>
                            </select>
                        </div>
                    </div>
                </div>`;
            container.appendChild(col);
        });

        // Add event listeners for aggregation dropdowns
        document
            .querySelectorAll('.product-column-aggregation')
            .forEach((select) => {
                select.addEventListener('change', function () {
                    const columnName = this.dataset.column;
                    const newAggregation = this.value;

                    // Update local state
                    currentProductColumnAggregations[columnName] =
                        newAggregation;

                    // ✅ Save both selectedFields and aggregations in single key
                    const productConfig = {
                        selectedFields: productValueSelectedState,
                        aggregations: currentProductColumnAggregations,
                    };
                    localStorage.setItem(
                        'productColumnAggregations',
                        JSON.stringify(productConfig)
                    );
                });
            });

        // Add event listeners for checkboxes to update selected count
        document
            .querySelectorAll('.product-value-checkbox')
            .forEach((checkbox) => {
                checkbox.addEventListener('change', updateProductSelectedCount);
            });

        // Update selected count initially
        updateProductSelectedCount();
    }

    // Function to update selected count display for Product
    function updateProductSelectedCount() {
        const selectedCount = document.querySelectorAll(
            '.product-value-checkbox:checked'
        ).length;
        const countElement = document.getElementById('product-selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
            countElement.className =
                selectedCount > 0
                    ? 'fw-bold text-success'
                    : 'fw-bold text-muted';
        }
    }

    // ✅ Function to show/hide quick selection buttons based on permissions
    async function updateProductQuickSelectionButtonsVisibility() {
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('product');

        // Show/hide buttons based on available field categories
        const revenueCostBtn = document.getElementById(
            'select-product-revenue-cost'
        );
        const metricsBtn = document.getElementById('select-product-metrics');
        const allBtn = document.getElementById('select-all-product');

        // Show revenue & cost button if user has metrics and spending permission
        if (revenueCostBtn) {
            revenueCostBtn.style.display =
                permissions.viewMetrics ||
                permissions.viewSpending ||
                permissions.viewAll ||
                permissions.viewAllAdvertisers
                    ? 'inline-block'
                    : 'none';
        }

        // Show metrics button if user has metrics permission
        if (metricsBtn) {
            metricsBtn.style.display =
                permissions.viewMetrics ||
                permissions.viewAll ||
                permissions.viewAllAdvertisers
                    ? 'inline-block'
                    : 'none';
        }

        // Show all button for all users
        if (allBtn) {
            allBtn.style.display = 'inline-block';
        }
    }

    // ✅ Product Modal Event Handlers
    $('#open-product-value-selection-modal').on('click', async function () {
        // Sync column aggregations from localStorage
        currentProductColumnAggregations = getStoredProductColumnAggregations();

        // Update valueDefs with current aggregations and permissions
        productValueDefs = await getProductValueDefsWithColumnAggregations(
            currentProductColumnAggregations
        );

        // Update quick selection buttons visibility based on permissions
        await updateProductQuickSelectionButtonsVisibility();

        // Render checkboxes with current selected values
        await renderProductValueCheckboxesWithAggregations(
            productValueSelectedState
        );

        // Show modal
        new bootstrap.Modal(
            document.getElementById('productValueSelectionModal')
        ).show();
    });

    $('#select-product-default').on('click', async function () {
        // Only select default values that are available based on permissions
        productValueSelectedState = defaultProductValues.filter((field) =>
            productValueDefs.some((v) => v.name === field)
        );
        await renderProductValueCheckboxesWithAggregations(
            productValueSelectedState
        );
    });

    $('#select-product-revenue-cost').on('click', async function () {
        // Only select revenue and cost fields that are available based on permissions
        const revenueCostFields = [
            'GrossRevenue',
            'AdsRevenue',
            'OrganicRevenue',
            'Cost',
            'CostPerOrder',
        ];
        productValueSelectedState = revenueCostFields.filter((field) =>
            productValueDefs.some((v) => v.name === field)
        );
        await renderProductValueCheckboxesWithAggregations(
            productValueSelectedState
        );
    });

    $('#select-product-metrics').on('click', async function () {
        // Only select metrics fields that are available based on permissions
        const metricsFields = [
            'ROAS',
            'Orders',
            'QuantitySold',
            'ProductImpressions',
            'ProductClicks',
            'ConversionRate',
        ];
        productValueSelectedState = metricsFields.filter((field) =>
            productValueDefs.some((v) => v.name === field)
        );
        await renderProductValueCheckboxesWithAggregations(
            productValueSelectedState
        );
    });

    $('#select-all-product').on('click', async function () {
        productValueSelectedState = productValueDefs.map((v) => v.name);
        await renderProductValueCheckboxesWithAggregations(
            productValueSelectedState
        );
    });

    $('#apply-product-value-selection').on('click', function () {
        // Get all checked values
        const selected = Array.from(
            document.querySelectorAll('.product-value-checkbox:checked')
        ).map((cb) => cb.value);
        productValueSelectedState = selected;

        // ✅ Save both selectedFields and aggregations in single key
        const productConfig = {
            selectedFields: productValueSelectedState,
            aggregations: currentProductColumnAggregations,
        };
        localStorage.setItem(
            'productColumnAggregations',
            JSON.stringify(productConfig)
        );

        if (
            window.gmvMaxDashboard &&
            window.gmvMaxDashboard.campaignTab &&
            window.gmvMaxDashboard.campaignTab.productPivotTable &&
            selected.length > 0
        ) {
            // Update pivot table with current column aggregations
            window.gmvMaxDashboard.campaignTab.productPivotTable
                .updatePivotTableWithColumnAggregations()
                .catch((error) => {
                    console.error('Error updating product pivot table:', error);
                });
        }

        // Close modal
        bootstrap.Modal.getInstance(
            document.getElementById('productValueSelectionModal')
        ).hide();

        // Refresh pivot table
        setTimeout(() => {
            if (
                window.gmvMaxDashboard &&
                window.gmvMaxDashboard.campaignTab &&
                window.gmvMaxDashboard.campaignTab.productPivotTable
            ) {
                window.gmvMaxDashboard.campaignTab.productPivotTable.showGrid();
            }
        }, 100);
    });

    // ✅ Campaign Value Selection Modal Logic
    function getStoredCampaignConfiguration() {
        const stored = localStorage.getItem('campaignColumnAggregations');
        if (stored) {
            try {
                const config = JSON.parse(stored);
                // Support new format with selectedFields and aggregations
                if (config.selectedFields && config.aggregations) {
                    return config;
                } else {
                    // Old format - treat as aggregations only
                    return {
                        selectedFields: defaultCampaignValues,
                        aggregations: config,
                    };
                }
            } catch (e) {}
        }

        // Return default configuration for campaigns
        return {
            selectedFields: defaultCampaignValues,
            aggregations: {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROAS: 'Average',
                TACOS: 'Average',
                CostPerOrder: 'Average',
            },
        };
    }

    // Backward compatibility function
    function getStoredCampaignColumnAggregations() {
        return getStoredCampaignConfiguration().aggregations;
    }

    function getCampaignValueDefsWithColumnAggregations(
        columnAggregations = null
    ) {
        const aggregations =
            columnAggregations || getStoredCampaignColumnAggregations();

        const allValueDefs = [
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu ($)`,
                type: aggregations.GrossRevenue || 'Sum',
                permission: 'ViewMetrics', // Revenue metrics
            },
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo ($)`,
                type: aggregations.Cost || 'Sum',
                permission: 'ViewSpending', // Spending data
            },
            {
                name: 'ROAS',
                caption: `ROI`,
                type: aggregations.ROAS || 'Average',
                permission: 'ViewMetrics', // Performance metrics
            },
            {
                name: 'TACOS',
                caption: `TACOS (%)`,
                type: aggregations.TACOS || 'Average',
                permission: 'ViewAll', // Restricted field - only for ViewAll/ViewAllAdvertisers
            },
            {
                name: 'Orders',
                caption: `Số đơn hàng`,
                type: aggregations.Orders || 'Sum',
                permission: 'ViewMetrics', // Order metrics
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn ($)`,
                type: aggregations.CostPerOrder || 'Average',
                permission: 'ViewSpending', // Cost per order
            },
        ];

        // Filter based on permissions
        return allValueDefs.filter((valueDef) => {
            if (!window.PermissionHelper) {
                // Default: show all if PermissionHelper not available
                return true;
            }

            // Get unified FactGmvMax permissions
            const permissions =
                window.PermissionHelper.getPermissions('campaign');

            // If user has ViewAll or ViewAllAdvertisers permission, show everything
            if (permissions.viewAll || permissions.viewAllAdvertisers) {
                return true;
            }

            // Check specific permission
            if (valueDef.permission === 'ViewSpending') {
                return (
                    permissions.viewSpending || permissions.viewAllAdvertisers
                );
            } else if (valueDef.permission === 'ViewMetrics') {
                return (
                    permissions.viewMetrics || permissions.viewAllAdvertisers
                );
            } else if (valueDef.permission === 'ViewAll') {
                return permissions.viewAll || permissions.viewAllAdvertisers;
            }

            // Default: hide if no matching permission
            return false;
        });
    }

    // Get current configuration from localStorage
    const defaultCampaignValues = [
        'GrossRevenue',
        'Cost',
        'ROAS',
        'Orders',
        'CostPerOrder',
    ];
    let currentCampaignConfig = getStoredCampaignConfiguration();
    let currentCampaignColumnAggregations = currentCampaignConfig.aggregations;
    let campaignValueSelectedState = currentCampaignConfig.selectedFields;

    // Initialize valueDefs with current column aggregations
    let campaignValueDefs = getCampaignValueDefsWithColumnAggregations(
        currentCampaignColumnAggregations
    );

    // Function to render campaign value checkboxes
    function renderCampaignValueCheckboxes(selected) {
        const container = document.getElementById('live-value-checkbox-list');
        if (!container) {
            return;
        }
        container.innerHTML = '';

        // ✅ FIXED: Create checkboxes with individual aggregation dropdowns (GIỐNG HỆT FACTBALANCE)
        campaignValueDefs.forEach((valueDef) => {
            const isChecked = selected.includes(valueDef.name);
            const currentAggregation =
                currentCampaignColumnAggregations[valueDef.name] || 'Sum';

            const checkboxHtml = `
                <div class="col-6 col-md-4 mb-3">
                    <div class="card border-light">
                        <div class="card-body p-2">
                            <div class="form-check d-flex align-items-center mb-2">
                                <input class="form-check-input live-value-checkbox me-2" type="checkbox" value="${
                                    valueDef.name
                                }" id="cb-live-${valueDef.name}" ${
                isChecked ? 'checked' : ''
            }>
                                <label class="form-check-label fw-bold" for="cb-live-${
                                    valueDef.name
                                }">${valueDef.caption}</label>
                            </div>
                            <div class="d-flex align-items-center">
                                <label class="form-label small mb-0 me-2">Chế độ:</label>
                                <select class="form-select form-select-sm live-column-aggregation" data-column="${
                                    valueDef.name
                                }" style="width: auto;">
                                    <option value="Sum" ${
                                        currentAggregation === 'Sum'
                                            ? 'selected'
                                            : ''
                                    }>Tổng (Sum)</option>
                                    <option value="Avg" ${
                                        currentAggregation === 'Avg'
                                            ? 'selected'
                                            : ''
                                    }>Trung bình (Avg)</option>
                                    <option value="Max" ${
                                        currentAggregation === 'Max'
                                            ? 'selected'
                                            : ''
                                    }>Lớn nhất (Max)</option>
                                    <option value="Min" ${
                                        currentAggregation === 'Min'
                                            ? 'selected'
                                            : ''
                                    }>Nhỏ nhất (Min)</option>
                                    <option value="First" ${
                                        currentAggregation === 'First'
                                            ? 'selected'
                                            : ''
                                    }>Bản ghi đầu (First)</option>
                                    <option value="Last" ${
                                        currentAggregation === 'Last'
                                            ? 'selected'
                                            : ''
                                    }>Bản ghi cuối (Last)</option>
                                    <option value="Count" ${
                                        currentAggregation === 'Count'
                                            ? 'selected'
                                            : ''
                                    }>Đếm (Count)</option>
                                    <option value="DistinctCount" ${
                                        currentAggregation === 'DistinctCount'
                                            ? 'selected'
                                            : ''
                                    }>Đếm duy nhất (Distinct)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += checkboxHtml;
        });

        // ✅ Add event listeners for aggregation dropdowns (GIỐNG HỆT FACTBALANCE)
        document
            .querySelectorAll('.live-column-aggregation')
            .forEach((select) => {
                select.addEventListener('change', function () {
                    const columnName = this.dataset.column;
                    const newAggregation = this.value;

                    // Update local state
                    currentCampaignColumnAggregations[columnName] =
                        newAggregation;

                    // ✅ Save both selectedFields and aggregations in single key
                    const campaignConfig = {
                        selectedFields: campaignValueSelectedState,
                        aggregations: currentCampaignColumnAggregations,
                    };
                    localStorage.setItem(
                        'campaignColumnAggregations',
                        JSON.stringify(campaignConfig)
                    );
                });
            });

        // ✅ Add event listeners for checkboxes to update selected count
        document
            .querySelectorAll('.live-value-checkbox')
            .forEach((checkbox) => {
                checkbox.addEventListener(
                    'change',
                    updateCampaignSelectedCount
                );
            });

        // ✅ Update selected count initially
        updateCampaignSelectedCount();
    }

    // Function to update selected count display for Campaign
    function updateCampaignSelectedCount() {
        const selectedCount = document.querySelectorAll(
            '.live-value-checkbox:checked'
        ).length;
        const countElement = document.getElementById('live-selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
            countElement.className =
                selectedCount > 0
                    ? 'fw-bold text-success'
                    : 'fw-bold text-muted';
        }
    }

    // ✅ Function to show/hide quick selection buttons based on permissions for Live Campaign
    async function updateLiveQuickSelectionButtonsVisibility() {
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('campaign');

        // Show/hide buttons based on available field categories
        const revenueCostBtn = document.getElementById(
            'select-live-revenue-cost'
        );
        const metricsBtn = document.getElementById('select-live-metrics');
        const allBtn = document.getElementById('select-all-live');

        // Show revenue & cost button if user has metrics or spending permission
        if (revenueCostBtn) {
            revenueCostBtn.style.display =
                permissions.viewMetrics ||
                permissions.viewSpending ||
                permissions.viewAll ||
                permissions.viewAllAdvertisers
                    ? 'inline-block'
                    : 'none';
        }

        // Show metrics button if user has metrics permission
        if (metricsBtn) {
            metricsBtn.style.display =
                permissions.viewMetrics ||
                permissions.viewAll ||
                permissions.viewAllAdvertisers
                    ? 'inline-block'
                    : 'none';
        }

        // Show all button for all users
        if (allBtn) {
            allBtn.style.display = 'inline-block';
        }
    }

    // ✅ Campaign Modal event handlers
    $('#open-live-value-selection-modal').on('click', async function () {
        // ✅ NEW: Sync column aggregations from localStorage (GIỐNG HỆT FACTBALANCE)
        currentCampaignColumnAggregations =
            getStoredCampaignColumnAggregations();

        // ✅ NEW: Update valueDefs with current aggregations (GIỐNG HỆT FACTBALANCE)
        campaignValueDefs = getCampaignValueDefsWithColumnAggregations(
            currentCampaignColumnAggregations
        );

        // Update quick selection buttons visibility based on permissions
        await updateLiveQuickSelectionButtonsVisibility();

        // Clear existing checkboxes and render new ones
        renderCampaignValueCheckboxes(campaignValueSelectedState);

        // Show the modal using Bootstrap 5
        const modal = new bootstrap.Modal(
            document.getElementById('liveValueSelectionModal')
        );
        modal.show();
    });

    // Handle apply button click for Campaign
    $('#apply-live-value-selection').on('click', function () {
        // ✅ Get all checked values using new class selector
        const selected = Array.from(
            document.querySelectorAll('.live-value-checkbox:checked')
        ).map((cb) => cb.value);

        campaignValueSelectedState = selected;

        // ✅ Save both selectedFields and aggregations in single key
        const campaignConfig = {
            selectedFields: campaignValueSelectedState,
            aggregations: currentCampaignColumnAggregations,
        };
        localStorage.setItem(
            'campaignColumnAggregations',
            JSON.stringify(campaignConfig)
        );

        if (
            window.gmvMaxDashboard &&
            window.gmvMaxDashboard.campaignTab &&
            window.gmvMaxDashboard.campaignTab.livePivotTable &&
            selected.length > 0
        ) {
            // ✅ Update pivot table with current column aggregations
            window.gmvMaxDashboard.campaignTab.livePivotTable
                .updatePivotTableWithColumnAggregations()
                .catch((error) => {
                    // Handle error silently
                });
        }

        // Hide modal using Bootstrap 5
        const modal = bootstrap.Modal.getInstance(
            document.getElementById('liveValueSelectionModal')
        );
        if (modal) {
            modal.hide();
        }

        // Refresh pivot table
        setTimeout(() => {
            if (
                window.gmvMaxDashboard &&
                window.gmvMaxDashboard.campaignTab &&
                window.gmvMaxDashboard.campaignTab.livePivotTable
            ) {
                window.gmvMaxDashboard.campaignTab.livePivotTable.showGrid();
            }
        }, 100);
    });

    // ✅ New Quick Selection Button Handlers for Live Campaign
    $('#select-live-default').on('click', async function () {
        // Only select default values that are available based on permissions
        campaignValueSelectedState = defaultCampaignValues.filter((field) =>
            campaignValueDefs.some((v) => v.name === field)
        );
        renderCampaignValueCheckboxes(campaignValueSelectedState);
    });

    $('#select-live-revenue-cost').on('click', async function () {
        // Only select revenue and cost fields that are available based on permissions
        const revenueCostFields = ['GrossRevenue', 'Cost'];
        campaignValueSelectedState = revenueCostFields.filter((field) =>
            campaignValueDefs.some((v) => v.name === field)
        );
        renderCampaignValueCheckboxes(campaignValueSelectedState);
    });

    $('#select-live-metrics').on('click', async function () {
        // Only select metrics fields that are available based on permissions
        const metricsFields = ['ROAS', 'Orders', 'CostPerOrder'];
        campaignValueSelectedState = metricsFields.filter((field) =>
            campaignValueDefs.some((v) => v.name === field)
        );
        renderCampaignValueCheckboxes(campaignValueSelectedState);
    });

    $('#select-all-live').on('click', async function () {
        campaignValueSelectedState = campaignValueDefs.map((v) => v.name);
        renderCampaignValueCheckboxes(campaignValueSelectedState);
    });

    // ========================================
    // ✅ Creative Value Selection Modal Logic
    // ========================================
    function getStoredCreativeConfiguration() {
        const stored = localStorage.getItem('creativeColumnAggregations');
        if (stored) {
            try {
                const config = JSON.parse(stored);
                if (config.selectedFields && config.aggregations) {
                    return config;
                } else {
                    return {
                        selectedFields: defaultCreativeValues,
                        aggregations: config,
                    };
                }
            } catch (e) {}
        }

        return {
            selectedFields: defaultCreativeValues,
            aggregations: {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROAS: 'Avg',
                CostPerOrder: 'Avg',
                ProductImpressions: 'Sum',
                ProductClicks: 'Sum',
                ProductClickRate: 'Avg',
                AdClickRate: 'Avg',
                AdConversionRate: 'Avg',
                AdVideoViewRate2s: 'Avg',
                AdVideoViewRate6s: 'Avg',
                AdVideoViewRateP25: 'Avg',
                AdVideoViewRateP50: 'Avg',
                AdVideoViewRateP75: 'Avg',
                AdVideoViewRateP100: 'Avg',
            },
        };
    }

    function getStoredCreativeColumnAggregations() {
        return getStoredCreativeConfiguration().aggregations;
    }

    async function getCreativeValueDefsWithColumnAggregations(
        columnAggregations = null
    ) {
        const aggregations =
            columnAggregations || getStoredCreativeColumnAggregations();

        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('creative');

        const allValueDefs = [
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu ($)`,
                type: aggregations['GrossRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo ($)`,
                type: aggregations['Cost'] || 'Sum',
                category: 'spending',
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn ($)`,
                type: aggregations['CostPerOrder'] || 'Avg',
                category: 'spending',
            },
            {
                name: 'ROAS',
                caption: 'ROI',
                type: aggregations['ROAS'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'Orders',
                caption: 'Số đơn hàng',
                type: aggregations['Orders'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'TACOS',
                caption: 'TACOS (Tỷ lệ chi phí/doanh thu)',
                type: aggregations['TACOS'] || 'Avg',
                category: 'restricted',
            },
            {
                name: 'ProductImpressions',
                caption: 'Lượt hiển thị sản phẩm',
                type: aggregations['ProductImpressions'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ProductClicks',
                caption: 'Lượt click sản phẩm',
                type: aggregations['ProductClicks'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ProductClickRate',
                caption: 'Tỷ lệ click sản phẩm (%)',
                type: aggregations['ProductClickRate'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdClickRate',
                caption: 'Tỷ lệ click quảng cáo (%)',
                type: aggregations['AdClickRate'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdConversionRate',
                caption: 'Tỷ lệ chuyển đổi (%)',
                type: aggregations['AdConversionRate'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRate2s',
                caption: 'Tỷ lệ xem video 2s (%)',
                type: aggregations['AdVideoViewRate2s'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRate6s',
                caption: 'Tỷ lệ xem video 6s (%)',
                type: aggregations['AdVideoViewRate6s'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP25',
                caption: 'Tỷ lệ xem video 25% (%)',
                type: aggregations['AdVideoViewRateP25'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP50',
                caption: 'Tỷ lệ xem video 50% (%)',
                type: aggregations['AdVideoViewRateP50'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP75',
                caption: 'Tỷ lệ xem video 75% (%)',
                type: aggregations['AdVideoViewRateP75'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'AdVideoViewRateP100',
                caption: 'Tỷ lệ xem video 100% (%)',
                type: aggregations['AdVideoViewRateP100'] || 'Avg',
                category: 'metrics',
            },
        ];

        const filteredValues = allValueDefs.filter((value) => {
            if (permissions.viewAll || permissions.viewAllAdvertisers)
                return true;
            if (
                value.category === 'spending' &&
                (permissions.viewSpending || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'metrics' &&
                (permissions.viewMetrics || permissions.viewAllAdvertisers)
            )
                return true;
            if (value.category === 'restricted') return false; // Restricted fields chỉ hiển thị với ViewAll/ViewAllAdvertisers
            return false;
        });

        if (filteredValues.length === 0) {
            console.warn('⚠️ No values filtered by permissions for Creative');
            return [];
        }

        return filteredValues;
    }

    const defaultCreativeValues = [
        'GrossRevenue',
        'Cost',
        'CostPerOrder',
        'ROAS',
        'Orders',
        'ProductImpressions',
        'ProductClicks',
    ];
    let currentCreativeConfig = getStoredCreativeConfiguration();
    let currentCreativeColumnAggregations = currentCreativeConfig.aggregations;
    let creativeValueSelectedState = currentCreativeConfig.selectedFields;
    let creativeValueDefs = [];

    async function renderCreativeValueCheckboxesWithAggregations(selected) {
        const container = document.getElementById(
            'creative-value-checkbox-list'
        );
        container.innerHTML = '';

        const filteredValueDefs =
            await getCreativeValueDefsWithColumnAggregations(
                currentCreativeColumnAggregations
            );

        filteredValueDefs.forEach((v) => {
            const col = document.createElement('div');
            col.className = 'col-6 col-md-4 mb-3';

            const currentAggregation =
                currentCreativeColumnAggregations[v.name] || 'Avg';

            col.innerHTML = `
                <div class='card border-light'>
                    <div class='card-body p-2'>
                        <div class='form-check d-flex align-items-center mb-2'>
                            <input class='form-check-input creative-value-checkbox me-2' type='checkbox' value='${
                                v.name
                            }' id='cb-creative-${v.name}' ${
                selected.includes(v.name) ? 'checked' : ''
            }>
                            <label class='form-check-label fw-bold' for='cb-creative-${
                                v.name
                            }'>${v.caption}</label>
                        </div>
                        <div class='d-flex align-items-center'>
                            <label class='form-label small mb-0 me-2'>Chế độ:</label>
                            <select class='form-select form-select-sm creative-column-aggregation' data-column='${
                                v.name
                            }' style='width: auto;'>
                                <option value='Sum' ${
                                    currentAggregation === 'Sum'
                                        ? 'selected'
                                        : ''
                                }>Tổng (Sum)</option>
                                <option value='Avg' ${
                                    currentAggregation === 'Avg'
                                        ? 'selected'
                                        : ''
                                }>Trung bình (Avg)</option>
                                <option value='Max' ${
                                    currentAggregation === 'Max'
                                        ? 'selected'
                                        : ''
                                }>Lớn nhất (Max)</option>
                                <option value='Min' ${
                                    currentAggregation === 'Min'
                                        ? 'selected'
                                        : ''
                                }>Nhỏ nhất (Min)</option>
                                <option value='First' ${
                                    currentAggregation === 'First'
                                        ? 'selected'
                                        : ''
                                }>Bản ghi đầu (First)</option>
                                <option value='Last' ${
                                    currentAggregation === 'Last'
                                        ? 'selected'
                                        : ''
                                }>Bản ghi cuối (Last)</option>
                                <option value='Count' ${
                                    currentAggregation === 'Count'
                                        ? 'selected'
                                        : ''
                                }>Đếm (Count)</option>
                                <option value='DistinctCount' ${
                                    currentAggregation === 'DistinctCount'
                                        ? 'selected'
                                        : ''
                                }>Đếm duy nhất (Distinct)</option>
                            </select>
                        </div>
                    </div>
                </div>`;
            container.appendChild(col);
        });

        document
            .querySelectorAll('.creative-column-aggregation')
            .forEach((select) => {
                select.addEventListener('change', function () {
                    const columnName = this.dataset.column;
                    const newAggregation = this.value;

                    currentCreativeColumnAggregations[columnName] =
                        newAggregation;

                    const creativeConfig = {
                        selectedFields: creativeValueSelectedState,
                        aggregations: currentCreativeColumnAggregations,
                    };
                    localStorage.setItem(
                        'creativeColumnAggregations',
                        JSON.stringify(creativeConfig)
                    );
                });
            });

        document
            .querySelectorAll('.creative-value-checkbox')
            .forEach((checkbox) => {
                checkbox.addEventListener(
                    'change',
                    updateCreativeSelectedCount
                );
            });

        updateCreativeSelectedCount();
    }

    function updateCreativeSelectedCount() {
        const selectedCount = document.querySelectorAll(
            '.creative-value-checkbox:checked'
        ).length;
        const countElement = document.getElementById('creative-selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
            countElement.className =
                selectedCount > 0
                    ? 'fw-bold text-success'
                    : 'fw-bold text-muted';
        }
    }

    async function updateCreativeQuickSelectionButtonsVisibility() {
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('creative');

        const revenueCostBtn = document.getElementById(
            'select-creative-revenue-cost'
        );
        const metricsBtn = document.getElementById('select-creative-metrics');
        const allBtn = document.getElementById('select-all-creative');

        if (revenueCostBtn) {
            revenueCostBtn.style.display =
                permissions.viewMetrics ||
                permissions.viewSpending ||
                permissions.viewAll ||
                permissions.viewAllAdvertisers
                    ? 'inline-block'
                    : 'none';
        }

        if (metricsBtn) {
            metricsBtn.style.display =
                permissions.viewMetrics ||
                permissions.viewAll ||
                permissions.viewAllAdvertisers
                    ? 'inline-block'
                    : 'none';
        }

        if (allBtn) {
            allBtn.style.display = 'inline-block';
        }
    }

    $('#open-creative-value-selection-modal').on('click', async function () {
        currentCreativeColumnAggregations =
            getStoredCreativeColumnAggregations();
        creativeValueDefs = await getCreativeValueDefsWithColumnAggregations(
            currentCreativeColumnAggregations
        );
        await updateCreativeQuickSelectionButtonsVisibility();
        await renderCreativeValueCheckboxesWithAggregations(
            creativeValueSelectedState
        );
        new bootstrap.Modal(
            document.getElementById('creativeValueSelectionModal')
        ).show();
    });

    $('#select-creative-default').on('click', async function () {
        creativeValueSelectedState = defaultCreativeValues.filter((field) =>
            creativeValueDefs.some((v) => v.name === field)
        );
        await renderCreativeValueCheckboxesWithAggregations(
            creativeValueSelectedState
        );
    });

    $('#select-creative-revenue-cost').on('click', async function () {
        const revenueCostFields = ['GrossRevenue', 'Cost', 'CostPerOrder'];
        creativeValueSelectedState = revenueCostFields.filter((field) =>
            creativeValueDefs.some((v) => v.name === field)
        );
        await renderCreativeValueCheckboxesWithAggregations(
            creativeValueSelectedState
        );
    });

    $('#select-creative-metrics').on('click', async function () {
        const metricsFields = [
            'ROAS',
            'Orders',
            'ProductImpressions',
            'ProductClicks',
            'ProductClickRate',
            'AdClickRate',
            'AdConversionRate',
            'AdVideoViewRate2s',
            'AdVideoViewRate6s',
            'AdVideoViewRateP25',
            'AdVideoViewRateP50',
            'AdVideoViewRateP75',
            'AdVideoViewRateP100',
        ];
        creativeValueSelectedState = metricsFields.filter((field) =>
            creativeValueDefs.some((v) => v.name === field)
        );
        await renderCreativeValueCheckboxesWithAggregations(
            creativeValueSelectedState
        );
    });

    $('#select-all-creative').on('click', async function () {
        creativeValueSelectedState = creativeValueDefs.map((v) => v.name);
        await renderCreativeValueCheckboxesWithAggregations(
            creativeValueSelectedState
        );
    });

    $('#apply-creative-value-selection').on('click', function () {
        const selected = Array.from(
            document.querySelectorAll('.creative-value-checkbox:checked')
        ).map((cb) => cb.value);
        creativeValueSelectedState = selected;

        const creativeConfig = {
            selectedFields: creativeValueSelectedState,
            aggregations: currentCreativeColumnAggregations,
        };
        localStorage.setItem(
            'creativeColumnAggregations',
            JSON.stringify(creativeConfig)
        );

        if (
            window.gmvMaxDashboard &&
            window.gmvMaxDashboard.campaignTab &&
            window.gmvMaxDashboard.campaignTab.creativePivotTable &&
            selected.length > 0
        ) {
            window.gmvMaxDashboard.campaignTab.creativePivotTable
                .updatePivotTableWithColumnAggregations()
                .catch((error) => {
                    console.error(
                        'Error updating creative pivot table:',
                        error
                    );
                });
        }

        bootstrap.Modal.getInstance(
            document.getElementById('creativeValueSelectionModal')
        ).hide();

        setTimeout(() => {
            if (
                window.gmvMaxDashboard &&
                window.gmvMaxDashboard.campaignTab &&
                window.gmvMaxDashboard.campaignTab.creativePivotTable
            ) {
                window.gmvMaxDashboard.campaignTab.creativePivotTable.showGrid();
            }
        }, 100);
    });
});
