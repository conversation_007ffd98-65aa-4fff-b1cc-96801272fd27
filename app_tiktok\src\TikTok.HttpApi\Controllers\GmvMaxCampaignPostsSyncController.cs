using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using TikTok.DataSync;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Controllers
{
    /// <summary>
    /// Controller cho GMV Max Campaign Posts Sync API
    /// </summary>
    [ApiController]
    [Route("api/app/gmv-max-campaign-posts-sync")]
    public class GmvMaxCampaignPostsSyncController : AbpControllerBase
    {
        private readonly IGmvMaxCampaignPostsSyncApplicationAppService _gmvMaxCampaignPostsSyncAppService;

        /// <summary>
        /// Constructor
        /// </summary>
        public GmvMaxCampaignPostsSyncController(IGmvMaxCampaignPostsSyncApplicationAppService gmvMaxCampaignPostsSyncAppService)
        {
            _gmvMaxCampaignPostsSyncAppService = gmvMaxCampaignPostsSyncAppService;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo Business Center ID
        /// Tự động truy vấn tất cả campaignId và advertiserId từ bảng RawGmvMaxCampaignsEntity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync")]
        public async Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsAsync(
            [Required] string bcId)
        {
            return await _gmvMaxCampaignPostsSyncAppService.SyncGmvMaxCampaignPostsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts cho nhiều Campaign của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="campaignIds">Danh sách Campaign IDs (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-many")]
        public async Task<GmvMaxCampaignPostsSyncResult> SyncManyGmvMaxCampaignPostsAsync(
            [Required] string advertiserId,
            [Required] string bcId,
            [FromBody] List<string>? campaignIds = null)
        {
            return await _gmvMaxCampaignPostsSyncAppService.SyncManyGmvMaxCampaignPostsAsync(advertiserId, bcId, campaignIds);
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign Posts cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-all")]
        public async Task<GmvMaxCampaignPostsSyncResult> SyncAllGmvMaxCampaignPostsAsync()
        {
            return await _gmvMaxCampaignPostsSyncAppService.SyncAllGmvMaxCampaignPostsAsync();
        }

        /// <summary>
        /// Lấy thống kê GMV Max Campaign Posts theo Campaign ID
        /// </summary>
        /// <param name="campaignId">ID của Campaign</param>
        /// <returns>Thống kê posts</returns>
        [HttpGet("stats/campaign/{campaignId}")]
        public async Task<object> GetCampaignPostsStatsAsync([Required] string campaignId)
        {
            // TODO: Implement statistics endpoint if needed
            return new { Message = "Statistics endpoint not implemented yet", CampaignId = campaignId };
        }

        /// <summary>
        /// Lấy thống kê GMV Max Campaign Posts theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <returns>Thống kê posts</returns>
        [HttpGet("stats/advertiser/{advertiserId}")]
        public async Task<object> GetAdvertiserPostsStatsAsync([Required] string advertiserId)
        {
            // TODO: Implement statistics endpoint if needed
            return new { Message = "Statistics endpoint not implemented yet", AdvertiserId = advertiserId };
        }

        /// <summary>
        /// Lấy thống kê GMV Max Campaign Posts theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Thống kê posts</returns>
        [HttpGet("stats/bc/{bcId}")]
        public async Task<object> GetBcPostsStatsAsync([Required] string bcId)
        {
            // TODO: Implement statistics endpoint if needed
            return new { Message = "Statistics endpoint not implemented yet", BcId = bcId };
        }

        /// <summary>
        /// Lấy thống kê video theo định dạng
        /// </summary>
        /// <param name="definition">Định dạng video (1080p, 540p, etc.)</param>
        /// <returns>Thống kê video</returns>
        [HttpGet("stats/video/definition/{definition}")]
        public async Task<object> GetVideoDefinitionStatsAsync([Required] string definition)
        {
            // TODO: Implement video statistics endpoint if needed
            return new { Message = "Video statistics endpoint not implemented yet", Definition = definition };
        }

        /// <summary>
        /// Lấy thống kê identity theo loại
        /// </summary>
        /// <param name="identityType">Loại identity (AUTH_CODE, BC_AUTH_TT, etc.)</param>
        /// <returns>Thống kê identity</returns>
        [HttpGet("stats/identity/type/{identityType}")]
        public async Task<object> GetIdentityTypeStatsAsync([Required] string identityType)
        {
            // TODO: Implement identity statistics endpoint if needed
            return new { Message = "Identity statistics endpoint not implemented yet", IdentityType = identityType };
        }
    }
}
