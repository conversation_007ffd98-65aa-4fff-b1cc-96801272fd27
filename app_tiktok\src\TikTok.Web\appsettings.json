{
    "App": {
        "SelfUrl": "https://localhost:44320"
    },
    "ConnectionStrings": {
        "Default": "Server=**********,1433;Database=TikTok;User ID=sa;Password=**********;TrustServerCertificate=true"
    },
    "StringEncryption": {
        "DefaultPassPhrase": "voSnn9O9TOgVB65s"
    },
    "TikTok": {
      "Auth": {
        "RedirectUri": "https://localhost:44320/TikTokAuthCallback",
        "UserId": "7527581526273721345" //QcMinh
      }
    },
    "NestJS": {
        "BaseUrl": "http://localhost:3000"
    },
    "MessageProviderOption": {
        "ApplicationName": "TikTok App",
      "Telegram": {
        "Enabled": true,
        "BotToken": "**********:AAErSjge9D1N7RHUvO2NZvXc9H6QVfyBVIM",
        "ChatId": "-*************",
        "AdAccountChatId": "-*************"
      }
    },
    "Elsa": {
        "Server": {
            "BaseUrl": "https://localhost:44320",
            "BasePath": "/workflows"
        },
        "DirectoryPath": "WorkflowDefinitions"
    },
    "BlobStorage": {
        "BasePath": "D:\\BlobFiles\\TikTok"
    },
    "Encryption": {
        "MasterKey": "0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF"
    },
    "APIClient": {
        "RateLimit": {
            "MaxWaitTime": 30,
            "RequestsPerWindow": 1,
            "WindowDuration": 2,
            "UseSlidingWindow": true
        },
      "Retry": {
        "BaseDelay": 1,
        "MaxDelay": 30,
        "MaxRetryAttempts": 3,
        "UseExponentialBackoff": true,
        "JitterFactor": 0.1
      }
    }
}
