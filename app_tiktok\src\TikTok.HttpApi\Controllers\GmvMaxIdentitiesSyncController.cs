using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using TikTok.DataSync;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Controllers
{
    /// <summary>
    /// Controller cho việc đồng bộ dữ liệu GMV Max Identities
    /// </summary>
    [RemoteService]
    [Route("api/app/gmv-max-identities-sync")]
    public class GmvMaxIdentitiesSyncController : AbpController
    {
        private readonly IGmvMaxIdentitiesSyncApplicationAppService _gmvMaxIdentitiesSyncApplicationAppService;

        public GmvMaxIdentitiesSyncController(
            IGmvMaxIdentitiesSyncApplicationAppService gmvMaxIdentitiesSyncApplicationAppService)
        {
            _gmvMaxIdentitiesSyncApplicationAppService = gmvMaxIdentitiesSyncApplicationAppService;
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> bộ GMV Max Identities theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync")]
        public async Task<GmvMaxIdentitiesSyncResult> SyncAsync([FromQuery] string advertiserId, [FromQuery] string bcId)
        {
            return await _gmvMaxIdentitiesSyncApplicationAppService.SyncGmvMaxIdentitiesAsync(advertiserId, bcId);
        }

        /// <summary>
        /// Đồng bộ nhiều GMV Max Identities cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-many")]
        public async Task<GmvMaxIdentitiesSyncResult> SyncManyAsync([FromQuery] string bcId, [FromBody] List<string>? advertiserIds = null)
        {
            return await _gmvMaxIdentitiesSyncApplicationAppService.SyncManyGmvMaxIdentitiesAsync(bcId, advertiserIds);
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Identities cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-all")]
        public async Task<GmvMaxIdentitiesSyncResult> SyncAllAsync()
        {
            return await _gmvMaxIdentitiesSyncApplicationAppService.SyncAllGmvMaxIdentitiesAsync();
        }
    }
}
