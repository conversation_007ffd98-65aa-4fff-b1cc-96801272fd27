using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace TikTok.JobManagement
{
    /// <summary>
    /// AppService để quản lý cấu hình loại công việc
    /// </summary>
    public class JobTypeConfigurationAppService : ApplicationService, IJobTypeConfigurationAppService
    {
        private readonly ILogger<JobTypeConfigurationAppService> _logger;
        private readonly IJobTypeConfigurationRepository _jobTypeConfigurationRepository;
        private readonly IGuidGenerator _guidGenerator;

        public JobTypeConfigurationAppService(
            ILogger<JobTypeConfigurationAppService> logger,
            IJobTypeConfigurationRepository jobTypeConfigurationRepository,
            IGuidGenerator guidGenerator)
        {
            _logger = logger;
            _jobTypeConfigurationRepository = jobTypeConfigurationRepository;
            _guidGenerator = guidGenerator;
        }

        /// <summary>
        /// Lấy danh sách tất cả cấu hình loại công việc
        /// </summary>
        /// <returns>Danh sách cấu hình</returns>
        public async Task<List<JobTypeConfigurationDto>> GetListAsync()
        {
            var configurations = await _jobTypeConfigurationRepository.GetListAsync();
            return ObjectMapper.Map<List<JobTypeConfigurationEntity>, List<JobTypeConfigurationDto>>(configurations);
        }

        /// <summary>
        /// Lấy cấu hình theo ID
        /// </summary>
        /// <param name="id">ID cấu hình</param>
        /// <returns>Cấu hình</returns>
        public async Task<JobTypeConfigurationDto> GetAsync(Guid id)
        {
            var configuration = await _jobTypeConfigurationRepository.GetAsync(id);
            return ObjectMapper.Map<JobTypeConfigurationEntity, JobTypeConfigurationDto>(configuration);
        }

        /// <summary>
        /// Lấy cấu hình theo CommandType
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Cấu hình</returns>
        public async Task<JobTypeConfigurationDto> GetByCommandTypeAsync(CommandType commandType)
        {
            var configuration = await _jobTypeConfigurationRepository.GetByCommandTypeAsync(commandType);
            return ObjectMapper.Map<JobTypeConfigurationEntity, JobTypeConfigurationDto>(configuration);
        }

        /// <summary>
        /// Tạo mới cấu hình
        /// </summary>
        /// <param name="input">Dữ liệu tạo mới</param>
        /// <returns>Cấu hình đã tạo</returns>
        public async Task<JobTypeConfigurationDto> CreateAsync(CreateJobTypeConfigurationDto input)
        {
            // Kiểm tra xem đã có cấu hình cho CommandType này chưa
            var exists = await _jobTypeConfigurationRepository.ExistsAsync(input.CommandType);
            if (exists)
            {
                throw new InvalidOperationException($"Configuration for CommandType {input.CommandType} already exists");
            }

            var configuration = new JobTypeConfigurationEntity(_guidGenerator.Create())
            {
                CommandType = input.CommandType,
                DisplayName = input.DisplayName,
                Description = input.Description,
                IntervalSeconds = input.IntervalSeconds,
                IsActive = input.IsActive,
                Priority = input.Priority,
                TimeoutMinutes = input.TimeoutMinutes,
                MaxRetryCount = input.MaxRetryCount
            };

            await _jobTypeConfigurationRepository.InsertAsync(configuration);

            _logger.LogDebug("Created job type configuration for {CommandType}", input.CommandType);

            return ObjectMapper.Map<JobTypeConfigurationEntity, JobTypeConfigurationDto>(configuration);
        }

        /// <summary>
        /// Cập nhật cấu hình
        /// </summary>
        /// <param name="id">ID cấu hình</param>
        /// <param name="input">Dữ liệu cập nhật</param>
        /// <returns>Cấu hình đã cập nhật</returns>
        public async Task<JobTypeConfigurationDto> UpdateAsync(Guid id, UpdateJobTypeConfigurationDto input)
        {
            var configuration = await _jobTypeConfigurationRepository.GetAsync(id);

            configuration.DisplayName = input.DisplayName;
            configuration.Description = input.Description;
            configuration.IntervalSeconds = input.IntervalSeconds;
            configuration.IsActive = input.IsActive;
            configuration.Priority = input.Priority;
            configuration.TimeoutMinutes = input.TimeoutMinutes;
            configuration.MaxRetryCount = input.MaxRetryCount;

            await _jobTypeConfigurationRepository.UpdateAsync(configuration);

            _logger.LogDebug("Updated job type configuration {Id} for {CommandType}", id, configuration.CommandType);

            return ObjectMapper.Map<JobTypeConfigurationEntity, JobTypeConfigurationDto>(configuration);
        }

        /// <summary>
        /// Xóa cấu hình
        /// </summary>
        /// <param name="id">ID cấu hình</param>
        public async Task DeleteAsync(Guid id)
        {
            var configuration = await _jobTypeConfigurationRepository.GetAsync(id);
            await _jobTypeConfigurationRepository.DeleteAsync(configuration);

            _logger.LogDebug("Deleted job type configuration {Id} for {CommandType}", id, configuration.CommandType);
        }

        /// <summary>
        /// Lấy danh sách cấu hình đang hoạt động
        /// </summary>
        /// <returns>Danh sách cấu hình</returns>
        public async Task<List<JobTypeConfigurationDto>> GetActiveConfigurationsAsync()
        {
            var configurations = await _jobTypeConfigurationRepository.GetActiveConfigurationsAsync();
            return ObjectMapper.Map<List<JobTypeConfigurationEntity>, List<JobTypeConfigurationDto>>(configurations);
        }

        /// <summary>
        /// Khởi tạo cấu hình mặc định cho tất cả CommandType
        /// </summary>
        public async Task InitializeDefaultConfigurationsAsync()
        {
            var existingConfigurations = await _jobTypeConfigurationRepository.GetListAsync();
            var existingCommandTypes = existingConfigurations.Select(c => c.CommandType).ToList();

            var allCommandTypes = Enum.GetValues<CommandType>();
            var missingCommandTypes = allCommandTypes.Where(ct => !existingCommandTypes.Contains(ct)).ToList();

            foreach (var commandType in missingCommandTypes)
            {
                var defaultConfig = CreateDefaultConfiguration(commandType);
                await _jobTypeConfigurationRepository.InsertAsync(defaultConfig);

                _logger.LogDebug("Created default configuration for CommandType {CommandType}", commandType);
            }
        }

        /// <summary>
        /// Tạo cấu hình mặc định cho CommandType
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Cấu hình mặc định</returns>
        private JobTypeConfigurationEntity CreateDefaultConfiguration(CommandType commandType)
        {
            var (displayName, description, intervalSeconds, priority) = GetDefaultValues(commandType);

            return new JobTypeConfigurationEntity(_guidGenerator.Create())
            {
                CommandType = commandType,
                DisplayName = displayName,
                Description = description,
                IntervalSeconds = intervalSeconds,
                IsActive = true,
                Priority = priority,
                TimeoutMinutes = null, // Sử dụng timeout chung
                MaxRetryCount = 3
            };
        }

        /// <summary>
        /// Lấy giá trị mặc định cho CommandType
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Tuple chứa (DisplayName, Description, IntervalSeconds, Priority)</returns>
        private (string displayName, string description, int intervalSeconds, int priority) GetDefaultValues(CommandType commandType)
        {
            return commandType switch
            {
                CommandType.SyncBusinessCenter => ("Đồng bộ Business Center", "Đồng bộ thông tin Business Center", 30 * 60, 1), // 30 phút
                CommandType.SyncTransaction => ("Đồng bộ Transaction", "Đồng bộ giao dịch", 5 * 60, 2), // 5 phút
                CommandType.SyncBalance => ("Đồng bộ Balance", "Đồng bộ số dư", 12 * 60 * 60, 3), // 12 tiếng
                CommandType.SyncAsset => ("Đồng bộ Asset", "Đồng bộ tài sản", 15 * 60, 4), // 15 phút
                CommandType.SyncCampaign => ("Đồng bộ Campaign", "Đồng bộ chiến dịch", 10 * 60, 5), // 10 phút
                CommandType.SyncReportIntegratedAdAccount => ("Đồng bộ Báo cáo AdAccount", "Đồng bộ báo cáo tích hợp AdAccount", 20 * 60, 6), // 20 phút
                CommandType.SyncReportIntegratedCampaign => ("Đồng bộ Báo cáo Campaign", "Đồng bộ báo cáo tích hợp Campaign", 25 * 60, 7), // 25 phút
                CommandType.SyncReportIntegratedAdGroup => ("Đồng bộ Báo cáo AdGroup", "Đồng bộ báo cáo tích hợp AdGroup", 30 * 60, 8), // 30 phút
                CommandType.SyncLatestBalance => ("Đồng bộ Balance mới nhất", "Đồng bộ số dư mới nhất", 5 * 60, 2), // 5 phút
                CommandType.SyncDetailedAdAccount => ("Đồng bộ chi tiết tài khoản quảng cáo", "Đồng bộ thông tin chi tiết tài khoản quảng cáo", 45 * 60, 4), // 45 phút
                CommandType.SyncGmvMax => ("Đồng bộ GMV Max", "Đồng bộ dữ liệu GMV Max", 60 * 60, 9), // 60 phút
                CommandType.SyncGmvMaxIdentities => ("Đồng bộ GMV Max Identities", "Đồng bộ dữ liệu GMV Max Identities", 12 * 60 * 60, 9), // 12 tiếng
                _ => ($"Đồng bộ {commandType}", $"Đồng bộ {commandType}", 60 * 60, 10) // 60 phút mặc định
            };
        }
    }
}
