﻿using Elsa.Activities.ControlFlow;
using MediatR.Pipeline;
using Microsoft.EntityFrameworkCore;
using Module.Notifications.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using TikTok.AdAccounts;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Domain.Entities.Rules;
using TikTok.Entities;
using TikTok.Entities.AdAccounts;
using TikTok.Entities.Apps;
using TikTok.Entities.Dim;
using TikTok.Entities.Permissions;
using TikTok.Entities.Raws;
using Tsp.Zalo.Entities;
using Tsp.Zalo.EntityFrameworkCore;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using TikTok.Entities.Dim;
using TikTok.Domain.Entities.SystemNotification;

namespace TikTok.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ReplaceDbContext(typeof(IZaloDbContext))]
[ReplaceDbContext(typeof(INotificationsDbContext))]
[ConnectionStringName("Default")]
public class TikTokDbContext :
    AbpDbContext<TikTokDbContext>,
    IIdentityDbContext,
    ITenantManagementDbContext,
    IZaloDbContext,
    INotificationsDbContext
{
    #region Raw

    /// <summary>
    /// DbSet cho trung tâm kinh doanh
    /// </summary>
    public DbSet<RawBusinessCenterEntity> RawBusinessCenters { get; set; }

    /// <summary>
    /// DbSet cho tài khoản quảng cáo
    /// </summary>
    public DbSet<RawAdAccountEntity> RawAdAccounts { get; set; }

    /// <summary>
    /// DbSet cho nhóm quảng cáo
    /// </summary>
    public DbSet<RawAdGroupEntity> RawAdGroups { get; set; }

    /// <summary>
    /// DbSet cho tài sản
    /// </summary>
    public DbSet<RawAssetEntity> RawAssets { get; set; }

    /// <summary>
    /// DbSet cho giao dịch
    /// </summary>
    public DbSet<RawTransactionEntity> RawTransactions { get; set; }


    /// <summary>
    /// DbSet cho ngân sách Business Center
    /// Name In Database: Raw_RawBalanceBusinessCenters
    /// </summary>
    public DbSet<RawBalanceBusinessCenterEntity> RawBalanceBusinessCenters { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Campaigns
    /// Name In Database: Raw_RawGmvMaxCampaigns
    /// </summary>
    public DbSet<RawGmvMaxCampaignsEntity> RawGmvMaxCampaigns { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Product Campaign Reports
    /// Name In Database: Raw_RawGmvMaxProductCampaignReports
    /// </summary>
    public DbSet<RawGmvMaxProductCampaignReportEntity> RawGmvMaxProductCampaignReports { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Product Detail Product Reports
    /// Name In Database: Raw_RawGmvMaxProductDetailProductReports
    /// </summary>
    public DbSet<RawGmvMaxProductDetailProductReportEntity> RawGmvMaxProductDetailProductReports { get; set; }

    ///// <summary>
    ///// DbSet cho GMV Max Identities
    ///// Name In Database: RawGmvMaxIdentities
    ///// </summary>
    public DbSet<RawGmvMaxIdentitiesEntity> RawGmvMaxIdentities { get; set; }
    /// <summary>
    /// DbSet cho TikTok Shop Products
    /// </summary>
    public DbSet<RawTikTokShopProductsEntity> RawTikTokShopProducts { get; set; }
    /// <summary>
    /// DbSet cho GMV Max Campaign Posts
    /// </summary>
    public DbSet<RawGmvMaxCampaignPostsEntity> RawGmvMaxCampaignPosts { get; set; }
    /// <summary>
    /// DbSet cho GMV Max Product Creative Reports
    /// Name In Database: Raw_RawGmvMaxProductCreativeReports
    /// </summary>
    public DbSet<RawGmvMaxProductCreativeReportEntity> RawGmvMaxProductCreativeReports { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Live Campaign Reports
    /// Name In Database: Raw_RawGmvMaxLiveCampaignReports
    /// </summary>
    public DbSet<RawGmvMaxLiveCampaignReportEntity> RawGmvMaxLiveCampaignReports { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Live Detail Livestream Reports
    /// Name In Database: Raw_RawGmvMaxLiveDetailLivestreamReports
    /// </summary>
    public DbSet<RawGmvMaxLiveDetailLivestreamReportEntity> RawGmvMaxLiveDetailLivestreamReports { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Campaign Identities
    /// Name In Database: Raw_RawGmvMaxCampaignIdentities
    /// </summary>
    public DbSet<RawGmvMaxCampaignIdentitiesEntity> RawGmvMaxCampaignIdentities { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Campaign Items
    /// Name In Database: Raw_RawGmvMaxCampaignItems
    /// </summary>
    public DbSet<RawGmvMaxCampaignItemsEntity> RawGmvMaxCampaignItems { get; set; }

    /// <summary>
    /// DbSet cho GMV Max Campaign Custom Anchor Videos
    /// Name In Database: Raw_RawGmvMaxCampaignCustomAnchorVideos
    /// </summary>
    public DbSet<RawGmvMaxCampaignCustomAnchorVideosEntity> RawGmvMaxCampaignCustomAnchorVideos { get; set; }

    /// <summary>
    /// DbSet cho số dư và ngân sách tài khoản nhà quảng cáo
    /// Name In Database: Raw_RawBalanceAdAccounts
    /// </summary>
    public DbSet<RawBalanceAdAccountEntity> RawBalanceAdAccounts { get; set; }

    /// <summary>
    /// DbSet cho ngân sách mới nhất của Business Center
    /// Name In Database: Raw_RawLatestBalanceBusinessCenters
    /// </summary>
    public DbSet<RawLatestBalanceBusinessCenterEntity> RawLatestBalanceBusinessCenters { get; set; }

    /// <summary>
    /// DbSet cho số dư và ngân sách mới nhất của tài khoản nhà quảng cáo
    /// Name In Database: Raw_RawLatestBalanceAdAccounts
    /// </summary>
    public DbSet<RawLatestBalanceAdAccountEntity> RawLatestBalanceAdAccounts { get; set; }

    /// <summary>
    /// DbSet cho chiến dịch
    /// </summary>
    public DbSet<RawCampaignEntity> RawCampaigns { get; set; }

    /// <summary>
    /// DbSet cho hồ sơ chi phí
    /// </summary>
    public DbSet<RawCostProfileEntity> RawCostProfiles { get; set; }

    /// <summary>
    /// DbSet cho bản ghi giao dịch tài khoản quảng cáo
    /// </summary>
    public DbSet<RawRecordTransactionAdAccountEntity> RecordTransactionAdAccounts { get; set; }

    /// <summary>
    /// DbSet cho bản ghi giao dịch BC
    /// </summary>
    public DbSet<RawRecordTransactionBcEntity> RecordTransactionBcs { get; set; }

    /// <summary>
    /// DbSet cho báo cáo tích hợp Business Center
    /// </summary>
    public DbSet<RawReportIntegratedBcEntity> RawReportIntegratedBcs { get; set; }

    /// <summary>
    /// DbSet cho báo cáo tích hợp Campaign
    /// </summary>
    public DbSet<RawReportIntegratedCampaignEntity> RawReportIntegratedCampaigns { get; set; }

    /// <summary>
    /// DbSet cho báo cáo tích hợp AdAccount
    /// </summary>
    public DbSet<RawReportIntegratedAdAccountEntity> RawReportIntegratedAdAccounts { get; set; }
    public DbSet<RawReportIntegratedAdGroupEntity> RawReportIntegratedAdGroups { get; set; }
    public DbSet<RawReportIntegratedAdEntity> RawReportIntegratedAds { get; set; }


    #endregion

    #region Dimension

    /// <summary>
    /// DbSet cho dimension Business Center
    /// </summary>
    public DbSet<DimBusinessCenterEntity> DimBusinessCenters { get; set; }

    /// <summary>
    /// DbSet cho dimension Ad Account
    /// </summary>
    public DbSet<DimAdAccountEntity> DimAdAccounts { get; set; }

    /// <summary>
    /// DbSet cho dimension Date
    /// </summary>
    public DbSet<DimDateEntity> DimDates { get; set; }

    /// <summary>
    /// DbSet cho dimension Campaign
    /// </summary>
    public DbSet<DimCampaignEntity> DimCampaigns { get; set; }

    /// <summary>
    /// DbSet cho dimension Transaction Type
    /// </summary>
    public DbSet<DimTransactionTypeEntity> DimTransactionTypes { get; set; }

    /// <summary>
    /// DbSet cho dimension Store (TikTok Shop)
    /// </summary>
    public DbSet<DimStoreEntity> DimStores { get; set; }

    /// <summary>
    /// DbSet cho dimension Product (TikTok Shop)
    /// </summary>
    public DbSet<DimProductEntity> DimProducts { get; set; }

    public DbSet<DimTTAccountEntity> DimTTAccounts { get; set; }
    #endregion

    #region Fact

    /// <summary>
    /// DbSet cho fact Balance
    /// </summary>
    public DbSet<FactBalanceEntity> FactBalances { get; set; }

    /// <summary>
    /// DbSet cho fact Daily Spend
    /// </summary>
    public DbSet<FactDailySpendEntity> FactDailySpends { get; set; }

    /// <summary>
    /// DbSet cho fact Transaction
    /// </summary>
    public DbSet<FactTransactionEntity> FactTransactions { get; set; }

    /// <summary>
    /// DbSet cho fact Campaign
    /// </summary>
    public DbSet<FactCampaignEntity> FactCampaigns { get; set; }

    /// <summary>
    /// DbSet cho fact Advertisement
    /// </summary>
    public DbSet<FactAdvertisementEntity> FactAdvertisements { get; set; }

    /// <summary>
    /// DbSet cho fact GMV Max Campaign
    /// </summary>
    public DbSet<FactGmvMaxCampaignEntity> FactGmvMaxCampaigns { get; set; }

    /// <summary>
    /// DbSet cho fact GMV Max Product
    /// </summary>
    public DbSet<FactGmvMaxProductEntity> FactGmvMaxProducts { get; set; }

    /// <summary>
    /// Db set cho fact creative của GMV Max Product
    /// </summary>
    public DbSet<FactGmvMaxProductCreativeEntity> FactGmvMaxProductCreatives { get; set; }
    #endregion

    /* Add DbSet properties for your Aggregate Roots / Entities here. */

    /// <summary>
    /// DbSet cho ứng dụng Business
    /// </summary>
    public DbSet<BusinessApplicationEntity> BusinessApplications { get; set; }

    /// <summary>
    /// DbSet cho tỷ giá quy đổi tiền tệ
    /// </summary>
    public DbSet<CurrencyExchangeRateEntity> CurrencyExchangeRates { get; set; }

    /// <summary>
    /// DbSet cho trạng thái ETL NIFI flow
    /// </summary>
    public DbSet<EtlFlowStatusEntity> EtlFlowStatuses { get; set; }

    #region Background job
    /// <summary>
    /// DbSet cho cấu hình job
    /// </summary>
    public DbSet<JobConfigurationEntity> JobConfigurations { get; set; }

    /// <summary>
    /// DbSet cho thông tin worker
    /// </summary>
    public DbSet<WorkerInfoEntity> WorkerInfos { get; set; }

    /// <summary>
    /// DbSet cho công việc
    /// </summary>
    public DbSet<JobEntity> Jobs { get; set; }
    public DbSet<JobTypeConfigurationEntity> JobTypeConfigurations { get; set; }

    /// <summary>
    /// DbSet cho khách hàng
    /// </summary>
    public DbSet<CustomerEntity> Customers { get; set; }

    /// <summary>
    /// DbSet cho tài khoản quảng cáo của khách hàng
    /// </summary>
    public DbSet<CustomerAdAccountEntity> CustomerAdAccounts { get; set; }

    /// <summary>
    /// DbSet cho thông báo mới nhất
    /// </summary>
    public DbSet<LatestNotificationEntity> LatestNotifications { get; set; }
    #endregion

    #region Notifications Module

    /// <summary>
    /// DbSet cho notifications từ TSP Module
    /// </summary>
    public DbSet<Module.Notifications.Context.NotificationEntity> Notifications { get; set; }
    public DbSet<Module.Notifications.Context.UserDevicesEntity> UserDevices { get; set; }
    #endregion

    #region Entities from the modules

    /* Notice: We only implemented IIdentityDbContext and ITenantManagementDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityDbContext and ITenantManagementDbContext.
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    //Identity
    public DbSet<IdentityUser> Users { get; set; }
    public DbSet<IdentityRole> Roles { get; set; }
    public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    public DbSet<IdentityUserDelegation> UserDelegations { get; set; }

    // Tenant Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }
    public DbSet<FailedMessage> FailedMessages { get; set; }
    public DbSet<ZaloCookie> ZaloCookies { get; set; }
    public DbSet<ChatMessage> ChatMessages { get; set; }
    public DbSet<ChatAttachment> ChatAttachments { get; set; }
    public DbSet<Template> Templates { get; set; }
    public DbSet<ChatCommandSetting> ChatCommandSettings { get; set; }
    public DbSet<AccountUser> AccountUsers { get; set; }

    #endregion


    #region TIKTOK_MANAGEMENT
    public DbSet<AdAccountSupporterEntity> AdAccountSupporters { get; set; }
    public DbSet<RuleEntity> RuleEntities { get; set; }
    public DbSet<RuleAdAccountEntity> RuleAdAccounts { get; set; }
    public DbSet<ResourcePermissionEntity> ResourcePermissions { get; set; }
    public DbSet<NotificationAttachment> NotificationAttachment { get; set; }
    public DbSet<NotificationHistory> NotificationHistories { get; set; }
    
    /// <summary>
    /// DbSet cho System Notification Rules
    /// </summary>
    public DbSet<SystemNotificationRule> SystemNotificationRules { get; set; }

    /// <summary>
    /// DbSet cho System Notification Rule Consumers
    /// </summary>
    public DbSet<SystemNotificationRuleConsumerEntity> SystemNotificationRuleConsumers { get; set; }
    #endregion

    public TikTokDbContext(DbContextOptions<TikTokDbContext> options)
        : base(options)
    {

    }


    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigurePermissionManagement();
        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureFeatureManagement();
        builder.ConfigureTenantManagement();
        builder.ConfigureZalo();
        /* Configure your own tables/entities inside here */

        //builder.Entity<YourEntity>(b =>
        //{
        //    b.ToTable(TikTokConsts.DbTablePrefix + "YourEntities", TikTokConsts.DbSchema);
        //    b.ConfigureByConvention(); //auto configure for the base class props
        //    //...
        //});

        #region Raw
        builder.Entity<RawBusinessCenterEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawBusinessCenters", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        builder.Entity<RawAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.Balance).HasPrecision(18, 2);
        });

        builder.Entity<RawAssetEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawAssets", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
            // Configure TtAccountRoles as JSON column
            b.Property(e => e.TtAccountRoles)
                .HasConversion(
                    v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => System.Text.Json.JsonSerializer.Deserialize<List<TikTok.Enums.TtAccountRole>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<TikTok.Enums.TtAccountRole>())
                .HasColumnType("nvarchar(max)");
        });

        builder.Entity<RawTransactionEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawTransactions", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.Amount).HasPrecision(18, 2);
            b.Property(e => e.Subtotal).HasPrecision(18, 2);
            b.Property(e => e.TaxAmount).HasPrecision(18, 2);
        });

        builder.Entity<RawBalanceBusinessCenterEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawBalanceBusinessCenters", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
            // Cấu hình decimal precision cho các trường số dư
            b.Property(e => e.AccountBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalance).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalance).HasPrecision(18, 2);
            b.Property(e => e.Tax).HasPrecision(18, 2);
            b.Property(e => e.CashBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalance).HasPrecision(18, 2);
            b.Property(e => e.GrantBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalance).HasPrecision(18, 2);
        });
        builder.Ignore<BudgetFrequencyRestriction>();
        builder.Ignore<BudgetAmountRestriction>();
        builder.Ignore<MinTransferableAmount>();
        builder.Entity<RawBalanceAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawBalanceAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Cấu hình decimal precision cho các trường số dư
            b.Property(e => e.AccountBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalance).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalance).HasPrecision(18, 2);
            b.Property(e => e.Tax).HasPrecision(18, 2);
            b.Property(e => e.CashBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalance).HasPrecision(18, 2);
            b.Property(e => e.GrantBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalance).HasPrecision(18, 2);
            b.Property(e => e.TransferableAmount).HasPrecision(18, 2);
            b.Property(e => e.Budget).HasPrecision(18, 2);
            b.Property(e => e.BudgetCost).HasPrecision(18, 2);
            b.Property(e => e.BudgetRemaining).HasPrecision(18, 2);
            b.Property(e => e.FirstRechargeAmount).HasPrecision(18, 2);
            b.Property(e => e.RechargeAmount).HasPrecision(18, 2);

            b.OwnsOne(e => e.BudgetFrequencyRestriction);
            b.OwnsOne(e => e.BudgetAmountRestriction);
            b.OwnsOne(e => e.MinTransferableAmount);
        });

        builder.Entity<RawLatestBalanceBusinessCenterEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawLatestBalanceBusinessCenters", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Cấu hình decimal precision cho các trường số dư
            b.Property(e => e.AccountBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalance).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalance).HasPrecision(18, 2);
            b.Property(e => e.Tax).HasPrecision(18, 2);
            b.Property(e => e.CashBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalance).HasPrecision(18, 2);
            b.Property(e => e.GrantBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalance).HasPrecision(18, 2);

            // Cấu hình unique index cho BcId để đảm bảo chỉ có một bản ghi cho mỗi BC
            b.HasIndex(e => e.BcId).IsUnique();

            // Cấu hình index cho các trường thường được query
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
        });

        builder.Entity<RawLatestBalanceAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawLatestBalanceAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Cấu hình decimal precision cho các trường số dư
            b.Property(e => e.AccountBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalance).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalance).HasPrecision(18, 2);
            b.Property(e => e.Tax).HasPrecision(18, 2);
            b.Property(e => e.CashBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalance).HasPrecision(18, 2);
            b.Property(e => e.GrantBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalance).HasPrecision(18, 2);
            b.Property(e => e.TransferableAmount).HasPrecision(18, 2);
            b.Property(e => e.Budget).HasPrecision(18, 2);
            b.Property(e => e.BudgetCost).HasPrecision(18, 2);
            b.Property(e => e.BudgetRemaining).HasPrecision(18, 2);
            b.Property(e => e.FirstRechargeAmount).HasPrecision(18, 2);
            b.Property(e => e.RechargeAmount).HasPrecision(18, 2);

            // Cấu hình unique index cho AdvertiserId để đảm bảo chỉ có một bản ghi cho mỗi advertiser
            b.HasIndex(e => e.AdvertiserId).IsUnique();

            // Cấu hình index cho các trường thường được query
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdvertiserName);
            b.HasIndex(e => e.AdvertiserStatus);
            b.HasIndex(e => e.AdvertiserType);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);

            b.OwnsOne(e => e.BudgetFrequencyRestriction);
            b.OwnsOne(e => e.BudgetAmountRestriction);
            b.OwnsOne(e => e.MinTransferableAmount);
        });

        builder.Entity<RawCampaignEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawCampaigns", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.RoasBid).HasPrecision(18, 2);
            b.Property(e => e.Budget).HasPrecision(18, 2);

            // Configure SpecialIndustries as JSON column
            b.Property(e => e.SpecialIndustries)
                .HasConversion(
                    v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => System.Text.Json.JsonSerializer.Deserialize<List<TikTok.Enums.SpecialIndustries>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<TikTok.Enums.SpecialIndustries>())
                .HasColumnType("nvarchar(max)");

            // Configure indexes for better performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.CampaignName);
            b.HasIndex(e => e.ObjectiveType);
            b.HasIndex(e => e.CampaignType);
            b.HasIndex(e => e.OperationStatus);
            b.HasIndex(e => e.CreateTime);
            b.HasIndex(e => e.ModifyTime);
        });

        builder.Entity<RawAdGroupEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawAdGroups", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for monetary values
            b.Property(e => e.Budget).HasPrecision(18, 2);
            b.Property(e => e.ScheduledBudget).HasPrecision(18, 2);
            b.Property(e => e.PreDiscountCpm).HasPrecision(18, 2);
            b.Property(e => e.Cpm).HasPrecision(18, 2);
            b.Property(e => e.DiscountAmount).HasPrecision(18, 2);
            b.Property(e => e.DiscountPercentage).HasPrecision(18, 4);
            b.Property(e => e.PreDiscountBudget).HasPrecision(18, 2);
            b.Property(e => e.BidPrice).HasPrecision(18, 2);
            b.Property(e => e.ConversionBidPrice).HasPrecision(18, 2);
            b.Property(e => e.RoasBid).HasPrecision(18, 2);
            b.Property(e => e.DeepCpaBid).HasPrecision(18, 2);
            b.Property(e => e.NextDayRetention).HasPrecision(18, 4);
            b.Property(e => e.RfEstimatedCpr).HasPrecision(18, 2);
            b.Property(e => e.RfEstimatedFrequency).HasPrecision(18, 4);

            // Configure JSON columns for complex data types
            b.Property(e => e.PlacementsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.TiktokSubplacementsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.SearchKeywordsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.BlockedPangleAppIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.AudienceRuleJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.IncludedCustomActionsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ExcludedCustomActionsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.LocationIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ZipcodeIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.LanguagesJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.AgeGroupsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.HouseholdIncomeJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.AudienceIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ExcludedAudienceIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.InterestCategoryIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.InterestKeywordIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.PurchaseIntentionKeywordIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ActionsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.IncludedPangleAudiencePackageIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ExcludedPangleAudiencePackageIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.OperatingSystemsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.DeviceModelIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.NetworkTypesJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.CarrierIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.IspIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.DevicePriceRangesJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.TargetingExpansionJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ContextualTagIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.CategoryExclusionIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.TopviewReachRangeJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ScheduleInfosJson).HasColumnType("nvarchar(max)");

            // Configure deprecated JSON columns
            b.Property(e => e.GendersJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.BehaviorCategoryIdsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.DeviceModelsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.DevicePriceBucketsJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.CustomAudiencesJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ExcludedCustomAudiencesJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.LookalikeAudiencesJson).HasColumnType("nvarchar(max)");
            b.Property(e => e.ExcludedLookalikeAudiencesJson).HasColumnType("nvarchar(max)");

            // Configure indexes for better query performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.AdgroupId);
            b.HasIndex(e => e.AdgroupName);
            b.HasIndex(e => e.OperationStatus);
            b.HasIndex(e => e.SecondaryStatus);
            b.HasIndex(e => e.CreateTime);
            b.HasIndex(e => e.ModifyTime);
            b.HasIndex(e => e.ScheduleStartTime);
            b.HasIndex(e => e.ScheduleEndTime);
            b.HasIndex(e => e.Budget);
            b.HasIndex(e => e.BidType);
            b.HasIndex(e => e.OptimizationGoal);
            b.HasIndex(e => e.PlacementType);
            b.HasIndex(e => e.ShoppingAdsType);
            b.HasIndex(e => e.IdentityId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.CatalogId);
            b.HasIndex(e => e.PixelId);
            b.HasIndex(e => e.AppId);

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.AdvertiserId, e.CampaignId });
            b.HasIndex(e => new { e.AdvertiserId, e.OperationStatus });
            b.HasIndex(e => new { e.CampaignId, e.OperationStatus });
            b.HasIndex(e => new { e.AdgroupId, e.OperationStatus });

            // Configure unique index on AdgroupId to prevent duplicates
            b.HasIndex(e => e.AdgroupId).IsUnique();
        });

        builder.Entity<RawCostProfileEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawCostProfiles", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.Amount).HasPrecision(18, 2);
            b.Property(e => e.CashAmount).HasPrecision(18, 2);
            b.Property(e => e.GrantAmount).HasPrecision(18, 2);
            b.Property(e => e.TaxAmount).HasPrecision(18, 2);

            // Configure indexes for better performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.AdvertiserName);
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
        });

        builder.Entity<RawRecordTransactionAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RecordTransactionAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.Amount).HasPrecision(18, 2);

            // Configure composite key (AdvertiserId + Date)
            b.HasIndex(e => new { e.AdvertiserId, e.Date }).IsUnique();
        });

        builder.Entity<RawRecordTransactionBcEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RecordTransactionBcs", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.Amount).HasPrecision(18, 2);

            // Configure unique index on Date to prevent duplicate transactions
            // is unique BcId and Date
            b.HasIndex(e => new { e.BcId, e.Date }).IsUnique();
        });

        builder.Entity<RawReportIntegratedBcEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawReportIntegratedBcs", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all decimal properties
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.BilledCost).HasPrecision(18, 2);
            b.Property(e => e.CashSpend).HasPrecision(18, 2);
            b.Property(e => e.VoucherSpend).HasPrecision(18, 2);
            b.Property(e => e.CashbackCouponSpend).HasPrecision(18, 2);
            b.Property(e => e.TaxSpend).HasPrecision(18, 2);
            b.Property(e => e.Cpc).HasPrecision(18, 2);
            b.Property(e => e.Cpm).HasPrecision(18, 2);
            b.Property(e => e.Ctr).HasPrecision(18, 4);
            b.Property(e => e.CostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.ConversionRate).HasPrecision(18, 4);
            b.Property(e => e.RealTimeCostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.RealTimeConversionRate).HasPrecision(18, 4);
            b.Property(e => e.SkanCostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.SkanConversionRate).HasPrecision(18, 4);

            // Configure composite unique index on BcId and Date to prevent duplicate reports
            b.HasIndex(e => new { e.BcId, e.Date }).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.Impressions);
            b.HasIndex(e => e.Clicks);
            b.HasIndex(e => e.Conversion);
        });

        builder.Entity<RawReportIntegratedCampaignEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawReportIntegratedCampaigns", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all decimal properties
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.CashSpend).HasPrecision(18, 2);
            b.Property(e => e.VoucherSpend).HasPrecision(18, 2);
            b.Property(e => e.CostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.ConversionRateV2).HasPrecision(18, 4);
            b.Property(e => e.CostPerResult).HasPrecision(18, 2);
            b.Property(e => e.OnsiteShoppingRoas).HasPrecision(18, 4);
            b.Property(e => e.TotalOnsiteShoppingValue).HasPrecision(18, 2);
            b.Property(e => e.CostPerOnsiteShopping).HasPrecision(18, 2);
            b.Property(e => e.ValuePerOnsiteShopping).HasPrecision(18, 2);

            // Configure composite unique index on CampaignId and Date to prevent duplicate reports
            b.HasIndex(e => new { e.CampaignId, e.Date }).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.Impressions);
            b.HasIndex(e => e.Clicks);
            b.HasIndex(e => e.Conversion);
        });

        builder.Entity<RawReportIntegratedAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawReportIntegratedAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all decimal properties
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.CashSpend).HasPrecision(18, 2);
            b.Property(e => e.VoucherSpend).HasPrecision(18, 2);
            b.Property(e => e.BilledCost).HasPrecision(18, 2);
            b.Property(e => e.Ctr).HasPrecision(18, 4);
            b.Property(e => e.Cpm).HasPrecision(18, 2);
            b.Property(e => e.Cpc).HasPrecision(18, 2);
            b.Property(e => e.Frequency).HasPrecision(18, 4);

            // Configure composite unique index on AdvertiserId and Date to prevent duplicate reports
            b.HasIndex(e => new { e.AdvertiserId, e.Date }).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.Impressions);
            b.HasIndex(e => e.Clicks);
            b.HasIndex(e => e.BcId);
        });

        builder.Entity<RawReportIntegratedAdGroupEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawReportIntegratedAdGroups", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all decimal properties
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.Ctr).HasPrecision(18, 4);
            b.Property(e => e.Cpm).HasPrecision(18, 2);
            b.Property(e => e.Cpc).HasPrecision(18, 2);
            b.Property(e => e.CostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.ConversionRateV2).HasPrecision(18, 4);
            b.Property(e => e.Frequency).HasPrecision(18, 4);
            b.Property(e => e.OnsiteShoppingRoas).HasPrecision(18, 4);
            b.Property(e => e.TotalOnsiteShoppingValue).HasPrecision(18, 2);
            b.Property(e => e.CostPerOnsiteShopping).HasPrecision(18, 2);
            b.Property(e => e.ValuePerOnsiteShopping).HasPrecision(18, 2);

            // Configure composite unique index on AdGroupId and Date to prevent duplicate reports
            b.HasIndex(e => new { e.AdGroupId, e.Date }).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.AdGroupId);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.Impressions);
            b.HasIndex(e => e.Clicks);
            b.HasIndex(e => e.Conversion);
        });

        builder.Entity<RawReportIntegratedAdEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawReportIntegratedAds", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all decimal properties
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.Ctr).HasPrecision(18, 4);
            b.Property(e => e.Cpm).HasPrecision(18, 2);
            b.Property(e => e.Cpc).HasPrecision(18, 2);
            b.Property(e => e.CostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.Frequency).HasPrecision(18, 4);
            b.Property(e => e.AverageVideoPlay).HasPrecision(18, 2);
            b.Property(e => e.OnsiteShoppingRoas).HasPrecision(18, 4);
            b.Property(e => e.TotalOnsiteShoppingValue).HasPrecision(18, 2);
            b.Property(e => e.CostPerOnsiteShopping).HasPrecision(18, 2);
            b.Property(e => e.ValuePerOnsiteShopping).HasPrecision(18, 2);

            // Configure composite unique index on AdId and Date to prevent duplicate reports
            b.HasIndex(e => new { e.AdId, e.Date }).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.AdGroupId);
            b.HasIndex(e => e.AdId);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.Impressions);
            b.HasIndex(e => e.Clicks);
            b.HasIndex(e => e.Conversion);
        });

        builder.Entity<RawGmvMaxCampaignsEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxCampaigns", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.RoasBid).HasPrecision(10, 2);
            b.Property(e => e.Budget).HasPrecision(15, 2);

            // Configure enum conversions
            b.Property(e => e.RoiProtectionCompensationStatus).HasConversion<int>();
            b.Property(e => e.ShoppingAdsType).HasConversion<int>();
            b.Property(e => e.ProductSpecificType).HasConversion<int>();
            b.Property(e => e.OptimizationGoal).HasConversion<int>();
            b.Property(e => e.DeepBidType).HasConversion<int>();
            b.Property(e => e.ScheduleType).HasConversion<int>();
            b.Property(e => e.ProductVideoSpecificType).HasConversion<int>();

            // Configure List<string> properties as JSON columns
            b.Property(e => e.Placements)
                .HasConversion(
                    v => v == null ? null : System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => v == null ? null : System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null))
                .HasColumnType("nvarchar(max)");

            b.Property(e => e.LocationIds)
                .HasConversion(
                    v => v == null ? null : System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => v == null ? null : System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null))
                .HasColumnType("nvarchar(max)");

            b.Property(e => e.AgeGroups)
                .HasConversion(
                    v => v == null ? null : System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => v == null ? null : System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null))
                .HasColumnType("nvarchar(max)");

            b.Property(e => e.ItemGroupIds)
                .HasConversion(
                    v => v == null ? null : System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => v == null ? null : System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null))
                .HasColumnType("nvarchar(max)");

            b.HasIndex(e => e.CampaignId);

            // Configure indexes for better performance
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.OperationStatus);
            b.HasIndex(e => e.ObjectiveType);
            b.HasIndex(e => e.ShoppingAdsType);
            b.HasIndex(e => e.OptimizationGoal);
            b.HasIndex(e => e.CreateTime);
            b.HasIndex(e => e.ModifyTime);
            b.HasIndex(e => e.SyncedAt);
            b.HasIndex(e => e.Budget);
            b.HasIndex(e => e.ScheduleStartTime);
            b.HasIndex(e => e.ScheduleEndTime);
        });

        builder.Entity<RawGmvMaxProductCampaignReportEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxProductCampaignReports", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.TargetRoiBudget).HasPrecision(15, 2);
            b.Property(e => e.MaxDeliveryBudget).HasPrecision(15, 2);
            b.Property(e => e.RoasBid).HasPrecision(10, 2);
            b.Property(e => e.Cost).HasPrecision(15, 2);
            b.Property(e => e.NetCost).HasPrecision(15, 2);
            b.Property(e => e.CostPerOrder).HasPrecision(15, 2);
            b.Property(e => e.GrossRevenue).HasPrecision(15, 2);
            b.Property(e => e.ROI).HasPrecision(10, 4);

            // Configure indexes for better performance
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.OperationStatus);
            b.HasIndex(e => e.ScheduleType);
            b.HasIndex(e => e.BidType);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.ScheduleStartTime);
            b.HasIndex(e => e.ScheduleEndTime);
            b.HasIndex(e => e.Cost);
            b.HasIndex(e => e.Orders);
            b.HasIndex(e => e.GrossRevenue);
            b.HasIndex(e => e.ROI);

            // Configure composite index for unique report identification
            b.HasIndex(e => new { e.CampaignId, e.Date });
        });

        builder.Entity<RawGmvMaxProductDetailProductReportEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxProductDetailProductReports", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.GrossRevenue).HasPrecision(15, 2);

            // Configure indexes for better performance
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.ItemGroupId);
            b.HasIndex(e => e.ProductStatus);
            b.HasIndex(e => e.BidType);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Orders);
            b.HasIndex(e => e.GrossRevenue);

            // Configure composite index for unique product report identification
            b.HasIndex(e => new { e.CampaignId, e.ItemGroupId, e.Date });
        });

        builder.Entity<RawGmvMaxProductCreativeReportEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxProductCreativeReports", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.GrossRevenue).HasPrecision(15, 2);
            b.Property(e => e.ProductClickRate).HasPrecision(10, 4);
            b.Property(e => e.AdClickRate).HasPrecision(10, 4);
            b.Property(e => e.AdConversionRate).HasPrecision(10, 4);
            b.Property(e => e.AdVideoViewRate2s).HasPrecision(10, 4);
            b.Property(e => e.AdVideoViewRate6s).HasPrecision(10, 4);
            b.Property(e => e.AdVideoViewRateP25).HasPrecision(10, 4);
            b.Property(e => e.AdVideoViewRateP50).HasPrecision(10, 4);
            b.Property(e => e.AdVideoViewRateP75).HasPrecision(10, 4);
            b.Property(e => e.AdVideoViewRateP100).HasPrecision(10, 4);

            // Configure enum conversions
            b.Property(e => e.CreativeType).HasConversion<int>();
            b.Property(e => e.TtAccountAuthorizationType).HasConversion<int>();
            b.Property(e => e.ShopContentType).HasConversion<int>();

            // Configure indexes for better performance
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.ItemGroupId);
            b.HasIndex(e => e.ItemId);
            b.HasIndex(e => e.CreativeType);
            b.HasIndex(e => e.TtAccountAuthorizationType);
            b.HasIndex(e => e.ShopContentType);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Orders);
            b.HasIndex(e => e.GrossRevenue);
            b.HasIndex(e => e.ProductImpressions);
            b.HasIndex(e => e.ProductClicks);

            // Configure composite index for unique creative report identification
            b.HasIndex(e => new { e.CampaignId, e.ItemGroupId, e.ItemId, e.Date });
        });

        builder.Entity<RawGmvMaxLiveCampaignReportEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxLiveCampaignReports", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.TargetRoiBudget).HasPrecision(15, 2);
            b.Property(e => e.MaxDeliveryBudget).HasPrecision(15, 2);
            b.Property(e => e.RoasBid).HasPrecision(10, 2);
            b.Property(e => e.Cost).HasPrecision(15, 2);
            b.Property(e => e.NetCost).HasPrecision(15, 2);
            b.Property(e => e.CostPerOrder).HasPrecision(15, 2);
            b.Property(e => e.GrossRevenue).HasPrecision(15, 2);
            b.Property(e => e.ROI).HasPrecision(10, 4);
            b.Property(e => e.CostPerLiveView).HasPrecision(15, 4);
            b.Property(e => e.CostPerTenSecondLiveView).HasPrecision(15, 4);

            // Configure indexes for better performance
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.OperationStatus);
            b.HasIndex(e => e.IdentityId);
            b.HasIndex(e => e.BidType);
            b.HasIndex(e => e.ScheduleType);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.ScheduleStartTime);
            b.HasIndex(e => e.ScheduleEndTime);
            b.HasIndex(e => e.Cost);
            b.HasIndex(e => e.Orders);
            b.HasIndex(e => e.GrossRevenue);
            b.HasIndex(e => e.ROI);
            b.HasIndex(e => e.LiveViews);
            b.HasIndex(e => e.TenSecondLiveViews);
            b.HasIndex(e => e.LiveFollows);

            // Configure composite index for unique report identification
            b.HasIndex(e => new { e.CampaignId, e.Date });
        });

        builder.Entity<RawGmvMaxLiveDetailLivestreamReportEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxLiveDetailLivestreamReports", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision
            b.Property(e => e.Cost).HasPrecision(15, 2);
            b.Property(e => e.NetCost).HasPrecision(15, 2);
            b.Property(e => e.CostPerOrder).HasPrecision(15, 2);
            b.Property(e => e.GrossRevenue).HasPrecision(15, 2);
            b.Property(e => e.ROI).HasPrecision(10, 4);
            b.Property(e => e.CostPerLiveView).HasPrecision(15, 4);
            b.Property(e => e.CostPerTenSecondLiveView).HasPrecision(15, 4);

            // Configure indexes for better performance
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.StoreId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.RoomId);
            b.HasIndex(e => e.LiveStatus);
            b.HasIndex(e => e.LiveLaunchedTime);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Date);
            b.HasIndex(e => e.Cost);
            b.HasIndex(e => e.Orders);
            b.HasIndex(e => e.GrossRevenue);
            b.HasIndex(e => e.ROI);
            b.HasIndex(e => e.LiveViews);
            b.HasIndex(e => e.TenSecondLiveViews);
            b.HasIndex(e => e.LiveFollows);

            // Configure composite index for unique report identification
            b.HasIndex(e => new { e.RoomId, e.Date });
        });

        builder.Entity<RawGmvMaxCampaignIdentitiesEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxCampaignIdentities", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
            // Configure composite index for unique identity per campaign
        });

        builder.Entity<RawGmvMaxIdentitiesEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxIdentities", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
        });
        builder.Entity<RawTikTokShopProductsEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawTikTokShopProducts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
        });
        builder.Entity<RawGmvMaxCampaignPostsEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxCampaignPosts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
        });
        builder.Entity<RawGmvMaxCampaignItemsEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxCampaignItems", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure List<string> properties as JSON columns
            b.Property(e => e.SpuIdList)
                .HasConversion(
                    v => v == null ? null : System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => v == null ? null : System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null))
                .HasColumnType("nvarchar(max)");

            // Configure composite index for unique item per campaign
        });

        builder.Entity<RawGmvMaxCampaignCustomAnchorVideosEntity>(b =>
        {
            b.ToTable(TikTokConsts.RawDataTablePrefix + "RawGmvMaxCampaignCustomAnchorVideos", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure List<string> properties as JSON columns
            b.Property(e => e.SpuIdList)
                .HasConversion(
                    v => v == null ? null : System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => v == null ? null : System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null))
                .HasColumnType("nvarchar(max)");


            // Configure composite index for unique custom anchor video per campaign
        });



        #endregion

        #region Dimension

        builder.Entity<DimBusinessCenterEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimBusinessCenters", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure indexes for SCD Type 2
            b.HasIndex(e => new { e.BcId, e.IsCurrent }).IsUnique();
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.IsCurrent);
            b.HasIndex(e => e.EffectiveDate);
            b.HasIndex(e => e.Status);
            b.HasIndex(e => e.RegisteredArea);
        });

        builder.Entity<DimAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure indexes for SCD Type 2
            b.HasIndex(e => new { e.AdvertiserId, e.IsCurrent }).IsUnique();
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.IsCurrent);
            b.HasIndex(e => e.EffectiveDate);
            b.HasIndex(e => e.OwnerBcId);
            b.HasIndex(e => e.Country);
            b.HasIndex(e => e.Currency);
            b.HasIndex(e => e.Status);
        });

        builder.Entity<DimDateEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimDates", TikTokConsts.DbSchema);
            b.ConfigureByConvention();

            // Configure primary key
            b.HasKey(e => e.Id);
            b.Property(e => e.Id)
              .ValueGeneratedNever(); // Không tạo IDENTITY

            // Configure indexes for time-based queries
            b.HasIndex(e => e.FullDate);
            b.HasIndex(e => e.Year);
            b.HasIndex(e => e.Month);
            b.HasIndex(e => e.Quarter);
            b.HasIndex(e => e.Week);
            b.HasIndex(e => e.DayOfWeek);
            b.HasIndex(e => e.YearMonth);
            b.HasIndex(e => e.YearQuarter);
            b.HasIndex(e => e.IsWeekend);
        });

        builder.Entity<DimCampaignEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimCampaigns", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for budget
            b.Property(e => e.Budget).HasPrecision(18, 2);



            // Configure indexes for SCD Type 2
            b.HasIndex(e => new { e.CampaignId, e.IsCurrent }).IsUnique();
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.DimAdAccountId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.IsCurrent);
            b.HasIndex(e => e.EffectiveDate);
            b.HasIndex(e => e.ObjectiveType);
            b.HasIndex(e => e.CampaignType);
            b.HasIndex(e => e.OperationStatus);
        });

        builder.Entity<DimTransactionTypeEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimTransactionTypes", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure unique index on TransactionTypeCode
            b.HasIndex(e => e.TransactionTypeCode).IsUnique();

            // Configure indexes for performance
            b.HasIndex(e => e.FundsTypeCode);
            b.HasIndex(e => e.IsActive);
            b.HasIndex(e => e.IsDebit);
            b.HasIndex(e => e.IsCredit);
        });

        // GMV Max
        // Dim Store
        builder.Entity<DimStoreEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimStores", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        // Dim Product
        builder.Entity<DimProductEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimProducts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        // Dim Product Creative
        builder.Entity<DimTTAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.DimDataTablePrefix + "DimTTAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });
        #endregion

        #region Fact

        builder.Entity<FactBalanceEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactBalances", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all balance fields (Original Currency)
            b.Property(e => e.AccountBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalance).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalance).HasPrecision(18, 2);
            b.Property(e => e.Tax).HasPrecision(18, 2);
            b.Property(e => e.CashBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalance).HasPrecision(18, 2);
            b.Property(e => e.GrantBalance).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalance).HasPrecision(18, 2);
            b.Property(e => e.TransferableAmount).HasPrecision(18, 2);
            b.Property(e => e.Budget).HasPrecision(18, 2);
            b.Property(e => e.BudgetCost).HasPrecision(18, 2);
            b.Property(e => e.BudgetRemaining).HasPrecision(18, 2);

            // Configure decimal precision for VND fields
            b.Property(e => e.AccountBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.TaxVND).HasPrecision(18, 2);
            b.Property(e => e.CashBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.GrantBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalanceVND).HasPrecision(18, 2);
            b.Property(e => e.TransferableAmountVND).HasPrecision(18, 2);
            b.Property(e => e.BudgetVND).HasPrecision(18, 2);
            b.Property(e => e.BudgetCostVND).HasPrecision(18, 2);
            b.Property(e => e.BudgetRemainingVND).HasPrecision(18, 2);

            // Configure decimal precision for USD fields
            b.Property(e => e.AccountBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.ValidAccountBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.FrozenBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.TaxUSD).HasPrecision(18, 2);
            b.Property(e => e.CashBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.ValidCashBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.GrantBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.ValidGrantBalanceUSD).HasPrecision(18, 2);
            b.Property(e => e.TransferableAmountUSD).HasPrecision(18, 2);
            b.Property(e => e.BudgetUSD).HasPrecision(18, 2);
            b.Property(e => e.BudgetCostUSD).HasPrecision(18, 2);
            b.Property(e => e.BudgetRemainingUSD).HasPrecision(18, 2);

            // Configure indexes for performance
            b.HasIndex(e => e.DimDateId);
            b.HasIndex(e => e.DimAdAccountId);
            b.HasIndex(e => e.DimBusinessCenterId);
            b.HasIndex(e => e.Type);
            b.HasIndex(e => e.EntityId);
            b.HasIndex(e => e.Date);

            // Configure composite unique index
            b.HasIndex(e => new { e.EntityId, e.Type, e.Date }).IsUnique();

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.Type, e.DimDateId });
            b.HasIndex(e => new { e.DimBusinessCenterId, e.DimDateId });
            b.HasIndex(e => new { e.DimAdAccountId, e.DimDateId });

            // Configure measure indexes (Original Currency)
            b.HasIndex(e => e.AccountBalance);
            b.HasIndex(e => e.ValidAccountBalance);
            b.HasIndex(e => e.Budget);
            b.HasIndex(e => e.BudgetCost);

            // Configure measure indexes (VND)
            b.HasIndex(e => e.AccountBalanceVND);
            b.HasIndex(e => e.ValidAccountBalanceVND);
            b.HasIndex(e => e.BudgetVND);
            b.HasIndex(e => e.BudgetCostVND);

            // Configure measure indexes (USD)
            b.HasIndex(e => e.AccountBalanceUSD);
            b.HasIndex(e => e.ValidAccountBalanceUSD);
            b.HasIndex(e => e.BudgetUSD);
            b.HasIndex(e => e.BudgetCostUSD);
        });

        builder.Entity<FactDailySpendEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactDailySpends", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all amount fields
            b.Property(e => e.TotalAmount).HasPrecision(18, 2);
            b.Property(e => e.CashAmount).HasPrecision(18, 2);
            b.Property(e => e.GrantAmount).HasPrecision(18, 2);
            b.Property(e => e.TaxAmount).HasPrecision(18, 2);

            // Configure indexes for performance
            b.HasIndex(e => e.DimDateId);
            b.HasIndex(e => e.DimAdAccountId);
            b.HasIndex(e => e.DimBusinessCenterId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.Date);

            // Configure composite unique index
            b.HasIndex(e => new { e.AdvertiserId, e.DimDateId }).IsUnique();

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.BcId, e.DimDateId });

            // Configure measure indexes
            b.HasIndex(e => e.TotalAmount);
            b.HasIndex(e => e.Date);
        });

        builder.Entity<FactTransactionEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactTransactions", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for amount
            b.Property(e => e.Amount).HasPrecision(18, 2);

            // Configure indexes for performance
            b.HasIndex(e => e.DimDateId);
            b.HasIndex(e => e.DimAdAccountId);
            b.HasIndex(e => e.DimBusinessCenterId);
            b.HasIndex(e => e.DimTransactionTypeId);
            b.HasIndex(e => e.Type);
            b.HasIndex(e => e.EntityId);
            b.HasIndex(e => e.Date);

            // Configure composite unique index
            b.HasIndex(e => new { e.EntityId, e.Type, e.Date }).IsUnique();

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.Type, e.DimDateId });
            b.HasIndex(e => new { e.DimBusinessCenterId, e.DimDateId });
            b.HasIndex(e => new { e.DimAdAccountId, e.DimDateId });

            // Configure measure indexes
            b.HasIndex(e => e.Amount);
        });

        builder.Entity<FactCampaignEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactCampaigns", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Ignore properties not present in the database schema
            b.Ignore(e => e.Type);
            b.Ignore(e => e.EntityId);
            b.Ignore(e => e.EntityName);

            // Configure decimal precision for all decimal fields
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.CostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.ConversionRate).HasPrecision(18, 4);
            b.Property(e => e.CostPerResult).HasPrecision(18, 2);
            b.Property(e => e.OnsiteShoppingRoas).HasPrecision(18, 4);
            b.Property(e => e.TotalOnsiteShoppingValue).HasPrecision(18, 2);
            b.Property(e => e.CostPerOnsiteShopping).HasPrecision(18, 2);
            b.Property(e => e.ValuePerOnsiteShopping).HasPrecision(18, 2);

            // Configure foreign key relationships with NO ACTION to avoid cascade path conflicts
            b.HasOne(e => e.DimDate)
                .WithMany()
                .HasForeignKey(e => e.DimDateId)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(e => e.DimAdAccount)
                .WithMany()
                .HasForeignKey(e => e.DimAdAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(e => e.DimBusinessCenter)
                .WithMany()
                .HasForeignKey(e => e.DimBusinessCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(e => e.DimCampaign)
                .WithMany()
                .HasForeignKey(e => e.DimCampaignId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure indexes for performance
            b.HasIndex(e => e.DimDateId);
            b.HasIndex(e => e.DimAdAccountId);
            b.HasIndex(e => e.DimBusinessCenterId);
            b.HasIndex(e => e.DimCampaignId);
            b.HasIndex(e => e.CampaignId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.ObjectiveType);
            b.HasIndex(e => e.Date);

            // Configure composite unique index
            b.HasIndex(e => new { e.CampaignId, e.DimDateId }).IsUnique();

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.DimAdAccountId, e.DimDateId });
            b.HasIndex(e => new { e.DimBusinessCenterId, e.DimDateId });

            // Configure measure indexes
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.OnsiteShoppingRoas);
            b.HasIndex(e => e.ObjectiveType);
        });

        builder.Entity<FactAdvertisementEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactAdvertisements", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for all decimal fields
            b.Property(e => e.Spend).HasPrecision(18, 2);
            b.Property(e => e.Ctr).HasPrecision(18, 4);
            b.Property(e => e.Cpm).HasPrecision(18, 2);
            b.Property(e => e.Cpc).HasPrecision(18, 2);
            b.Property(e => e.CostPerConversion).HasPrecision(18, 2);
            b.Property(e => e.ConversionRate).HasPrecision(18, 4);
            b.Property(e => e.Frequency).HasPrecision(18, 4);
            b.Property(e => e.AverageVideoPlay).HasPrecision(18, 2);
            b.Property(e => e.OnsiteShoppingRoas).HasPrecision(18, 4);
            b.Property(e => e.TotalOnsiteShoppingValue).HasPrecision(18, 2);
            b.Property(e => e.CostPerOnsiteShopping).HasPrecision(18, 2);
            b.Property(e => e.ValuePerOnsiteShopping).HasPrecision(18, 2);

            // Configure indexes for performance
            b.HasIndex(e => e.DimDateId);
            b.HasIndex(e => e.DimAdAccountId);
            b.HasIndex(e => e.DimBusinessCenterId);
            b.HasIndex(e => e.DimCampaignId);
            b.HasIndex(e => e.Type);
            b.HasIndex(e => e.EntityId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.Date);

            // Configure composite unique index
            b.HasIndex(e => new { e.EntityId, e.Type, e.DimDateId }).IsUnique();

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.Type, e.DimDateId });
            b.HasIndex(e => new { e.DimAdAccountId, e.DimDateId });
            b.HasIndex(e => new { e.DimBusinessCenterId, e.DimDateId });
            b.HasIndex(e => new { e.DimCampaignId, e.DimDateId });

            // Configure measure indexes
            b.HasIndex(e => e.Spend);
            b.HasIndex(e => e.OnsiteShoppingRoas);
        });

        // GMV Max
        // Fact Gmv Max Campaign
        builder.Entity<FactGmvMaxCampaignEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactGmvMaxCampaigns", TikTokConsts.DbSchema);
            b.ConfigureByConvention();

            // ✅ Core indexes for JOIN and filtering
            b.HasIndex(e => e.DimDateId);           // JOIN với DimDates
            b.HasIndex(e => e.AdvertiserId);       // Permission filtering (quan trọng nhất)
            b.HasIndex(e => e.Date);               // Date range queries
            b.HasIndex(e => e.StoreId);            // Store filtering & ranking
            b.HasIndex(e => e.CampaignId);         // Campaign filtering & detail

            // ✅ Composite indexes for most common query patterns
            b.HasIndex(e => new { e.AdvertiserId, e.Date });  // Permission + Date range (quan trọng nhất)
            b.HasIndex(e => new { e.DimDateId, e.AdvertiserId }); // JOIN + Permission
            b.HasIndex(e => new { e.StoreId, e.Date, e.GrossRevenue }); // Store ranking
            b.HasIndex(e => new { e.CampaignId, e.Date, e.ROAS }); // Campaign performance

            // ✅ Indexes for measures commonly used in GROUP BY/ORDER BY
            b.HasIndex(e => e.GrossRevenue);       // Dashboard ranking
            b.HasIndex(e => e.ROAS);              // Performance metrics
            b.HasIndex(e => e.Orders);             // Volume metrics
            b.HasIndex(e => e.Cost);               // Cost analysis
            b.HasIndex(e => e.TACOS);              // TACOS analysis

            // Navigation properties
            b.HasOne(x => x.DimDate)
             .WithMany()
             .HasForeignKey(x => x.DimDateId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimBusinessCenter)
             .WithMany()
             .HasForeignKey(x => x.DimBusinessCenterId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimAdAccount)
             .WithMany()
             .HasForeignKey(x => x.DimAdAccountId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimCampaign)
             .WithMany()
             .HasForeignKey(x => x.DimCampaignId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimStore)
             .WithMany()
             .HasForeignKey(x => x.DimStoreId)
             .OnDelete(DeleteBehavior.Restrict);
        });

        // Fact Gmv Max Product
        builder.Entity<FactGmvMaxProductEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactGmvMaxProducts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();

            // ✅ Core indexes for JOIN and filtering
            b.HasIndex(e => e.DimDateId);           // JOIN với DimDates
            b.HasIndex(e => e.AdvertiserId);       // Permission filtering (quan trọng nhất)
            b.HasIndex(e => e.Date);               // Date range queries
            b.HasIndex(e => e.StoreId);            // Store filtering & ranking
            b.HasIndex(e => e.CampaignId);         // Campaign filtering & detail
            b.HasIndex(e => e.ProductId);          // Product filtering & grouping

            // ✅ Composite indexes for most common query patterns
            b.HasIndex(e => new { e.AdvertiserId, e.Date });  // Permission + Date range (quan trọng nhất)
            b.HasIndex(e => new { e.DimDateId, e.AdvertiserId }); // JOIN + Permission
            b.HasIndex(e => new { e.StoreId, e.Date, e.GrossRevenue }); // Store ranking
            b.HasIndex(e => new { e.CampaignId, e.Date, e.ROAS }); // Campaign performance
            b.HasIndex(e => new { e.ProductId, e.Date, e.GrossRevenue }); // Product performance

            // ✅ Indexes for measures commonly used in GROUP BY/ORDER BY
            b.HasIndex(e => e.GrossRevenue);       // Dashboard ranking
            b.HasIndex(e => e.ROAS);              // Performance metrics
            b.HasIndex(e => e.Orders);             // Volume metrics
            b.HasIndex(e => e.TACOS);              // TACOS analysis

            b.HasOne(x => x.DimCampaign)
            .WithMany()
            .HasForeignKey(x => x.DimCampaignId)
            .OnDelete(DeleteBehavior.Restrict);
        });

        // Fact Gmv Max Product Creative Entity
        builder.Entity<FactGmvMaxProductCreativeEntity>(b =>
        {
            b.ToTable(TikTokConsts.FactDataTablePrefix + "FactGmvMaxProductCreatives", TikTokConsts.DbSchema);
            b.ConfigureByConvention();

            // ✅ Core indexes for JOIN and filtering
            b.HasIndex(e => e.DimDateId);           // JOIN với DimDates
            b.HasIndex(e => e.AdvertiserId);       // Permission filtering (quan trọng nhất)
            b.HasIndex(e => e.Date);               // Date range queries
            b.HasIndex(e => e.StoreId);            // Store filtering & ranking
            b.HasIndex(e => e.CampaignId);         // Campaign filtering & detail
            b.HasIndex(e => e.ItemGroupId);


            // Navigation properties
            b.HasOne(x => x.DimDate)
             .WithMany()
             .HasForeignKey(x => x.DimDateId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimBusinessCenter)
             .WithMany()
             .HasForeignKey(x => x.DimBusinessCenterId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimAdAccount)
             .WithMany()
             .HasForeignKey(x => x.DimAdAccountId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimCampaign)
             .WithMany()
             .HasForeignKey(x => x.DimCampaignId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimStore)
             .WithMany()
             .HasForeignKey(x => x.DimStoreId)
             .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x=>x.DimTTAccount)
            .WithMany()
            .HasForeignKey(x => x.DimTTAccountId)
            .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.DimProduct)
           .WithMany()
           .HasForeignKey(x => x.DimProductId)
           .OnDelete(DeleteBehavior.Restrict);
        });

        #endregion

        builder.Entity<BusinessApplicationEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "BusinessApplications", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        builder.Entity<CurrencyExchangeRateEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "CurrencyExchangeRates", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure decimal precision for Rate field
            b.Property(e => e.Rate).HasPrecision(18, 2);

            // Configure unique index on Currency to prevent duplicate currencies
            b.HasIndex(e => e.Currency).IsUnique();

            // Configure index on IsBase for quick lookup of base currency
            b.HasIndex(e => e.IsBase);
        });

        builder.Entity<EtlFlowStatusEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "EtlFlowStatuses", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure unique index on Name to prevent duplicate flow names
            b.HasIndex(e => e.Name).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.IsActive);
            b.HasIndex(e => e.LastDateTimeProcessed);
            b.HasIndex(e => e.LastProcessingStatus);
            b.HasIndex(e => e.LastProcessingStartTime);
            b.HasIndex(e => e.LastProcessingEndTime);
        });

        #region Background job

        builder.Entity<JobConfigurationEntity>(b =>
        {
            b.ToTable(TikTokConsts.BackgroundJobTablePrefix + "JobConfigurations", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        builder.Entity<WorkerInfoEntity>(b =>
        {
            b.ToTable(TikTokConsts.BackgroundJobTablePrefix + "WorkerInfos", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        builder.Entity<JobEntity>(b =>
        {
            b.ToTable(TikTokConsts.BackgroundJobTablePrefix + "Jobs", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure enum conversions
            b.Property(e => e.CommandType)
                .HasConversion<int>();

            b.Property(e => e.Status)
                .HasConversion<int>();

            // Configure indexes for better performance
            b.HasIndex(e => e.Status);
            b.HasIndex(e => e.CommandType);
            b.HasIndex(e => e.BusinessApplicationId);
            b.HasIndex(e => e.WorkerId);
            b.HasIndex(e => e.CreationTime);
        });

        builder.Entity<JobTypeConfigurationEntity>(b =>
        {
            b.ToTable(TikTokConsts.BackgroundJobTablePrefix + "JobTypeConfigurations", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure enum conversion
            b.Property(e => e.CommandType)
                .HasConversion<int>();

            // Configure unique index on CommandType to prevent duplicate configurations
            b.HasIndex(e => e.CommandType).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.IsActive);
            b.HasIndex(e => e.Priority);
        });

        #endregion

        builder.Entity<CustomerEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "Customers", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            // Configure unique index on CustomerId to prevent duplicate customer IDs
            b.HasIndex(e => e.CustomerId).IsUnique();
            // Configure indexes for better performance
            b.HasIndex(e => e.CustomerName);
            b.HasIndex(e => e.CustomerType);
            b.HasIndex(e => e.PhoneNumber);
        });

        builder.Entity<CustomerAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "CustomerAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            // Configure indexes for better performance
            b.HasIndex(e => e.CustomerId);
            b.HasIndex(e => e.AdvertiserId);
            b.HasIndex(e => e.ShopId);
        });
        #region TIKTOK_MANAGEMENT
        // Configure relationships
        builder.Entity<AdAccountSupporterEntity>(b =>
        {
            b.HasKey(x => x.Id);
            b.HasIndex(x => new { x.AdvertiserId, x.SupporterId }).IsUnique();
            b.Property(x => x.AdvertiserId).IsRequired().HasMaxLength(100);

        });

        // Rule
        builder.Entity<RuleEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "Rules", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure enum conversion
            b.Property(e => e.NotificationFrequency)
                .HasConversion<int>();
        });

        builder.Entity<RuleAdAccountEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "RuleAdAccounts", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();
        });

        //builder.Entity<ResourcePermissionEntity>(b =>
        //{
        //    b.ToTable(TikTokConsts.DbTablePrefix + "ResourcePermissions", TikTokConsts.DbSchema);
        //    b.ConfigureByConvention();
        //    b.TryConfigureSoftDelete();

        //    // Configure unique composite index on UserId, ResourceId, and ResourceType to prevent duplicate permissions
        //    b.HasIndex(e => new { e.UserId, e.ResourceId, e.ResourceType, e.Permission }).IsUnique();

        //    // Configure indexes for better performance
        //    b.HasIndex(e => e.UserId);
        //    b.HasIndex(e => e.ResourceId);
        //    b.HasIndex(e => e.ResourceType);
        //    b.HasIndex(e => e.Permission);
        //});

        #endregion
        builder.Entity<LatestNotificationEntity>(b =>
        {
            b.ToTable(TikTokConsts.DbTablePrefix + "LatestNotifications", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure enum conversion
            b.Property(e => e.Type)
                .HasConversion<int>();

            // Configure unique composite index on Type and ObjectId to prevent duplicate notifications
            b.HasIndex(e => new { e.Type, e.ObjectId, e.Rule }).IsUnique();

            // Configure indexes for better performance
            b.HasIndex(e => e.Type);
            b.HasIndex(e => e.ObjectId);
            b.HasIndex(e => e.LastNotifiedTime);

            // Configure string length for better performance
            b.Property(e => e.ObjectId).HasMaxLength(255);
            b.Property(e => e.Payload).HasMaxLength(4000);
            b.Property(e => e.Rule).HasMaxLength(1000);
        });

        // System Notification Rules
        builder.Entity<SystemNotificationRule>(b =>
        {
            b.ToTable("SystemNotificationRule", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure string lengths
            b.Property(e => e.RuleName).HasMaxLength(200).IsRequired();
            b.Property(e => e.EntityType).HasMaxLength(50).IsRequired();

            // Configure indexes for better performance
            b.HasIndex(e => e.EntityType);
            b.HasIndex(e => e.IsActive);
            b.HasIndex(e => e.CreatorId);
            b.HasIndex(e => e.CreationTime);
            b.HasIndex(e => e.LastTriggeredAt);

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.EntityType, e.IsActive });
            b.HasIndex(e => new { e.CreatorId, e.IsActive });
        });

        // System Notification Rule Consumers
        builder.Entity<SystemNotificationRuleConsumerEntity>(b =>
        {
            b.ToTable("SystemNotificationRuleConsumer", TikTokConsts.DbSchema);
            b.ConfigureByConvention();
            b.TryConfigureSoftDelete();

            // Configure string lengths
            b.Property(e => e.ConsumerType).HasMaxLength(50).IsRequired();
            b.Property(e => e.ConsumerName).HasMaxLength(200);
            b.Property(e => e.ConsumerId).HasMaxLength(100);

            // Configure indexes for better performance
            b.HasIndex(e => e.SystemNotificationRuleId);
            b.HasIndex(e => e.BcId);
            b.HasIndex(e => e.AdAccountId);
            b.HasIndex(e => e.ConsumerType);

            // Configure composite indexes for common queries
            b.HasIndex(e => new { e.SystemNotificationRuleId, e.ConsumerType });
            b.HasIndex(e => new { e.BcId, e.AdAccountId });
        });
    }
}
