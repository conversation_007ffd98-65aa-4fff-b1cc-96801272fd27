﻿.dropdown-menu.show {
    max-height: 300px;
    overflow-y: auto;
}

/* Hide default DataTable search */
.dataTables_filter {
    display: none !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .input-group-text {
        padding: 0.375rem 0.5rem;
    }

    .btn {
        padding: 0.375rem 0.5rem;
    }

    .form-control,
    .form-select {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Make table more responsive */
    .table-responsive {
        border: none;
    }

    /* Stack buttons on mobile */
    .btn-group-vertical .btn {
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 576px) {
    .card-header .row {
        margin: 0;
    }

    .card-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .input-group {
        margin-bottom: 0.5rem;
    }

    /* Full width on very small screens */
    .col-lg-6 {
        margin-bottom: 0.75rem;
    }
}

/* Improve table responsiveness */
@media (max-width: 992px) {
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: center;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: 0.5rem;
    }
}

/* Better spacing for filter section */
@media (min-width: 769px) {
    .mb-2.mb-lg-0 {
        margin-bottom: 0 !important;
    }
}
