using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.FactGmvMaxCampaigns;
using TikTok.FactGmvMaxCampaigns.Dtos;
using TikTok.Permissions;
using TikTok.DimStores;
using TikTok.DimCampaigns;

namespace TikTok.HttpApi.Controllers
{
    [Route("api/fact-gmv-max-campaign")]
    public class FactGmvMaxCampaignController : AbpControllerBase
    {
        private readonly IFactGmvMaxCampaignService _factGmvMaxCampaignService;
        private readonly IDimStoreAppService _dimStoreAppService;
        private readonly IDimCampaignAppService _dimCampaignAppService;

        public FactGmvMaxCampaignController(
            IFactGmvMaxCampaignService factGmvMaxCampaignService,
            IDimStoreAppService dimStoreAppService,
            IDimCampaignAppService dimCampaignAppService)
        {
            _factGmvMaxCampaignService = factGmvMaxCampaignService;
            _dimStoreAppService = dimStoreAppService;
            _dimCampaignAppService = dimCampaignAppService;
        }

        /// <summary>
        /// Lấy dữ liệu GMV Max Campaign cho pivot table
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="shoppingAdsType">Loại quảng cáo shopping</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <param name="campaignIds">Danh sách Campaign IDs để filter</param>
        /// <param name="shopIds">Danh sách Shop IDs để filter</param>
        /// <param name="searchText">Tìm kiếm theo tên campaign, store, advertiser</param>
        /// <returns>Dữ liệu GMV Max Campaign</returns>
        [HttpGet("data")]
        public async Task<ActionResult<GetFactGmvMaxCampaignDataResponse>> GetGmvMaxCampaignData(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? shoppingAdsType = null,
            [FromQuery] string? operationStatus = null,
            [FromQuery] string? currency = "USD",
            [FromQuery] List<string>? campaignIds = null,
            [FromQuery] List<string>? shopIds = null,
            [FromQuery] string? searchText = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập dữ liệu GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                // ✅ TỐI ƯU: Get data with filters at database level for better performance
                var result = await _factGmvMaxCampaignService.GetListWithFiltersAsync(
                    from, to, currency,
                    campaignIds, shopIds, searchText, shoppingAdsType, operationStatus);

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }


        /// <summary>
        /// Lấy danh sách chiến dịch có hiệu suất thấp
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="roasThreshold">Ngưỡng ROAS thấp (mặc định: 1.5)</param>
        /// <param name="tacosThreshold">Ngưỡng TACOS cao (mặc định: 30%)</param>
        /// <returns>Danh sách chiến dịch có hiệu suất thấp</returns>
        [HttpGet("low-performance")]
        public async Task<IActionResult> GetLowPerformanceCampaigns(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] decimal roasThreshold = 1.5m,
            [FromQuery] decimal tacosThreshold = 30.0m)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem chỉ số hiệu suất GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxCampaignService.GetListAsync(from, to, "USD", hasViewAllAdvertisers);
                var data = result.FactGmvMaxCampaigns;

                var lowPerformance = data
                    .Where(c => (c.ROAS.HasValue && c.ROAS < roasThreshold) || (c.TACOS.HasValue && (c.TACOS * 100) > tacosThreshold))
                    .Select(c => new
                    {
                        c.CampaignId,
                        c.ShoppingAdsType,
                        c.StoreId,
                        ROAS = c.ROAS,
                        TACOS = c.TACOS,
                        Cost = c.Cost,
                        GrossRevenue = c.GrossRevenue,
                        Orders = c.Orders,
                        Status = (c.ROAS.HasValue && c.ROAS < roasThreshold) ? "Low ROAS" : "High TACOS"
                    })
                    .OrderBy(c => c.ROAS ?? 0)
                    .Take(20)
                    .ToList();

                return Ok(lowPerformance);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách chiến dịch có chi phí cao
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="costPerOrderThreshold">Ngưỡng chi phí mỗi đơn cao (mặc định: 100000 VND)</param>
        /// <returns>Danh sách chiến dịch có chi phí cao</returns>
        [HttpGet("high-cost")]
        public async Task<IActionResult> GetHighCostCampaigns(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] decimal costPerOrderThreshold = 100000m)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem thông tin chi tiêu GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxCampaignService.GetListAsync(from, to, "USD", hasViewAllAdvertisers);
                var data = result.FactGmvMaxCampaigns;

                var highCost = data
                    .Where(c => c.CostPerOrder.HasValue && c.CostPerOrder > costPerOrderThreshold)
                    .Select(c => new
                    {
                        c.CampaignId,
                        c.ShoppingAdsType,
                        c.StoreId,
                        CostPerOrder = c.CostPerOrder,
                        Cost = c.Cost,
                        Orders = c.Orders,
                        ROAS = c.ROAS,
                        Status = "High Cost Per Order"
                    })
                    .OrderByDescending(c => c.CostPerOrder ?? 0)
                    .Take(20)
                    .ToList();

                return Ok(highCost);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy cảnh báo hiệu suất chiến dịch
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách cảnh báo</returns>
        [HttpGet("alerts")]
        public async Task<IActionResult> GetCampaignAlerts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem cảnh báo hiệu suất GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxCampaignService.GetListAsync(from, to, "USD", hasViewAllAdvertisers);
                var data = result.FactGmvMaxCampaigns;

                var alerts = new List<object>();

                // ROAS alerts
                var lowRoasCampaigns = data.Where(c => c.ROAS.HasValue && c.ROAS < 1.5m).Take(5);
                foreach (var campaign in lowRoasCampaigns)
                {
                    alerts.Add(new
                    {
                        Type = "Low ROAS",
                        Severity = campaign.ROAS < 1.0m ? "Critical" : "Warning",
                        CampaignId = campaign.CampaignId,
                        StoreId = campaign.StoreId,
                        Value = campaign.ROAS,
                        Threshold = 1.5m,
                        Message = $"ROAS ({campaign.ROAS:F2}x) dưới ngưỡng 1.5x",
                        Date = campaign.Date
                    });
                }

                // TACOS alerts
                var highTacosCampaigns = data.Where(c => c.TACOS.HasValue && (c.TACOS * 100) > 30m).Take(5);
                foreach (var campaign in highTacosCampaigns)
                {
                    alerts.Add(new
                    {
                        Type = "High TACOS",
                        Severity = (campaign.TACOS * 100) > 40m ? "Critical" : "Warning",
                        CampaignId = campaign.CampaignId,
                        StoreId = campaign.StoreId,
                        Value = campaign.TACOS,
                        Threshold = 0.30m,
                        Message = $"TACOS ({campaign.TACOS:P1}) vượt ngưỡng 30%",
                        Date = campaign.Date
                    });
                }

                return Ok(alerts.OrderByDescending(a => a.GetType().GetProperty("Date")?.GetValue(a)).Take(10));
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy xu hướng GMV Max Campaign theo thời gian
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Xu hướng chiến dịch</returns>
        [HttpGet("trends")]
        public async Task<IActionResult> GetGmvMaxCampaignTrends(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem xu hướng GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxCampaignService.GetListAsync(from, to, "USD", hasViewAllAdvertisers);
                var data = result.FactGmvMaxCampaigns;

                var trends = data
                    .GroupBy(c => c.Date.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalCost = g.Sum(c => c.Cost),
                        TotalGrossRevenue = g.Sum(c => c.GrossRevenue),
                        AverageROAS = g.Where(c => c.ROAS.HasValue).Select(c => c.ROAS ?? 0m).DefaultIfEmpty(0m).Average(),
                        AverageTACOS = g.Where(c => c.TACOS.HasValue).Select(c => c.TACOS ?? 0m).DefaultIfEmpty(0m).Average(),
                        TotalOrders = g.Sum(c => c.Orders),
                        CampaignCount = g.Select(c => c.CampaignId).Distinct().Count(),
                        StoreCount = g.Select(c => c.StoreId).Distinct().Count()
                    })
                    .OrderBy(t => t.Date)
                    .ToList();

                return Ok(trends);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy thống kê theo store
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Thống kê theo store</returns>
        [HttpGet("store-performance")]
        public async Task<IActionResult> GetStorePerformance(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem thống kê hiệu suất store GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxCampaignService.GetListAsync(from, to, "USD", hasViewAllAdvertisers);
                var data = result.FactGmvMaxCampaigns;

                var storePerformance = data
                    .GroupBy(c => c.StoreId)
                    .Select(g => new
                    {
                        StoreId = g.Key,
                        TotalCost = g.Sum(c => c.Cost),
                        TotalGrossRevenue = g.Sum(c => c.GrossRevenue),
                        TotalOrders = g.Sum(c => c.Orders),
                        AverageROAS = g.Where(c => c.ROAS.HasValue).Select(c => c.ROAS ?? 0m).DefaultIfEmpty(0m).Average(),
                        AverageTACOS = g.Where(c => c.TACOS.HasValue).Select(c => c.TACOS ?? 0m).DefaultIfEmpty(0m).Average(),
                        CampaignCount = g.Select(c => c.CampaignId).Distinct().Count(),
                        Performance = (g.Where(c => c.ROAS.HasValue).Select(c => c.ROAS ?? 0m).DefaultIfEmpty(0m).Average() >= 2.0m) ? "Good" : "Poor"
                    })
                    .OrderByDescending(s => s.TotalGrossRevenue)
                    .ToList();

                return Ok(storePerformance);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Summary Cards - Dữ liệu tổng hợp cho summary cards
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <param name="shoppingAdsType">Loại quảng cáo shopping (PRODUCT/LIVE)</param>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="searchText">Tìm kiếm theo tên</param>
        /// <param name="shopIds">Danh sách shop IDs</param>
        /// <param name="campaignIds">Danh sách Campaign IDs để filter</param>
        /// <returns>Summary cards data</returns>
        [HttpGet("summary")]
        public async Task<IActionResult> GetSummaryCards(
            [FromQuery] string? currency = "USD",
            [FromQuery] string? shoppingAdsType = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? searchText = null,
            [FromQuery] List<string>? shopIds = null,
            // ✅ NEW FILTERS - Backward compatible
            [FromQuery] List<string>? campaignIds = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập summary cards GMV Max Campaign");
                }

                // ✅ TỐI ƯU: Get summary data with filters at database level
                var summaryData = await _factGmvMaxCampaignService.GetSummaryCardsAsync(
                    currency, 
                    shoppingAdsType, 
                    fromDate, 
                    toDate, 
                    searchText, 
                    shopIds,
                    campaignIds);
                return Ok(summaryData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API lấy danh sách campaigns theo ROI categories
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <param name="shoppingAdsType">Loại quảng cáo shopping (PRODUCT/LIVE)</param>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="searchText">Tìm kiếm theo tên</param>
        /// <param name="shopIds">Danh sách shop IDs</param>
        /// <param name="roiCategory">Loại ROI (good/poor)</param>
        /// <param name="campaignIds">Danh sách Campaign IDs để filter</param>
        /// <returns>Danh sách campaigns theo ROI category</returns>
        [HttpGet("campaigns-by-roi")]
        public async Task<IActionResult> GetCampaignsByRoi(
            [FromQuery] string? currency = "USD",
            [FromQuery] string? shoppingAdsType = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? searchText = null,
            [FromQuery] List<string>? shopIds = null,
            [FromQuery] string? roiCategory = "good",
            // ✅ NEW FILTERS - Backward compatible
            [FromQuery] List<string>? campaignIds = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập ROI analysis GMV Max Campaign");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                // ✅ Lấy snapshot mới nhất theo mỗi campaign trong khoảng thời gian
                var latestCampaigns = await _factGmvMaxCampaignService.GetLatestPerCampaignWithPermissionsAsync(from, to, currency);
                var campaigns = latestCampaigns.ToList();

                // Apply additional filters
                if (!string.IsNullOrEmpty(shoppingAdsType))
                {
                    campaigns = campaigns.Where(c => 
                        c.ShoppingAdsType.Equals(shoppingAdsType, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (!string.IsNullOrEmpty(searchText))
                {
                    var searchLower = searchText.ToLower();
                    campaigns = campaigns.Where(c => 
                        (c.CampaignName?.ToLower().Contains(searchLower) ?? false) ||
                        (c.StoreId?.ToLower().Contains(searchLower) ?? false) ||
                        (c.AdvertiserId?.ToLower().Contains(searchLower) ?? false)).ToList();
                }

                if (shopIds != null && shopIds.Any())
                {
                    var shopIdStrings = shopIds.Select(id => id.ToString()).ToHashSet();
                    campaigns = campaigns.Where(c => 
                        shopIdStrings.Contains(c.StoreId)).ToList();
                }

                // ✅ Apply CampaignId filter
                if (campaignIds != null && campaignIds.Any())
                {
                    campaigns = campaigns.Where(c => 
                        campaignIds.Contains(c.CampaignId)).ToList();
                }

                // ✅ Backend ROI rules (fixed thresholds)
                const decimal GOOD_ROI_THRESHOLD = 3.0m;
                const decimal POOR_ROI_THRESHOLD = 1.5m;

                // ✅ Filter campaigns by ROI category
                IEnumerable<FactGmvMaxCampaignDto> filteredCampaigns;
                
                if (roiCategory?.ToLower() == "poor")
                {
                    filteredCampaigns = campaigns
                        .Where(c => c.ROAS.HasValue && c.ROAS < POOR_ROI_THRESHOLD)
                        .OrderBy(c => c.ROAS); // Worst first
                }
                else // Default to "good"
                {
                    filteredCampaigns = campaigns
                        .Where(c => c.ROAS.HasValue && c.ROAS >= GOOD_ROI_THRESHOLD)
                        .OrderByDescending(c => c.ROAS); // Best first
                }

                // ✅ Return simple campaign list
                var campaignList = filteredCampaigns
                    .Take(100) // Reasonable limit
                    .Select(c => new
                    {
                        campaignId = c.CampaignId,
                        campaignName = c.CampaignName,
                        storeId = c.StoreId,
                        advertiserId = c.AdvertiserId,
                        roas = c.ROAS,
                        tacos = c.TACOS,
                        cost = currency?.ToUpper() == "VND" ? c.CostVND : c.CostUSD,
                        grossRevenue = currency?.ToUpper() == "VND" ? c.GrossRevenueVND : c.GrossRevenueUSD,
                        orders = c.Orders,
                        date = c.Date,
                        shoppingAdsType = c.ShoppingAdsType
                    })
                    .ToList();

                return Ok(new
                {
                    campaigns = campaignList,
                    count = campaignList.Count,
                    roiCategory = roiCategory,
                    threshold = roiCategory?.ToLower() == "poor" ? $"< {POOR_ROI_THRESHOLD}" : $">= {GOOD_ROI_THRESHOLD}",
                    currency = currency,
                    generatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Overview Section - Dữ liệu tổng quan tháng hiện tại và tháng trước
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Overview section data</returns>
        [HttpGet("overview")]
        public async Task<IActionResult> GetOverviewSection(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập overview GMV Max Campaign");
                }

                var overviewData = await _factGmvMaxCampaignService.GetOverviewSectionAsync(currency);
                return Ok(overviewData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Charts Section - Dữ liệu biểu đồ tổng quan và chi tiết (GỘP)
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Combined charts data (overview + detailed)</returns>
        [HttpGet("charts")]
        public async Task<IActionResult> GetChartsData(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập charts GMV Max Campaign");
                }

                // ✅ OPTIMIZED: Gộp cả charts và detailed charts trong 1 API call
                var chartsData = await _factGmvMaxCampaignService.GetChartsDataAsync(currency);
                var detailedChartsData = await _factGmvMaxCampaignService.GetDetailedChartsAsync();

                // ✅ Return combined data structure
                var combinedData = new
                {
                    // Overview charts data
                    weeklyData = chartsData.WeeklyData,
                    monthlyData = chartsData.MonthlyData,
                    currency = chartsData.Currency,
                    generatedAt = chartsData.GeneratedAt,
                    
                    // Detailed charts data
                    financialAnalysis = detailedChartsData.FinancialAnalysis,
                    ordersAnalysis = detailedChartsData.OrdersAnalysis,
                    livePerformance = detailedChartsData.LivePerformance,
                    detailedFromDate = detailedChartsData.FromDate,
                    detailedToDate = detailedChartsData.ToDate,
                    detailedGeneratedAt = detailedChartsData.GeneratedAt
                };

                return Ok(combinedData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Rankings Section - Dữ liệu xếp hạng store
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Rankings data</returns>
        [HttpGet("rankings")]
        public async Task<IActionResult> GetRankingsData(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding - requires ViewAll or ViewAllAdvertisers
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập rankings GMV Max Campaign");
                }

                var rankingsData = await _factGmvMaxCampaignService.GetRankingsDataAsync(currency);
                return Ok(rankingsData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API chuyên cho Dashboard - Pre-aggregated data using Dapper + Dashboard Summary
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Dashboard data với current month, last month, weekly, yearly và dashboard summary</returns>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardData(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập dashboard GMV Max Campaign");
                }

                // ✅ Get REAL data from Dapper repository
                var dashboardData = await _factGmvMaxCampaignService.GetDashboardAsync(currency);
                
                // ✅ Get Dashboard Summary using optimized Dapper query
                var dashboardSummary = await _factGmvMaxCampaignService.GetDashboardSummaryAsync(currency);
                
                // ✅ Get Detailed Analysis Data for 7 days
                var detailedAnalysisData = await _factGmvMaxCampaignService.GetDetailedAnalysisDataAsync();

                // Kết hợp dashboard data với dashboard summary và detailed analysis
                var combinedData = new
                {
                    DashboardSummary = dashboardSummary,
                    DetailedAnalysisData = detailedAnalysisData,
                    CurrentMonth = dashboardData.CurrentMonth,
                    LastMonth = dashboardData.LastMonth,
                    WeeklyData = dashboardData.WeeklyData,
                    MonthlyData = dashboardData.MonthlyData,
                    // Revenue rankings (doanh thu)
                    CurrentWeekStoreRanking = dashboardData.CurrentWeekStoreRanking,
                    TwoWeeksAgoStoreRanking = dashboardData.TwoWeeksAgoStoreRanking,
                    OneWeekAgoStoreRanking = dashboardData.OneWeekAgoStoreRanking,
                    // Cost rankings (chi tiêu) - THÊM MỚI
                    CurrentWeekStoreCostRanking = dashboardData.CurrentWeekStoreCostRanking,
                    TwoWeeksAgoStoreCostRanking = dashboardData.TwoWeeksAgoStoreCostRanking,
                    OneWeekAgoStoreCostRanking = dashboardData.OneWeekAgoStoreCostRanking,
                };

                return Ok(combinedData);
            }
            catch (Exception ex)
            {
                // Fallback: return empty dashboard payload to avoid FE 400 handling
                var now = DateTime.Now;
                var emptyResponse = new
                {
                    DashboardSummary = new
                    {
                        TotalCost = 0m,
                        TotalNetCost = 0m,
                        TotalGrossRevenue = 0m,
                        AverageROAS = 0m,
                        AverageTACOS = 0m,
                        TotalOrders = 0,
                        CampaignCount = 0,
                        ActiveStores = 0,
                        ActiveAdvertisers = 0,
                        Month = now.Month,
                        Year = now.Year,
                        MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN")),
                        FromDate = new DateTime(now.Year, now.Month, 1),
                        ToDate = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59),
                        DataPointCount = 0
                    },
                    DetailedAnalysisData = new
                    {
                        FromDate = now.AddDays(-6).Date,
                        ToDate = now.Date,
                        DailyData = new List<object>()
                    },
                    CurrentMonth = new { Year = now.Year, Month = now.Month, TotalRevenue = 0m },
                    LastMonth = new { Year = now.Month == 1 ? now.Year - 1 : now.Year, Month = now.Month == 1 ? 12 : now.Month - 1, TotalRevenue = 0m },
                    WeeklyData = new List<object>(),
                    MonthlyData = new List<object>(),
                    // Revenue rankings (doanh thu)
                    CurrentWeekStoreRanking = new List<object>(),
                    TwoWeeksAgoStoreRanking = new List<object>(),
                    OneWeekAgoStoreRanking = new List<object>(),
                    // Cost rankings (chi tiêu) - THÊM MỚI
                    CurrentWeekStoreCostRanking = new List<object>(),
                    TwoWeeksAgoStoreCostRanking = new List<object>(),
                    OneWeekAgoStoreCostRanking = new List<object>(),
                    Error = ex.Message
                };
                return Ok(emptyResponse);
            }
        }

    }
}
