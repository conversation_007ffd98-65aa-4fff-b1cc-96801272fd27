using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TikTok.Entities;
using TikTok.Entities.Dim;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Domain.Entities.DataWarehouse.Fact;

/// <summary>
/// Fact table cho GMV Max Product Creative - Chi tiết hiệu suất từng creative/video
/// Phục vụ drill-down analysis theo creative cụ thể trong Product GMV Max Campaign
/// Được xây dựng từ dữ liệu thô: RawGmvMaxProductCreativeReportEntity
/// </summary>
[Table("Fact_GmvMaxProductCreative")]
public class FactGmvMaxProductCreativeEntity : AuditedEntity<Guid>
{
    /// <summary>
    /// Foreign Key đến Dim_Date
    /// </summary>
    [Required]
    public int DimDateId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_BusinessCenter
    /// </summary>
    [Required]
    public Guid DimBusinessCenterId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_AdAccount
    /// </summary>
    [Required]
    public Guid DimAdAccountId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_Campaign
    /// </summary>
    [Required]
    public Guid DimCampaignId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_Store (TikTok Shop)
    /// </summary>
    [Required]
    public Guid DimStoreId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_TTAccount
    /// </summary>
    public Guid? DimTTAccountId { get; set; }

    /// <summary>
    /// Foriegn Key đến Dim_Product
    /// </summary>
    public Guid? DimProductId { get; set; }

    /// <summary>
    /// Business Key - Campaign ID từ TikTok
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.CampaignId
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string CampaignId { get; set; }

    /// <summary>
    /// Business Key - Store ID từ TikTok Shop
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.StoreId
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string StoreId { get; set; }

    /// <summary>
    /// Business Key - Business Center ID từ TikTok
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.BcId
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string BcId { get; set; }

    /// <summary>
    /// Business Key - Advertiser ID từ TikTok
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdvertiserId
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string AdvertiserId { get; set; }

    /// <summary>
    /// Business Key - Product Group ID (SPU ID) từ TikTok Shop
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ItemGroupId
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string ItemGroupId { get; set; }

    /// <summary>
    /// Business Key - Item ID (TikTok post ID hoặc -1 nếu là Product Card)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ItemId
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string ItemId { get; set; }

    /// <summary>
    /// Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.CreativeType
    /// </summary>
    [Required]
    [StringLength(20)]
    public required string CreativeType { get; set; }

    /// <summary>
    /// Tên creative/video
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.Title
    /// </summary>
    [StringLength(500)]
    public string? Title { get; set; }

    /// <summary>
    /// Hình ảnh minh họa video
    /// </summary>
    public string? Cover { get; set; }


    /// <summary>
    /// Loại nội dung shop (VIDEO, PRODUCT_CARD)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ShopContentType
    /// </summary>
    [StringLength(20)]
    public string? ShopContentType { get; set; }

    /// <summary>
    /// Trạng thái creative delivery
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.CreativeDeliveryStatus
    /// </summary>
    [Required]
    [StringLength(20)]
    public required string CreativeDeliveryStatus { get; set; }

    /// <summary>
    /// Chi phí quảng cáo từ creative này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.Cost
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal Cost { get; set; }

    /// <summary>
    /// Chi phí quảng cáo (VND)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostVND { get; set; }

    /// <summary>
    /// Chi phí quảng cáo (USD)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostUSD { get; set; }

    /// <summary>
    /// Số lượng đơn hàng SKU từ creative này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.Orders
    /// </summary>
    public int Orders { get; set; }

    /// <summary>
    /// Tổng doanh thu gộp từ creative này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.GrossRevenue
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal GrossRevenue { get; set; }

    /// <summary>
    /// Tổng doanh thu gộp (VND)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? GrossRevenueVND { get; set; }

    /// <summary>
    /// Tổng doanh thu gộp (USD)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? GrossRevenueUSD { get; set; }

    /// <summary>
    /// Tổng lượt hiển thị sản phẩm (organic + paid)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ProductImpressions
    /// </summary>
    public long? ProductImpressions { get; set; }

    /// <summary>
    /// Tổng lượt click sản phẩm (organic + paid)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ProductClicks
    /// </summary>
    public long? ProductClicks { get; set; }

    /// <summary>
    /// Tỷ lệ click sản phẩm (ProductClicks/ProductImpressions)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ProductClickRate
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? ProductClickRate { get; set; }

    /// <summary>
    /// Tỷ lệ click-through của paid views từ video này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdClickRate
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdClickRate { get; set; }

    /// <summary>
    /// Tỷ lệ chuyển đổi của paid clicks từ video này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdConversionRate
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdConversionRate { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 2 giây
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRate2s
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRate2s { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 6 giây
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRate6s
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRate6s { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 25% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP25
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP25 { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 50% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP50
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP50 { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 75% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP75
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP75 { get; set; }

    /// <summary>
    /// Tỷ lệ xem video 100% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP100
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP100 { get; set; }

    /// <summary>
    /// ROAS (Return on Ad Spend) - Hiệu quả quảng cáo
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ROI
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? ROAS { get; set; }

    /// <summary>
    /// TACOS (Total Advertising Cost of Sales) - Tỷ lệ chi phí quảng cáo trên doanh thu
    /// TACOS = Cost / GrossRevenue
    /// Chỉ hiển thị với quyền ViewAll hoặc ViewAllAdvertisers
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? TACOS { get; set; }

    /// <summary>
    /// Chi phí trung bình mỗi đơn hàng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.CostPerOrder
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostPerOrder { get; set; }

    /// <summary>
    /// Chi phí trung bình mỗi đơn hàng (VND)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostPerOrderVND { get; set; }

    /// <summary>
    /// Chi phí trung bình mỗi đơn hàng (USD)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostPerOrderUSD { get; set; }

    /// <summary>
    /// Tiền tệ
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.Currency
    /// </summary>
    [Required]
    [StringLength(10)]
    public required string Currency { get; set; }

    /// <summary>
    /// Ngày báo cáo UTC format yyyy-MM-dd 00:00:00 (theo ngày)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.Date (converted to date only)
    /// </summary>
    [Required]
    public DateTime Date { get; set; }

    /// <summary>
    /// Navigation Properties
    /// </summary>
    public virtual DimDateEntity? DimDate { get; set; }
    public virtual DimBusinessCenterEntity? DimBusinessCenter { get; set; }
    public virtual DimAdAccountEntity? DimAdAccount { get; set; }
    public virtual DimCampaignEntity? DimCampaign { get; set; }
    public virtual DimStoreEntity? DimStore { get; set; }
    public virtual DimTTAccountEntity? DimTTAccount { get; set; }
    public virtual DimProductEntity? DimProduct { get; set; }
}
