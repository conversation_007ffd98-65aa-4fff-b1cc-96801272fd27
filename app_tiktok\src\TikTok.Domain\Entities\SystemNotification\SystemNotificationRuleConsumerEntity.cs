using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Domain.Entities.SystemNotification
{
    /// <summary>
    /// Relationship between SystemNotificationRule and Consumers (Ad Accounts/Business Centers)
    /// Similar to RuleAdAccountEntity but for SystemNotificationRules
    /// </summary>
    [Table("SystemNotificationRuleConsumer")]
    public class SystemNotificationRuleConsumerEntity : AuditedEntity<Guid>
    {
        /// <summary>
        /// ID của SystemNotificationRule
        /// </summary>
        [Required]
        public Guid SystemNotificationRuleId { get; set; }
        
        public SystemNotificationRule SystemNotificationRule { get; set; }

        /// <summary>
        /// Nếu BcId là null thì Rule áp dụng cho tất cả Bc
        /// </summary>
        public Guid? BcId { get; set; }

        /// <summary>
        /// Nếu AdAccountId là null thì Rule áp dụng cho tất cả AdAccount trong Bc
        /// </summary>
        public Guid? AdAccountId { get; set; }

        /// <summary>
        /// Loại consumer: "AdAccount", "BusinessCenter", "All"
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ConsumerType { get; set; } = "AdAccount";

        /// <summary>
        /// Tên consumer để hiển thị
        /// </summary>
        [StringLength(200)]
        public string? ConsumerName { get; set; }

        /// <summary>
        /// ID của consumer (AdAccountId hoặc BcId)
        /// </summary>
        [StringLength(100)]
        public string? ConsumerId { get; set; }
    }
}
