using System.Collections.Generic;

namespace TikTok.Application.Contracts.DataSync.TikTokVideoSync
{
    /// <summary>
    /// DTO cho response lấy thông tin nhiều video TikTok
    /// </summary>
    public class GetVideoInfoListResponseDto
    {
        /// <summary>
        /// Danh sách kết quả thông tin video
        /// </summary>
        public List<VideoInfoResponseDto> Results { get; set; } = new List<VideoInfoResponseDto>();

        /// <summary>
        /// Tổng số video được xử lý
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// Số video thành công
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// Số video thất bại
        /// </summary>
        public int FailureCount { get; set; }
    }
}
