using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Controllers;
using TikTok.Permissions;
using Volo.Abp;

namespace TikTok.HttpApi.Controllers.SystemNotification
{
    [RemoteService(Name = "TikTok")]
    [Route("api/system-notification-rule-consumers")]
    [Authorize(TikTokPermissions.NotificationRules.Default)]
    public class SystemNotificationRuleConsumerController : TikTokController
    {
        private readonly ISystemNotificationRuleConsumerAppService _consumerAppService;

        public SystemNotificationRuleConsumerController(ISystemNotificationRuleConsumerAppService consumerAppService)
        {
            _consumerAppService = consumerAppService;
        }

        /// <summary>
        /// Lấy danh sách consumers của một rule
        /// </summary>
        [HttpGet("{ruleId}/consumers")]
        public async Task<SystemNotificationRuleConsumersResponse> GetRuleConsumers(Guid ruleId)
        {
            return await _consumerAppService.GetRuleConsumersAsync(ruleId);
        }

        /// <summary>
        /// Thêm consumers vào rule
        /// </summary>
        [HttpPost("add-consumers")]
        [Authorize(TikTokPermissions.NotificationRules.ManageRule)]
        public async Task<List<SystemNotificationRuleConsumerDto>> AddConsumersToRule(AddSystemNotificationRuleConsumersDto input)
        {
            return await _consumerAppService.AddConsumersToRuleAsync(input);
        }

        /// <summary>
        /// Xóa consumers khỏi rule
        /// </summary>
        [HttpDelete("{ruleId}/consumers")]
        [Authorize(TikTokPermissions.NotificationRules.ManageRule)]
        public async Task RemoveConsumersFromRule(Guid ruleId, [FromBody] List<Guid> consumerIds)
        {
            await _consumerAppService.RemoveConsumersFromRuleAsync(ruleId, consumerIds);
        }
    }
}
