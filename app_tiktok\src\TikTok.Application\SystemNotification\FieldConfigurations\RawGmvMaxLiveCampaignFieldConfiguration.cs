using System;
using System.Collections.Generic;
using Volo.Abp.DependencyInjection;
using TikTok.Application.Contracts.SystemNotification;

namespace TikTok.Application.SystemNotification.FieldConfigurations
{
    /// <summary>
    /// Cấu hình field definitions cho RawGmvMaxLiveCampaignReportEntity
    /// Chỉ đăng ký các field thực sự cần thiết cho notification rules
    /// Hỗ trợ cross-entity rules (Live Campaign → Campaign notifications)
    /// </summary>
    public class RawGmvMaxLiveCampaignFieldConfiguration : ITransientDependency
    {
        public const string EntityType = "RawGmvMaxLiveCampaignReportEntity";

        public List<FieldMetadata> GetFieldDefinitions()
        {
            return new List<FieldMetadata>
            {
                // === PERFORMANCE METRICS (High Priority) ===
                new FieldMetadata(
                    name: "ROI",
                    displayName: "ROI",
                    dataType: "Decimal",
                    category: "Performance",
                    description: "Return on Investment - Tỷ suất lợi nhuận"
                ),

                new FieldMetadata(
                    name: "Cost",
                    displayName: "Chi phí quảng cáo",
                    dataType: "Decimal",
                    category: "Performance",
                    description: "Tổng chi phí quảng cáo"
                ),

                new FieldMetadata(
                    name: "NetCost",
                    displayName: "Chi phí thực tế",
                    dataType: "Decimal",
                    category: "Performance",
                    description: "Chi phí thực tế (trừ ad credit hoặc coupon)"
                ),

                new FieldMetadata(
                    name: "GrossRevenue",
                    displayName: "Doanh thu gộp",
                    dataType: "Decimal",
                    category: "Performance",
                    description: "Tổng doanh thu từ đơn hàng LIVE"
                ),

                new FieldMetadata(
                    name: "Orders",
                    displayName: "Số đơn hàng",
                    dataType: "Number",
                    category: "Performance",
                    description: "Số lượng đơn hàng SKU LIVE"
                ),

                new FieldMetadata(
                    name: "CostPerOrder",
                    displayName: "Chi phí mỗi đơn hàng",
                    dataType: "Decimal",
                    category: "Performance",
                    description: "Chi phí trung bình mỗi đơn hàng LIVE"
                ),

                // === LIVE METRICS (High Priority) ===
                new FieldMetadata(
                    name: "LiveViews",
                    displayName: "Lượt xem LIVE",
                    dataType: "Number",
                    category: "Live Performance",
                    description: "Số lượt xem LIVE"
                ),

                new FieldMetadata(
                    name: "CostPerLiveView",
                    displayName: "Chi phí mỗi lượt xem",
                    dataType: "Decimal",
                    category: "Live Performance",
                    description: "Chi phí trung bình mỗi lượt xem LIVE"
                ),

                new FieldMetadata(
                    name: "TenSecondLiveViews",
                    displayName: "Lượt xem 10 giây",
                    dataType: "Number",
                    category: "Live Performance",
                    description: "Số lượt xem LIVE ít nhất 10 giây"
                ),

                new FieldMetadata(
                    name: "CostPerTenSecondLiveView",
                    displayName: "Chi phí mỗi lượt xem 10 giây",
                    dataType: "Decimal",
                    category: "Live Performance",
                    description: "Chi phí trung bình mỗi lượt xem LIVE 10 giây"
                ),

                new FieldMetadata(
                    name: "LiveFollows",
                    displayName: "Lượt follow",
                    dataType: "Number",
                    category: "Live Performance",
                    description: "Số lượt follow profile trong quá trình LIVE"
                ),

                // === CAMPAIGN STATUS (High Priority) ===
                new FieldMetadata(
                    name: "OperationStatus",
                    displayName: "Trạng thái hoạt động",
                    dataType: "String",
                    category: "Basic Info",
                    description: "Trạng thái ON/OFF chiến dịch",
                    allowedValues: new[] { "ENABLE", "DISABLE" }
                ),

                new FieldMetadata(
                    name: "BidType",
                    displayName: "Chế độ tối ưu",
                    dataType: "String",
                    category: "Basic Info",
                    description: "Chế độ tối ưu (CUSTOM: Target ROI, NO_BID: Maximum delivery)",
                    allowedValues: new[] { "CUSTOM", "NO_BID" }
                ),

                new FieldMetadata(
                    name: "RoasBid",
                    displayName: "Mục tiêu ROI",
                    dataType: "Decimal",
                    category: "Budget",
                    description: "Mục tiêu ROI"
                ),

                new FieldMetadata(
                    name: "TargetRoiBudget",
                    displayName: "Ngân sách Target ROI",
                    dataType: "Decimal",
                    category: "Budget",
                    description: "Ngân sách Target ROI"
                ),

                new FieldMetadata(
                    name: "MaxDeliveryBudget",
                    displayName: "Ngân sách Maximum delivery",
                    dataType: "Decimal",
                    category: "Budget",
                    description: "Ngân sách Maximum delivery"
                ),
            };
        }
    }
}
