﻿using System.Threading.Tasks;
using TikTok.Localization;
using TikTok.MultiTenancy;
using TikTok.Permissions;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Identity.Web.Navigation;
using Volo.Abp.SettingManagement.Web.Navigation;
using Volo.Abp.TenantManagement.Web.Navigation;
using Volo.Abp.UI.Navigation;

namespace TikTok.Web.Menus;

public class TikTokMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var administration = context.Menu.GetAdministration();

        var l = context.GetLocalizer<TikTokResource>();
        var factReport = new ApplicationMenuItem("Facts", l["Menu:Facts"], icon: "fas fa-table");

        context.Menu.Items.Insert(
            0,
            new ApplicationMenuItem(
                TikTokMenus.Home,
                l["Menu:Home"],
                "~/Dashboard",
                icon: "fas fa-home",
                order: 0
            )
        );

        //Business Applications
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.BusinessApplications,
                l["Menu:BusinessApplications"],
                "~/BusinessApplications",
                icon: "fa fa-mobile-alt",
                order: 1
            ).RequirePermissions(TikTokPermissions.BusinessApplications.Default)
        );

        context.Menu.AddItem(
           new ApplicationMenuItem(
               TikTokMenus.FactGmvMaxCampaign,
               l["Menu:FactGmvMaxCampaign"],
               "~/fact-gmv-max-campaign",
               icon: "fa fa-chart-line",
               order: 2
           ).RequirePermissions(TikTokPermissions.FactGmvMax.Default)
       );

        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.FactGmvMaxProduct,
                l["Menu:FactGmvMaxProduct"],
                "~/fact-gmv-max-product",
                icon: "fa fa-box",
                order: 3
            ).RequirePermissions(TikTokPermissions.FactGmvMax.Default)
        );

        // ✅ THÊM: GmvMax Dashboard menu item
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.GmvMax,
                l["Menu:GmvMax"],
                "~/GmvMax",
                icon: "fas fa-chart-pie",
                order: 4
            ).RequirePermissions(TikTokPermissions.FactGmvMax.Default)
        );

        //Balance Business Centers
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.BalanceBusinessCenters,
                l["Menu:BalanceBusinessCenters"],
                "~/BalanceBusinessCenters",
                icon: "fa fa-wallet",
                order: 5
            ).RequirePermissions(TikTokPermissions.BalanceBusinessCenters.Default)
        );

        //Balance Advertiser Accounts
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.BalanceAdAccounts,
                l["Menu:BalanceAdAccounts"],
                "~/BalanceAdAccounts",
                icon: "fa fa-credit-card",
                order: 6
            ).RequirePermissions(TikTokPermissions.BalanceAdAccounts.Default)
        );

        //Transactions
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.Transactions,
                l["Menu:Transactions"],
                "~/Transactions",
                icon: "fa fa-exchange-alt",
                order: 7
            ).RequirePermissions(TikTokPermissions.Transactions.Default)
        );

        //Business Centers
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.BusinessCenters,
                l["Menu:BusinessCenters"],
                "~/BusinessCenters",
                icon: "fa fa-building",
                order: 8
            ).RequirePermissions(TikTokPermissions.BusinessCenters.Default)
        );

        //Ad Accounts
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.AdAccounts,
                l["Menu:AdAccounts"],
                "~/AdAccounts",
                icon: "fa fa-ad",
                order: 9
            ).RequirePermissions(TikTokPermissions.AdAccounts.Default)
        );

        //Assets
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.Assets,
                l["Menu:Assets"],
                "~/Assets",
                icon: "fa fa-cube",
                order: 10
            ).RequirePermissions(TikTokPermissions.Assets.Default)
        );

        //Campaigns
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.Campaigns,
                l["Menu:Campaigns"],
                "~/Campaigns",
                icon: "fa fa-bullhorn",
                order: 11
            ).RequirePermissions(TikTokPermissions.Campaigns.Default)
        );

        //Cost Profiles
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.CostProfiles,
                l["Menu:CostProfiles"],
                "~/CostProfiles",
                icon: "fa fa-chart-line",
                order: 12
            ).RequirePermissions(TikTokPermissions.CostProfiles.Default)
        );

        //Report Integrated BCs
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.ReportIntegratedBcs,
                l["Menu:ReportIntegratedBcs"],
                "~/ReportIntegratedBcs",
                icon: "fa fa-chart-bar",
                order: 13
            ).RequirePermissions(TikTokPermissions.ReportIntegratedBcs.Default)
        );

        //Record Transaction AdAccounts
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.RecordTransactionAdAccounts,
                l["Menu:RecordTransactionAdAccounts"],
                "~/RecordTransactionAdAccounts",
                icon: "fa fa-list-alt",
                order: 14
            ).RequirePermissions(TikTokPermissions.RecordTransactionAdAccounts.Default)
        );

        //Record Transaction BCs
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.RecordTransactionBcs,
                l["Menu:RecordTransactionBcs"],
                "~/RecordTransactionBcs",
                icon: "fa fa-file-invoice-dollar",
                order: 15
            ).RequirePermissions(TikTokPermissions.RecordTransactionBcs.Default)
        );

        //Job Management
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.JobManagement,
                l["Menu:JobManagement"],
                "~/JobManagement/Dashboard",
                icon: "fa fa-cogs",
                order: 16
            ).RequirePermissions(TikTokPermissions.JobManagement.Default)
        );

        //Customers
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.Customers,
                l["Menu:Customers"],
                "~/Customers",
                icon: "fa fa-users",
                order: 17
            ).RequirePermissions(TikTokPermissions.Customers.Default)
        );

        //System Cache Monitor
        context.Menu.AddItem(
            new ApplicationMenuItem(
                "SystemCache",
                l["Menu:SystemCache"],
                "~/CacheMonitor",
                icon: "fas fa-database",
                order: 18
            ).RequirePermissions(TikTokPermissions.SystemCache.Monitor)
        );

        // Fact Reports
        factReport.AddItem(
            new ApplicationMenuItem(
                "FactReports",
                l["Menu:FactBalance"],
                "~/fact-balance",
                icon: "fa fa-chart-bar",
                order: 15
            )
        ).RequirePermissions(TikTokPermissions.FactBalances.Default);

        factReport.AddItem(
            new ApplicationMenuItem(
                "FactReports",
                l["Menu:FactCampaign"],
                "~/fact-campaign",
                icon: "fa fa-chart-bar",
                order: 16
            )
        ).RequirePermissions(TikTokPermissions.BusinessApplications.Default);

        factReport.AddItem(
            new ApplicationMenuItem(
                "FactReports",
                l["Menu:FactDailySpend"],
                "~/fact-daily-spend",
                icon: "fa fa-chart-bar",
                order: 17
            )
        ).RequirePermissions(TikTokPermissions.BusinessApplications.Default);


        context.Menu.AddItem(
                    new ApplicationMenuItem(
                        "NotificationRules",
                        l["Menu:NotificationRules"],
                        "~/NotificationRules",
                        icon: "bi bi-bell-fill",
                        order: 19
                    ).RequirePermissions(TikTokPermissions.NotificationRules.Default)
                );

        context.Menu.AddItem(
           new ApplicationMenuItem(
               TikTokMenus.SupportManagement,
               l["Menu:SupportManagement"],
               "~/SupportManagement",
               icon: "fa fa-headset",
               order: 20
           ).RequirePermissions(TikTokPermissions.SupportManagement.Default)
       );

        //User Access Management
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.UserAccessManagement,
                l["Menu:UserAccessManagement"],
                "~/UserAccessManagement",
                icon: "fa fa-user-shield",
                order: 21
            ).RequirePermissions(TikTokPermissions.AdAccountPermissionManagement.Default)
        );

        //System Notification Rules
        context.Menu.AddItem(
            new ApplicationMenuItem(
                TikTokMenus.SystemNotificationRules,
                l["Menu:SystemNotificationRules"],
                "~/SystemNotificationRules",
                icon: "fa fa-bell",
                order: 22
            ).RequirePermissions(TikTokPermissions.SystemNotificationRules.Default)
        );

        context.Menu.AddItem(factReport);

        if (MultiTenancyConsts.IsEnabled)
        {
            administration.SetSubItemOrder(TenantManagementMenuNames.GroupName, 1);
        }
        else
        {
            administration.TryRemoveMenuItem(TenantManagementMenuNames.GroupName);
        }

        administration.SetSubItemOrder(IdentityMenuNames.GroupName, 2);
        administration.SetSubItemOrder(SettingManagementMenuNames.GroupName, 3);


        return Task.CompletedTask;
    }
}
