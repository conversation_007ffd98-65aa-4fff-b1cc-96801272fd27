/**
 * Consumers Management Component for System Notification Rules
 * Handles consumers modal, data population, and consumer operations
 */

const ConsumersManager = {
    // Global variables for consumers management
    currentRuleId: null,
    currentRuleName: null,
    consumersData: null,
    consumersDataViewMode: localStorage.getItem('systemNotificationConsumersDataViewMode') || 'detail',

    // Initialize consumers manager
    init() {
        this.setupEventListeners();
    },

    // Open consumers modal
    openConsumersModal(ruleId, ruleName) {
        // Store current rule info globally for use in ad account selection
        this.currentRuleId = ruleId;
        this.currentRuleName = ruleName;
        
        console.log('ConsumersManager.openConsumersModal called with:', { ruleId, ruleName });
        console.log('ConsumersManager.currentRuleId set to:', this.currentRuleId);

        // Update modal title with rule name
        $('#systemNotificationRuleConsumersModalLabel').text(
            `${getLocalizedText('consumersTitle')}: ${ruleName}`
        );

        // Show loading state
        $('#systemNotificationAdAccountsList').html(
            '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> ' +
                getLocalizedText('loading') +
                '</div>'
        );
        $('#systemNotificationBusinessCentersList').html(
            '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> ' +
                getLocalizedText('loading') +
                '</div>'
        );

        // Load rule consumers
        SystemNotificationApiService.getRuleConsumers(ruleId)
            .then((response) => {
                this.consumersData = response;

                // Update counts
                $('#systemNotificationAdAccountsCount').text(
                    response.adAccounts?.length || 0
                );
                $('#systemNotificationBusinessCentersCount').text(
                    response.businessCenters?.length || 0
                );

                // Show/hide buttons based on permissions
                if (window.systemNotificationRulePermission?.canManageRule) {
                    $('#addSystemNotificationAdAccountBtn').show();
                    $('#selectAllSystemNotificationAdAccountsBtn').show();
                    $('#selectAllSystemNotificationBusinessCentersBtn').show();
                }

                // Set view mode
                this.toggleViewMode(this.consumersDataViewMode);
                if (this.consumersDataViewMode !== 'table') {
                    this.populateConsumersModal(response);
                }
            })
            .catch((error) => {
                console.error('Error loading system notification rule consumers:', error);
                $('#systemNotificationAdAccountsList').html(
                    '<div class="alert alert-danger">' +
                        getLocalizedText('loadConsumersError') +
                        '</div>'
                );
                $('#systemNotificationBusinessCentersList').html(
                    '<div class="alert alert-danger">' +
                        getLocalizedText('loadConsumersError') +
                        '</div>'
                );
            });

        // Show modal
        $('#systemNotificationRuleConsumersModal').modal('show');
    },

    // Populate consumers modal with data
    populateConsumersModal(data) {
        // Populate Ad Accounts
        const adAccounts = data.adAccounts || [];
        const adAccountsList = $('#systemNotificationAdAccountsList');
        const adAccountsCount = $('#systemNotificationAdAccountsCount');

        adAccountsCount.text(adAccounts.length);

        if (adAccounts.length === 0) {
            adAccountsList.html(`
                <div class="empty-state">
                    <i class="fas fa-ad"></i>
                    <p>${getLocalizedText('noAdAccounts')}</p>
                </div>
            `);
        } else {
            const adAccountsHtml = adAccounts
                .map((account) => `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card consumer-card" data-consumer-id="${account.id}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">${escapeHtml(
                                    account.adAccountName || getLocalizedText('notAvailable')
                                )}</h6>
                                <p class="card-text text-muted small mb-1">${getLocalizedText(
                                    'id'
                                )}: ${escapeHtml(
                    account.advertiserId || getLocalizedText('notAvailable')
                )}</p>
                                <p class="card-text text-muted small">${getLocalizedText(
                                    'bc'
                                )}: ${escapeHtml(
                    account.bcName || getLocalizedText('notAvailable')
                )}</p>
                            </div>
                            <div class="consumer-actions">
                                <input type="checkbox" class="form-check-input consumer-checkbox" data-consumer-id="${
                                    account.id
                                }">
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="ConsumersManager.removeConsumer('${
                                    account.id
                                }')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `)
                .join('');

            adAccountsList.html(adAccountsHtml);
        }

        // Populate Business Centers
        const businessCenters = data.businessCenters || [];
        const businessCentersList = $('#systemNotificationBusinessCentersList');
        const businessCentersCount = $('#systemNotificationBusinessCentersCount');

        businessCentersCount.text(businessCenters.length);

        if (businessCenters.length === 0) {
            businessCentersList.html(`
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <p>${getLocalizedText('noBusinessCenters')}</p>
                </div>
            `);
        } else {
            const businessCentersHtml = businessCenters
                .map((bc) => `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card consumer-card" data-consumer-id="${bc.id}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">${escapeHtml(
                                    bc.bcName || getLocalizedText('notAvailable')
                                )}</h6>
                                <p class="card-text text-muted small">${getLocalizedText(
                                    'id'
                                )}: ${escapeHtml(
                    bc.bcId || getLocalizedText('notAvailable')
                )}</p>
                            </div>
                            <div class="consumer-actions">
                                <input type="checkbox" class="form-check-input consumer-checkbox" data-consumer-id="${
                                    bc.id
                                }">
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="ConsumersManager.removeConsumer('${
                                    bc.id
                                }')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `)
                .join('');

            businessCentersList.html(businessCentersHtml);
        }
    },

    // Toggle view mode
    toggleViewMode(viewMode) {
        this.consumersDataViewMode = viewMode;
        localStorage.setItem('systemNotificationConsumersDataViewMode', viewMode);

        // Update dropdown active state
        $('#systemNotificationRuleConsumersViewModeDropdown .dropdown-item').removeClass('active');
        $(`#systemNotificationRuleConsumersViewModeDropdown .dropdown-item[data-mode="${viewMode}"]`).addClass('active');

        // Update dropdown button text
        const modeTexts = {
            simple: getLocalizedText('viewModeSimple'),
            detail: getLocalizedText('viewModeDetail'),
            table: getLocalizedText('viewModeTable'),
        };
        $('#systemNotificationViewModeDropdown').html(
            `<i class="fas fa-th-large"></i> ${modeTexts[viewMode]}`
        );

        if (this.consumersData) {
            if (viewMode === 'table') {
                this.populateConsumersModalWithTableView(this.consumersData);
            } else {
                this.populateConsumersModal(this.consumersData);
            }
        }
    },

    // Populate consumers modal with table view
    populateConsumersModalWithTableView(data) {
        // Similar to populateConsumersModal but with table view
        // Implementation would be similar to NotificationRules but with system notification specific data
        this.populateConsumersModal(data); // For now, use the same view
    },

    // Remove consumer
    removeConsumer(consumerId) {
        if (!this.currentRuleId) return;

        abp.message.confirm(
            getLocalizedText('deleteConsumerConfirm'),
            getLocalizedText('confirmDelete'),
            (result) => {
                if (result) {
                    const consumerIds = [consumerId];
                    SystemNotificationApiService.removeConsumers(this.currentRuleId, consumerIds)
                        .then(() => {
                            abp.notify.success(getLocalizedText('deleteConsumerSuccess'));
                            // Refresh the modal
                            this.openConsumersModal(this.currentRuleId, this.currentRuleName);
                        })
                        .catch((error) => {
                            console.error('Error removing system notification consumer:', error);
                            abp.notify.error(getLocalizedText('deleteConsumerError'));
                        });
                }
            }
        );
    },

    // Setup event listeners
    setupEventListeners() {
        // Handle consumers button click
        $(document).on('click', '[data-action="consumers"]', (e) => {
            const ruleId = $(e.currentTarget).data('id');
            const ruleName = $(e.currentTarget).data('name');
            this.openConsumersModal(ruleId, ruleName);
        });

        // Handle add ad account button
        $('#addSystemNotificationAdAccountBtn').on('click', () => {
            $('#selectSystemNotificationAdAccountsModal').modal('show');
            window.AdAccountSelector?.loadAvailableAdAccounts();
        });

        // Handle select all ad accounts
        $('#systemNotificationSelectAllAdAccounts').on('click', () => {
            $('#systemNotificationAvailableAdAccountsList input[type="checkbox"]').prop('checked', true);
        });

        // Handle deselect all ad accounts
        $('#systemNotificationDeselectAllAdAccounts').on('click', () => {
            $('#systemNotificationAvailableAdAccountsList input[type="checkbox"]').prop('checked', false);
        });

        // Reset global rule info when modal is hidden
        $('#systemNotificationRuleConsumersModal').on('hidden.bs.modal', () => {
            this.currentRuleId = null;
            this.currentRuleName = null;
        });
    }
};

// Export to global scope
window.ConsumersManager = ConsumersManager;
