using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Data;
using TikTok.DbMigrator.Consts;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Entities;
using TikTok.Entities.Dim;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace TikTok.DbMigrator.Data.Facts
{
    /// <summary>
    /// Data seeder cho FactGmvMaxProductCreative - Chi tiết hiệu suất từng creative/video
    /// Phụ<PERSON> vụ drill-down analysis theo creative cụ thể trong Product GMV Max Campaign
    /// </summary>
    public class FactGmvMaxProductCreativeDataSeeder : ICustomDataSeedContributor
    {
        private readonly IRepository<FactGmvMaxProductCreativeEntity, Guid> _factRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;
        private readonly IRepository<DimTTAccountEntity, Guid> _dimTTAccountRepository;
        private readonly IRepository<DimProductEntity, Guid> _dimProductRepository;
        private readonly ILogger<FactGmvMaxProductCreativeDataSeeder> _logger;

        public int Order { get; set; } = 13; // Sau FactGmvMaxCampaign (Order = 11) và FactGmvMaxProduct (Order = 12)

        public FactGmvMaxProductCreativeDataSeeder(
            IRepository<FactGmvMaxProductCreativeEntity, Guid> factRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IRepository<DimStoreEntity, Guid> dimStoreRepository,
            IRepository<DimTTAccountEntity, Guid> dimTTAccountRepository,
            IRepository<DimProductEntity, Guid> dimProductRepository,
            ILogger<FactGmvMaxProductCreativeDataSeeder> logger)
        {
            _factRepository = factRepository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _dimCampaignRepository = dimCampaignRepository;
            _dimStoreRepository = dimStoreRepository;
            _dimTTAccountRepository = dimTTAccountRepository;
            _dimProductRepository = dimProductRepository;
            _logger = logger;
        }

        public async Task RunSeedAsync(DataSeedContext context)
        {
            if (await _factRepository.AnyAsync())
            {
                _logger.LogDebug("FactGmvMaxProductCreative data already exists, skipping seeding.");
                return;
            }

            _logger.LogDebug("Starting FactGmvMaxProductCreative data seeding...");

            // Get existing dimension data
            var dimDates = await _dimDateRepository.GetListAsync();
            var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync();
            var dimAdAccounts = await _dimAdAccountRepository.GetListAsync();
            var dimCampaigns = await _dimCampaignRepository.GetListAsync();
            var dimStores = await _dimStoreRepository.GetListAsync();
            var dimTTAccounts = await _dimTTAccountRepository.GetListAsync();
            var dimProducts = await _dimProductRepository.GetListAsync();

            if (!dimDates.Any() || !dimBusinessCenters.Any() || !dimAdAccounts.Any() || 
                !dimCampaigns.Any() || !dimStores.Any() || !dimTTAccounts.Any() || !dimProducts.Any())
            {
                _logger.LogWarning("Required dimension data not found. Please seed all dimension tables first.");
                return;
            }

            var endDate = DateTime.Today;
            var startDate = endDate.AddDays(-30);
            var relevantDates = dimDates.Where(x => x.FullDate >= startDate && x.FullDate <= endDate).OrderBy(x => x.FullDate).ToList();

            var currentBusinessCenters = dimBusinessCenters.Where(x => x.IsCurrent).ToList();
            var currentAdAccounts = dimAdAccounts.Where(x => x.IsCurrent).ToList();
            var currentCampaigns = dimCampaigns.Where(x => x.IsCurrent).ToList();
            var currentStores = dimStores.ToList();
            var currentTTAccounts = dimTTAccounts.Where(x => x.IsCurrent).ToList();
            var currentProducts = dimProducts.ToList();

            if (!currentBusinessCenters.Any() || !currentAdAccounts.Any() || !currentCampaigns.Any() || 
                !currentStores.Any() || !currentTTAccounts.Any() || !currentProducts.Any())
            {
                _logger.LogWarning("Current dimension data not sufficient. Ensure IsCurrent records exist for all dimensions.");
                return;
            }

            // Build quick lookup by business keys
            var bcByBcId = currentBusinessCenters.GroupBy(b => b.BcId).ToDictionary(g => g.Key, g => g.First());
            var adByAdvertiserId = currentAdAccounts.GroupBy(a => a.AdvertiserId).ToDictionary(g => g.Key, g => g.First());
            var storeByStoreId = currentStores.GroupBy(s => s.StoreId).ToDictionary(g => g.Key, g => g.First());
            var ttAccountByTtAccountId = currentTTAccounts.GroupBy(t => t.TtAccountId).ToDictionary(g => g.Key, g => g.First());
            var productByProductId = currentProducts.GroupBy(p => p.ProductId).ToDictionary(g => g.Key, g => g.First());

            // Deterministic picker helper
            Func<int, int, int> pickIndex = (max, seed) => max == 0 ? 0 : Math.Abs(seed) % max;

            var toInsert = new List<FactGmvMaxProductCreativeEntity>();
            const int batchSize = 2000;

            _logger.LogDebug($"Generating product creative data for {currentCampaigns.Count} campaigns from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

            foreach (var campaign in currentCampaigns)
            {
                // Map campaign -> ad account via business key
                DimAdAccountEntity? adAccount = null;
                if (!string.IsNullOrWhiteSpace(campaign.AdvertiserId) && adByAdvertiserId.TryGetValue(campaign.AdvertiserId, out var mappedAd))
                {
                    adAccount = mappedAd;
                }
                else
                {
                    var idx = pickIndex(currentAdAccounts.Count, campaign.CampaignId.GetHashCode());
                    adAccount = currentAdAccounts[idx];
                }

                // Map ad account -> business center
                DimBusinessCenterEntity? businessCenter = null;
                if (!string.IsNullOrWhiteSpace(adAccount.OwnerBcId) && bcByBcId.TryGetValue(adAccount.OwnerBcId, out var mappedBc))
                {
                    businessCenter = mappedBc;
                }
                else
                {
                    var idx = pickIndex(currentBusinessCenters.Count, adAccount.AdvertiserId.GetHashCode());
                    businessCenter = currentBusinessCenters[idx];
                }

                if (adAccount == null || businessCenter == null)
                {
                    continue;
                }

                // Currency from ad account
                var currency = (adAccount.Currency ?? "USD").ToUpperInvariant();

                // Generate multiple creatives per campaign (2-5 creatives)
                var creativeCount = 2 + (Math.Abs(campaign.CampaignId.GetHashCode()) % 4);
                
                for (int creativeIndex = 0; creativeIndex < creativeCount; creativeIndex++)
                {
                    foreach (var dimDate in relevantDates)
                    {
                        var seedBase = HashCode.Combine(campaign.CampaignId, creativeIndex, dimDate.Id);

                        // Deterministically pick related entities
                        var storeIdx = pickIndex(currentStores.Count, seedBase);
                        var store = currentStores[storeIdx];

                        var ttAccountIdx = pickIndex(currentTTAccounts.Count, seedBase + 1);
                        var ttAccount = currentTTAccounts[ttAccountIdx];

                        var productIdx = pickIndex(currentProducts.Count, seedBase + 2);
                        var product = currentProducts[productIdx];

                        var record = CreateProductCreativePerformance(
                            campaign,
                            adAccount,
                            businessCenter,
                            store,
                            ttAccount,
                            product,
                            dimDate,
                            currency,
                            creativeIndex);

                        toInsert.Add(record);

                        if (toInsert.Count >= batchSize)
                        {
                            await _factRepository.InsertManyAsync(toInsert);
                            toInsert.Clear();
                        }
                    }
                }
            }

            if (toInsert.Count > 0)
            {
                await _factRepository.InsertManyAsync(toInsert);
                toInsert.Clear();
            }

            _logger.LogDebug("Successfully seeded FactGmvMaxProductCreative records.");
        }

        public Task SeedAsync(DataSeedContext context)
        {
#if DEBUG
            if (DbMigratorConst.IsEnableSeed)
            {
                return RunSeedAsync(context);
            }
#endif

            return Task.CompletedTask;
        }

        private FactGmvMaxProductCreativeEntity CreateProductCreativePerformance(
            DimCampaignEntity campaign,
            DimAdAccountEntity adAccount,
            DimBusinessCenterEntity businessCenter,
            DimStoreEntity store,
            DimTTAccountEntity ttAccount,
            DimProductEntity product,
            DimDateEntity dimDate,
            string currency,
            int creativeIndex)
        {
            var seed = HashCode.Combine(campaign.CampaignId, store.StoreId, ttAccount.TtAccountId, product.ProductId, dimDate.Id, creativeIndex);
            var random = new Random(seed);

            // Generate creative-specific data
            var itemId = GenerateItemId(random, creativeIndex);
            var creativeType = GetRandomCreativeType(random);
            var title = GenerateCreativeTitle(product.ProductName, creativeIndex, random);
            var shopContentType = GetRandomShopContentType(random);
            var creativeDeliveryStatus = GetRandomCreativeDeliveryStatus(random);

            // Generate performance metrics
            var cost = Math.Round((decimal)(random.NextDouble() * 2000 + 50), 2); // 50-2050
            var orders = Math.Max(0, random.Next(0, 20) + (int)(random.NextDouble() * 5));
            var grossRevenue = Math.Round(orders * (decimal)(50000 + random.NextDouble() * 500000), 2); // 50k-550k per order

            // Product engagement metrics
            var productImpressions = random.Next(1000, 100000);
            var productClicks = random.Next(10, Math.Min(productImpressions / 10, 5000));
            var productClickRate = productImpressions > 0 ? Math.Round((decimal)productClicks / productImpressions, 4) : 0;

            // Ad engagement metrics
            var adClickRate = 0.01m + (decimal)(random.NextDouble() * 0.05); // 1-6%
            var adConversionRate = 0.02m + (decimal)(random.NextDouble() * 0.15); // 2-17%

            // Video view rates
            var adVideoViewRate2s = 0.3m + (decimal)(random.NextDouble() * 0.4); // 30-70%
            var adVideoViewRate6s = adVideoViewRate2s * (0.6m + (decimal)(random.NextDouble() * 0.3)); // 60-90% of 2s rate
            var adVideoViewRateP25 = adVideoViewRate6s * (0.7m + (decimal)(random.NextDouble() * 0.2)); // 70-90% of 6s rate
            var adVideoViewRateP50 = adVideoViewRateP25 * (0.8m + (decimal)(random.NextDouble() * 0.15)); // 80-95% of 25% rate
            var adVideoViewRateP75 = adVideoViewRateP50 * (0.85m + (decimal)(random.NextDouble() * 0.1)); // 85-95% of 50% rate
            var adVideoViewRateP100 = adVideoViewRateP75 * (0.7m + (decimal)(random.NextDouble() * 0.2)); // 70-90% of 75% rate

            // Calculate KPIs
            decimal? roas = cost > 0 ? Math.Round(grossRevenue / cost, 4) : null;
            decimal? tacos = grossRevenue > 0 ? Math.Round(cost / grossRevenue, 4) : null;
            decimal? costPerOrder = orders > 0 ? Math.Round(cost / orders, 2) : null;

            // Currency conversion
            var (costVnd, costUsd) = ConvertCurrency(cost, currency);
            var (costPerOrderVnd, costPerOrderUsd) = ConvertCurrency(costPerOrder ?? 0m, currency);
            var (grossRevenueVnd, grossRevenueUsd) = ConvertCurrency(grossRevenue, currency);

            return new FactGmvMaxProductCreativeEntity
            {
                DimDateId = dimDate.Id,
                DimBusinessCenterId = businessCenter.Id,
                DimAdAccountId = adAccount.Id,
                DimCampaignId = campaign.Id,
                DimStoreId = store.Id,
                DimTTAccountId = ttAccount.Id,
                DimProductId = product.Id,
                CampaignId = campaign.CampaignId,
                StoreId = store.StoreId,
                BcId = businessCenter.BcId,
                AdvertiserId = adAccount.AdvertiserId,
                ItemGroupId = product.ProductId, // Using product ID as item group ID
                ItemId = itemId,
                CreativeType = creativeType,
                Title = title,
                ShopContentType = shopContentType,
                CreativeDeliveryStatus = creativeDeliveryStatus,
                Cost = cost,
                CostVND = Math.Round(costVnd, 2),
                CostUSD = Math.Round(costUsd, 2),
                Orders = orders,
                GrossRevenue = grossRevenue,
                GrossRevenueVND = Math.Round(grossRevenueVnd, 2),
                GrossRevenueUSD = Math.Round(grossRevenueUsd, 2),
                ProductImpressions = productImpressions,
                ProductClicks = productClicks,
                ProductClickRate = productClickRate,
                AdClickRate = adClickRate,
                AdConversionRate = adConversionRate,
                AdVideoViewRate2s = adVideoViewRate2s,
                AdVideoViewRate6s = adVideoViewRate6s,
                AdVideoViewRateP25 = adVideoViewRateP25,
                AdVideoViewRateP50 = adVideoViewRateP50,
                AdVideoViewRateP75 = adVideoViewRateP75,
                AdVideoViewRateP100 = adVideoViewRateP100,
                ROAS = roas,
                TACOS = tacos,
                CostPerOrder = costPerOrder,
                CostPerOrderVND = costPerOrder.HasValue ? Math.Round(costPerOrderVnd, 2) : null,
                CostPerOrderUSD = costPerOrder.HasValue ? Math.Round(costPerOrderUsd, 2) : null,
                Currency = currency,
                Date = dimDate.FullDate
            };
        }

        private string GenerateItemId(Random random, int creativeIndex)
        {
            // Item ID can be TikTok post ID or -1 for Product Card
            if (random.Next(100) < 70) // 70% chance of actual post ID
            {
                return random.Next(100000000, 999999999).ToString();
            }
            else // 30% chance of Product Card
            {
                return "-1";
            }
        }

        private string GetRandomCreativeType(Random random)
        {
            var types = new[] { "ADS_AND_ORGANIC", "ORGANIC", "REMOVED" };
            var weights = new[] { 60, 35, 5 }; // 60% ADS_AND_ORGANIC, 35% ORGANIC, 5% REMOVED
            
            var totalWeight = weights.Sum();
            var randomValue = random.Next(totalWeight);
            var currentWeight = 0;
            
            for (int i = 0; i < types.Length; i++)
            {
                currentWeight += weights[i];
                if (randomValue < currentWeight)
                {
                    return types[i];
                }
            }
            
            return types[0]; // Fallback
        }

        private string GenerateCreativeTitle(string productName, int creativeIndex, Random random)
        {
            var prefixes = new[] { "Amazing", "Best", "Top", "Premium", "Quality", "Trendy", "Popular", "New" };
            var suffixes = new[] { "Review", "Unboxing", "Test", "Demo", "Showcase", "Tutorial", "Guide", "Tips" };
            
            var prefix = prefixes[random.Next(prefixes.Length)];
            var suffix = suffixes[random.Next(suffixes.Length)];
            
            return $"{prefix} {productName} {suffix} #{creativeIndex + 1}";
        }

        private string GetRandomShopContentType(Random random)
        {
            var types = new[] { "VIDEO", "PRODUCT_CARD" };
            return types[random.Next(types.Length)];
        }

        private string GetRandomCreativeDeliveryStatus(Random random)
        {
            var statuses = new[] { "DELIVERING", "PAUSED", "DELIVERY_OK", "DELIVERY_FAILED", "AUDIT_FAILED" };
            var weights = new[] { 70, 10, 15, 3, 2 }; // Most are delivering
            
            var totalWeight = weights.Sum();
            var randomValue = random.Next(totalWeight);
            var currentWeight = 0;
            
            for (int i = 0; i < statuses.Length; i++)
            {
                currentWeight += weights[i];
                if (randomValue < currentWeight)
                {
                    return statuses[i];
                }
            }
            
            return statuses[0]; // Fallback
        }

        private (decimal vndAmount, decimal usdAmount) ConvertCurrency(decimal amount, string sourceCurrency)
        {
            var exchangeRates = new Dictionary<string, (decimal vndRate, decimal usdRate)>
            {
                { "VND", (1.0m, 0.000041m) },
                { "USD", (24350.0m, 1.0m) },
                { "EUR", (26500.0m, 1.09m) },
                { "GBP", (31000.0m, 1.27m) },
                { "JPY", (165.0m, 0.0068m) },
                { "KRW", (18.5m, 0.00076m) },
                { "CNY", (3400.0m, 0.14m) },
                { "SGD", (18000.0m, 0.74m) },
                { "THB", (680.0m, 0.028m) },
                { "MYR", (5200.0m, 0.21m) }
            };

            var key = (sourceCurrency ?? "USD").ToUpperInvariant();
            if (exchangeRates.TryGetValue(key, out var rates))
            {
                var vndAmount = amount * rates.vndRate;
                var usdAmount = amount * rates.usdRate;
                return (vndAmount, usdAmount);
            }

            return (amount * 24350.0m, amount);
        }
    }
}
