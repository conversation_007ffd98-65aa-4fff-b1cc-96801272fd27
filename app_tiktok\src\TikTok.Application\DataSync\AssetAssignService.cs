using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DataSync;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using Volo.Abp;

namespace TikTok.Application.DataSync
{
    /// <summary>
    /// Service chuyên trách cấp quyền tài sản
    /// </summary>
    public class AssetAssignService : BaseSyncService, IAssetAssignService
    {
        private readonly string _tiktokUserId;

        public AssetAssignService(IServiceProvider serviceProvider, ILogger<AssetAssignService> logger)
            : base(serviceProvider, logger)
        {
            _tiktokUserId = _configuration["TikTok:Auth:UserId"] ?? "";
        }

        /// <summary>
        /// Cấp quyền tài sản cho người dùng
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <param name="advertiserRole">Vai trò advertiser (bắt buộc khi asset_type là ADVERTISER)</param>
        /// <param name="catalogRole">Vai trò catalog (bắt buộc khi asset_type là CATALOG)</param>
        /// <param name="formLibraryRole">Vai trò form library (bắt buộc khi asset_type là LEAD)</param>
        /// <param name="ttAccountRoles">Vai trò TikTok account (bắt buộc khi asset_type là TT_ACCOUNT)</param>
        /// <param name="storeRole">Vai trò store (bắt buộc khi asset_type là TIKTOK_SHOP)</param>
        /// <returns>Kết quả cấp quyền</returns>
        public async Task<AssetAssignResult> AssignAssetToUserAsync(
            string bcId,
            string assetId,
            string userId = "",
            string assetType = "ADVERTISER",
            string advertiserRole = "ADMIN",
            string? catalogRole = null,
            string? formLibraryRole = null,
            List<string>? ttAccountRoles = null,
            string? storeRole = null)
        {
            var result = new AssetAssignResult();

            try
            {
                if (_tiktokUserId.IsNullOrEmpty())
                {
                    throw new BusinessException("ConfigError", "Chưa cấu hình TikTok User ID trong appsettings");
                }
                _logger.LogDebug("Bắt đầu cấp quyền tài sản {AssetId} loại {AssetType} cho user {UserId} trong BC: {BcId}",
                    assetId, assetType, userId, bcId);
                
                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Tạo request để cấp quyền tài sản
                var request = new TikTokBusinessApi.Models.AssignAssetRequest
                {
                    BcId = bcId,
                    AssetId = assetId,
                    UserId = userId.IsNullOrEmpty() ? _tiktokUserId : userId,
                    AssetType = assetType,
                    AdvertiserRole = advertiserRole,
                };

                // Gọi API để cấp quyền tài sản
                var response = await tikTokClient.BcAssets.AssignAssetAsync(request);

                // Kiểm tra response code
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Không thể cấp quyền tài sản: {response.Message}");
                }

                result.IsSuccess = true;
                result.AssetId = assetId;
                result.UserId = userId.IsNullOrEmpty()? _tiktokUserId:userId;
                result.AssetType = assetType;

                _logger.LogDebug("Hoàn thành cấp quyền tài sản {AssetId} loại {AssetType} cho user {UserId} trong BC: {BcId}",
                    assetId, assetType, userId, bcId);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.IsSuccess = false;
                _logger.LogError(ex, "Lỗi khi cấp quyền tài sản {AssetId} loại {AssetType} cho user {UserId} trong BC: {BcId}",
                    assetId, assetType, userId, bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi cấp quyền tài sản: {ex.Message}";
                result.IsSuccess = false;
                _logger.LogError(ex, "Lỗi khi cấp quyền tài sản {AssetId} loại {AssetType} cho user {UserId} trong BC: {BcId}",
                    assetId, assetType, userId, bcId);
            }

            return result;
        }
    }
}
