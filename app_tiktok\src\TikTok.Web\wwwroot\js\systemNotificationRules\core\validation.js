/**
 * Validation Functions for System Notification Rules
 * Client-side validation logic for forms and data
 */

// Function to setup real-time validation
function setupRealTimeValidation() {
    // Rule Name validation
    const ruleNameInput = document.getElementById('editRuleName');
    if (ruleNameInput) {
        ruleNameInput.addEventListener('blur', validateRuleName);
        ruleNameInput.addEventListener('input', clearRuleNameError);
    }
    
    // Entity Type validation
    const entityTypeSelect = document.getElementById('editEntityType');
    if (entityTypeSelect) {
        entityTypeSelect.addEventListener('change', validateEntityType);
    }
}

// Validation functions
function validateRuleName() {
    const input = document.getElementById('editRuleName');
    const value = input.value?.trim();
    
    if (!value) {
        showFieldError(input, 'editRuleNameError');
        return false;
    } else {
        clearFieldError(input, 'editRuleNameError');
        return true;
    }
}

function validateEntityType() {
    const select = document.getElementById('editEntityType');
    const value = select.value;
    
    if (!value) {
        showFieldError(select, 'editEntityTypeError');
        return false;
    } else {
        clearFieldError(select, 'editEntityTypeError');
        return true;
    }
}

function clearRuleNameError() {
    const input = document.getElementById('editRuleName');
    clearFieldError(input, 'editRuleNameError');
}

function showFieldError(field, errorElementId) {
    // Add error styling
    field.classList.add('is-invalid');
    
    // Show error message
    const errorDiv = document.getElementById(errorElementId);
    if (errorDiv) {
        errorDiv.style.display = 'block';
    }
}

function clearFieldError(field, errorElementId) {
    field.classList.remove('is-invalid');
    
    // Hide error message
    const errorDiv = document.getElementById(errorElementId);
    if (errorDiv) {
        errorDiv.style.display = 'none';
    }
}

function clearAllValidationErrors() {
    const ruleNameInput = document.getElementById('editRuleName');
    const entityTypeSelect = document.getElementById('editEntityType');
    
    if (ruleNameInput) clearFieldError(ruleNameInput, 'editRuleNameError');
    if (entityTypeSelect) clearFieldError(entityTypeSelect, 'editEntityTypeError');
}

// FE-side validation to mirror BE checks
function validateRulesBeforeSubmit(containerId) {
    const el = document.getElementById(containerId);
    const qb = el && el.__qb;
    if (!qb) return 'QueryBuilder is not initialized';
    const rule = qb.getValidRules(qb.rule);
    if (!rule || !rule.rules || !rule.rules.length)
        return 'Conditions are required';
    const fields = el.__fields || [];

    function findMeta(fieldName) {
        return fields.find(function (f) {
            return f.name === fieldName;
        });
    }

    function validateNode(node) {
        if (node.rules && Array.isArray(node.rules)) {
            for (var i = 0; i < node.rules.length; i++) {
                var err = validateNode(node.rules[i]);
                if (err) return err;
            }
            return null;
        }
        var field = node.field || '';
        var op = (node.operator || '')
            .toString()
            .toLowerCase()
            .replace(/\s+/g, '');
        var val = node.value;
        var meta = findMeta(field);
        if (!meta) return "Field '" + field + "' is not available";
        var dt = (meta.dataType || 'string').toLowerCase();

        // AllowedValues enforcement (single value)
        if (
            meta.allowedValues &&
            Array.isArray(meta.allowedValues) &&
            op !== 'between'
        ) {
            if (
                typeof val !== 'string' ||
                meta.allowedValues.indexOf(val) === -1
            ) {
                return (
                    "Value for '" +
                    field +
                    "' must be one of allowed values"
                );
            }
        }

        if (dt === 'number' || dt === 'decimal') {
            if (op === 'between') {
                if (!Array.isArray(val) || val.length !== 2)
                    return (
                        "Between requires two numbers for '" + field + "'"
                    );
                var n1 = Number(val[0]);
                var n2 = Number(val[1]);
                if (isNaN(n1) || isNaN(n2))
                    return (
                        "Between requires numeric values for '" +
                        field +
                        "'"
                    );
            } else {
                var n = Number(val);
                if (isNaN(n))
                    return "Value for '" + field + "' must be a number";
            }
        } else if (dt === 'boolean') {
            if (
                !(
                    val === true ||
                    val === false ||
                    val === 'true' ||
                    val === 'false'
                )
            ) {
                return "Value for '" + field + "' must be boolean";
            }
        } else if (dt === 'datetime' || dt === 'date') {
            function isDate(x) {
                return x && !isNaN(Date.parse(x));
            }
            if (op === 'between') {
                if (!Array.isArray(val) || val.length !== 2)
                    return "Between requires two dates for '" + field + "'";
                if (!isDate(val[0]) || !isDate(val[1]))
                    return (
                        "Between requires valid dates for '" + field + "'"
                    );
            } else {
                if (!isDate(val))
                    return "Value for '" + field + "' must be a valid date";
            }
        } else if (dt === 'string') {
            if (meta.allowedValues && Array.isArray(meta.allowedValues)) {
                if (
                    typeof val !== 'string' ||
                    meta.allowedValues.indexOf(val) === -1
                ) {
                    return (
                        "Value for '" +
                        field +
                        "' must be one of allowed values"
                    );
                }
            }
        }
        return null;
    }

    return validateNode(rule);
}

// Export functions to global scope for backward compatibility
window.setupRealTimeValidation = setupRealTimeValidation;
window.validateRuleName = validateRuleName;
window.validateEntityType = validateEntityType;
window.clearRuleNameError = clearRuleNameError;
window.showFieldError = showFieldError;
window.clearFieldError = clearFieldError;
window.clearAllValidationErrors = clearAllValidationErrors;
window.validateRulesBeforeSubmit = validateRulesBeforeSubmit;
