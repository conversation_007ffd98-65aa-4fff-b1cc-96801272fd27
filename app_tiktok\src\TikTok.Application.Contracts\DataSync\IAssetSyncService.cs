using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Assets;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu tài sản (Assets)
    /// </summary>
    public interface IAssetSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ tài sản theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAssetsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tài sản theo loại tài sản và BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAssetsByTypeAsync(string bcId, string assetType);

        /// <summary>
        /// Đồng bộ tất cả tài sản cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAllAssetsForAllBcsAsync();

        /// <summary>
        /// Cấp quyền tài sản cho người dùng
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <param name="advertiserRole">Vai trò advertiser (bắt buộc khi asset_type là ADVERTISER)</param>
        /// <param name="catalogRole">Vai trò catalog (bắt buộc khi asset_type là CATALOG)</param>
        /// <param name="formLibraryRole">Vai trò form library (bắt buộc khi asset_type là LEAD)</param>
        /// <param name="ttAccountRoles">Vai trò TikTok account (bắt buộc khi asset_type là TT_ACCOUNT)</param>
        /// <param name="storeRole">Vai trò store (bắt buộc khi asset_type là TIKTOK_SHOP)</param>
        /// <returns>Kết quả cấp quyền</returns>
        Task<AssetAssignResult> AssignAssetToUserAsync(
            string bcId,
            string assetId,
            string assetType="",
            string userId="",
            string? advertiserRole = null,
            string? catalogRole = null,
            string? formLibraryRole = null,
            List<string>? ttAccountRoles = null,
            string? storeRole = null);
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu tài sản
    /// </summary>
    public class AssetSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số loại tài sản đã đồng bộ
        /// </summary>
        public int AssetTypeCount { get; set; }

        /// <summary>
        /// Danh sách tài sản đã đồng bộ thành công
        /// </summary>
        public List<AssetDto> SyncedAssets { get; set; } = new List<AssetDto>();
    }

    /// <summary>
    /// Kết quả cấp quyền tài sản
    /// </summary>
    public class AssetAssignResult
    {
        /// <summary>
        /// Mã trạng thái
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Thông báo lỗi (nếu có)
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Thành công hay không
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// ID của tài sản
        /// </summary>
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// ID của người dùng
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Loại tài sản
        /// </summary>
        public string AssetType { get; set; } = string.Empty;
    }
}