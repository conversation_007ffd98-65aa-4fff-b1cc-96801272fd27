// Moved from TikTok.Application/Notification to SystemNotification namespace for centralized management
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.SystemNotification
{
    public class SystemNotificationBuildProvider : ITikTokNotificationBuildProvider, ITransientDependency
    {
        public string Context => "SystemNotification";

        public IEnumerable<TikTokUserType> AllowSendToUserTypes => new[]
        {
            TikTokUserType.BUSINESS_CENTER_ADMIN,
            TikTokUserType.ACCOUNT_MANAGER,
            TikTokUserType.ADVERTISER
        };

        public async Task<string> GetTemplateTitle(TikTokUserType userType, TikTokEntityType? entityType = null)
        {
            return await Task.FromResult("System Notification");
        }

        public async Task<IEnumerable<TikTokNotificationDto>> BuildNotifications(string objectId)
        {
            return await Task.FromResult(Enumerable.Empty<TikTokNotificationDto>());
        }

        public async Task<IEnumerable<TikTokNotificationDto>> BuildNotificationByUsers(
            string objectId,
            IEnumerable<TikTokNotificationUserDto> users)
        {
            var notifications = new List<TikTokNotificationDto>();

            foreach (var user in users)
            {
                // Prefer values from metadata populated upstream by TriggerService
                var hasMeta = user.Metadata != null;
                var metaTitle = hasMeta && user.Metadata.ContainsKey("Title") ? user.Metadata["Title"]?.ToString() : null;
                var metaMessage = hasMeta && user.Metadata.ContainsKey("Message") ? user.Metadata["Message"]?.ToString() : null;
                var redirect = hasMeta && user.Metadata.ContainsKey("RedirectUrl") ? user.Metadata["RedirectUrl"]?.ToString() ?? "/notifications" : "/notifications";

                // Hard guarantee: avoid generic defaults. If metadata is missing, synthesize friendly text from objectId.
                var fallbackCampaign = string.IsNullOrWhiteSpace(objectId) ? "-" : objectId;
                var title = !string.IsNullOrWhiteSpace(metaTitle)
                    ? metaTitle!
                    : $"Campaign {fallbackCampaign} - Thông báo hệ thống";
                var message = !string.IsNullOrWhiteSpace(metaMessage)
                    ? metaMessage!
                    : $"Campaign {fallbackCampaign} đang có cập nhật từ hệ thống. Vui lòng kiểm tra và xử lý.";

                var notification = new TikTokNotificationDto
                {
                    UserId = user.UserId,
                    UserType = user.UserType,
                    ObjectId = objectId,
                    Context = Context,
                    Title = title,
                    Content = message,
                    Payload = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        NotificationType = "SystemNotification",
                        TriggeredAt = DateTime.UtcNow,
                        ObjectId = objectId
                    }),
                    Metadata = new Dictionary<string, object>
                    {
                        ["NotificationType"] = "SystemNotification",
                        ["TriggeredAt"] = DateTime.UtcNow,
                        ["ObjectId"] = objectId,
                        ["Title"] = title,
                        ["Message"] = message,
                        ["Icon"] = "🔔",
                        ["Color"] = "#666666",
                        ["Priority"] = 2,
                        ["RedirectUrl"] = redirect
                    },
                    PhoneNumber = user.PhoneNumber,
                    AdAccountId = user.AdAccountId,
                    BcId = user.BcId
                };

                // Forward selected user metadata keys (EntityType, RuleId, CampaignId, AffectedCount) if present
                if (user.Metadata != null)
                {
                    void Forward(string key)
                    {
                        if (user.Metadata.ContainsKey(key))
                        {
                            notification.Metadata[key] = user.Metadata[key];
                        }
                    }

                    Forward("EntityType");
                    Forward("RuleId");
                    Forward("CampaignId");
                    Forward("AffectedCount");
                }

                notifications.Add(notification);
            }

            return await Task.FromResult(notifications);
        }
    }
}


