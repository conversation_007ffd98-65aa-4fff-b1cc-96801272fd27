using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using TikTok.Application.Contracts.SystemNotification.Realtime;

namespace TikTok.Web.Notification.Realtime
{
    /// <summary>
    /// Coalesces summary broadcasts per-user within a short window to avoid spamming clients.
    /// </summary>
    public class DebouncedRealtimeNotificationBroadcaster : IRealtimeNotificationBroadcaster
    {
        private readonly SignalRRealtimeNotificationBroadcaster _inner;
        private readonly IMemoryCache _cache;
        private readonly ILogger<DebouncedRealtimeNotificationBroadcaster> _logger;
        private static readonly TimeSpan DebounceWindow = TimeSpan.FromMilliseconds(400);

        private class AggregateState
        {
            public int UnreadDelta;
            public int Scheduling;
        }

        public DebouncedRealtimeNotificationBroadcaster(
            SignalRRealtimeNotificationBroadcaster inner,
            IMemoryCache cache,
            ILogger<DebouncedRealtimeNotificationBroadcaster> logger)
        {
            _inner = inner;
            _cache = cache;
            _logger = logger;
        }

        public Task BroadcastUserUpdatedAsync(Guid userId, string? context = null, string? objectId = null)
        {
            // For legacy listeners; keep immediate signal
            return _inner.BroadcastUserUpdatedAsync(userId, context, objectId);
        }

        public Task BroadcastUserSummaryAsync(Guid userId, NotificationSummaryUpdatedDto summary)
        {
            try
            {
                var key = $"notif_summary_{userId}";
                var state = _cache.GetOrCreate(key, entry =>
                {
                    entry.SlidingExpiration = TimeSpan.FromSeconds(5);
                    return new AggregateState();
                });

                Interlocked.Add(ref state.UnreadDelta, summary.UnreadDelta.GetValueOrDefault(0));

                // Schedule one flush within debounce window
                if (Interlocked.Exchange(ref state.Scheduling, 1) == 0)
                {
                    _ = FlushAfter(userId, key, DebounceWindow);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Debounce summary failed, fallback to immediate broadcast for {UserId}", userId);
                return _inner.BroadcastUserSummaryAsync(userId, summary);
            }

            return Task.CompletedTask;
        }

        private async Task FlushAfter(Guid userId, string key, TimeSpan delay)
        {
            await Task.Delay(delay);
            if (_cache.TryGetValue(key, out AggregateState? state) && state != null)
            {
                var delta = Interlocked.Exchange(ref state.UnreadDelta, 0);
                Interlocked.Exchange(ref state.Scheduling, 0);
                if (delta != 0)
                {
                    var payload = new NotificationSummaryUpdatedDto
                    {
                        UnreadDelta = delta
                    };
                    await _inner.BroadcastUserSummaryAsync(userId, payload);
                }
            }
        }
    }
}


