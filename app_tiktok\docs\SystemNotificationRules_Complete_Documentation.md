# 📋 System Notification Rules - Complete Documentation

## 🎯 Overview

System Notification Rules là một hệ thống thông báo tự động cho phép người dùng tạo các quy tắc (rules) để gửi thông báo khi có điều kiện nhất định xảy ra trên dữ liệu GMVMax Campaign và Product. Đặc điểm chính:

-   **Auto-generate Content**: Hệ thống tự động tạo nội dung thông báo
-   **Campaign-level Notifications**: Tất cả thông báo đều gửi ở cấp Campaign để giảm spam
-   **Cross-entity Support**: <PERSON><PERSON> thể cấu hình rule trên Product nhưng gửi thông báo Campaign
-   **Query Builder UI**: Giao diện trực quan để tạo điều kiện phức tạp

### Entity types (Raw-based)

-   Campaign (internal): `RawGmvMaxCampaignsEntity`
-   Product (internal): `RawGmvMaxProductCreativeReportEntity`

Lưu ý: Tên hiển thị trên UI vẫn giữ nguyên (GMV Max Campaign / GMV Max Product). Chỉ thay đổi mã entity nội bộ để đồng bộ dữ liệu từ nguồn Raw.

---

## 📁 File Structure & Architecture

### 🏗️ **1. Domain Layer (TikTok.Domain)**

#### **Entities**

```
TikTok.Domain/Entities/SystemNotification/
├── SystemNotificationRule.cs                    # Entity chính cho rules
└── SystemNotificationRuleConsumerEntity.cs      # Entity cho người nhận
```

**SystemNotificationRule.cs** - Core Entity:

```csharp
public class SystemNotificationRule : FullAuditedEntity<Guid>
{
    public string RuleName { get; set; }           // Tên rule
    public string EntityType { get; set; }         // Loại entity (FactGmvMaxCampaignEntity/ProductEntity)
    public string? ConditionsJson { get; set; }    // Điều kiện dạng JSON (Syncfusion QueryBuilder)
    public bool IsActive { get; set; } = true;     // Trạng thái hoạt động
    public bool IsDefault { get; set; } = false;   // Rule mặc định
    public bool IsPublic { get; set; } = false;    // Rule công khai
    public DateTime? LastTriggeredAt { get; set; } // Lần trigger cuối
    public long TriggerCount { get; set; } = 0;    // Số lần trigger
    public int CooldownMinutes { get; set; } = 60; // Thời gian nghỉ giữa các lần gửi
}
```

#### **Repositories**

```
TikTok.Domain/Repositories/
├── ISystemNotificationRuleRepository.cs          # Interface repository chính
└── ISystemNotificationRuleConsumerRepository.cs  # Interface repository consumer
```

---

### 🏗️ **2. Infrastructure Layer (TikTok.EntityFrameworkCore)**

#### **Repositories Implementation**

```
TikTok.EntityFrameworkCore/Repositories/
├── SystemNotificationRuleRepository.cs           # Implementation repository chính
└── SystemNotificationRuleConsumerRepository.cs   # Implementation repository consumer
```

**Key Features:**

-   **Search & Filter**: Hỗ trợ tìm kiếm theo nhiều tiêu chí
-   **Statistics**: Thống kê rules, triggers, performance
-   **Pagination**: Phân trang cho danh sách rules
-   **Soft Delete**: Xóa mềm với FullAuditedEntity

---

### 🏗️ **3. Application Contracts (TikTok.Application.Contracts)**

#### **DTOs**

```
TikTok.Application.Contracts/SystemNotification/
├── SystemNotificationRuleDto.cs                  # DTO chính
├── SystemNotificationRuleConsumerDto.cs          # Consumer DTO
├── SystemNotificationRequest.cs                  # Request DTO cho notification
└── [Create/Update/Search DTOs]                   # Các DTO cho CRUD operations
```

**SystemNotificationRequest.cs** - Core Request:

```csharp
public class SystemNotificationRequest
{
    public string Title { get; set; }              // Tiêu đề thông báo
    public string Message { get; set; }            // Nội dung thông báo
    public string EntityId { get; set; }           // ID của entity (Campaign ID)
    public string EntityType { get; set; }         // Loại entity (luôn là FactGmvMaxCampaignEntity)
    public Guid RuleId { get; set; }               // ID của rule
    public string RuleName { get; set; }           // Tên rule
    public Dictionary<string, object> Data { get; set; } // Dữ liệu bổ sung
}
```

#### **Service Interfaces**

```
TikTok.Application.Contracts/SystemNotification/
├── ISystemNotificationRuleAppService.cs          # Interface service chính
└── ISystemNotificationRuleConsumerAppService.cs  # Interface consumer service
```

---

### 🏗️ **4. Application Layer (TikTok.Application)**

#### **Core Services**

```
TikTok.Application/SystemNotification/
├── SystemNotificationRuleAppService.cs           # Main service - CRUD operations
├── SystemNotificationRuleConsumerAppService.cs   # Consumer management
├── SystemNotificationTriggerService.cs           # Rule triggering & notification sending
├── SystemRuleEngine.cs                           # Rule evaluation engine
├── SystemTemplateRenderer.cs                     # Template rendering
├── FieldDefinitionService.cs                     # Field metadata service
└── FieldDefinitionAppService.cs                  # Field definition API
```

#### **Key Services:**

**SystemNotificationTriggerService.cs** - Core Trigger:

```csharp
public class SystemNotificationTriggerService
{
    // Main entry point
    public async Task TriggerRuleCheck<T>(IEnumerable<T> entities, string entityType)

    // Process rules and group by Campaign
    private async Task<List<SystemNotificationRequest>> ProcessRulesWithCampaignGrouping<T>()

    // Group entities by CampaignId
    private Dictionary<string, List<T>> GroupEntitiesByCampaign<T>()

    // Build Campaign-level notification
    private Task<SystemNotificationRequest> BuildCampaignLevelNotification()
}
```

**SystemRuleEngine.cs** - Rule Evaluation:

```csharp
public class SystemRuleEngine
{
    // Evaluate rule against entity
    public bool EvaluateRule<T>(T entity, SystemNotificationRule rule)

    // Parse Syncfusion QueryBuilder JSON
    private bool EvaluateConditionsJson<T>(T entity, string conditionsJson)

    // Get property value using reflection
    public object? GetPropertyValue<T>(T entity, string propertyName)
}
```

---

### 🏗️ **5. HTTP API Layer (TikTok.HttpApi)**

#### **Controllers**

```
TikTok.HttpApi/Controllers/SystemNotification/
├── SystemNotificationRuleController.cs           # Main API controller
└── SystemNotificationRuleConsumerController.cs   # Consumer API controller
```

**API Endpoints:**

```csharp
[Route("api/system-notification-rules")]
public class SystemNotificationRuleController : TikTokController
{
    [HttpGet]                                      // GET /api/system-notification-rules
    [HttpPost]                                     // POST /api/system-notification-rules
    [HttpPut("{id}")]                             // PUT /api/system-notification-rules/{id}
    [HttpDelete("{id}")]                          // DELETE /api/system-notification-rules/{id}
    [HttpGet("fields/{entityType}")]              // GET /api/system-notification-rules/fields/{entityType}
    [HttpGet("statistics")]                       // GET /api/system-notification-rules/statistics
    [HttpPost("search")]                          // POST /api/system-notification-rules/search
}
```

---

### 🏗️ **6. Web Layer (TikTok.Web)**

#### **Frontend Pages**

```
TikTok.Web/Pages/SystemNotificationRules/
├── Index.cshtml                                  # Main Razor page
├── Index.cshtml.cs                               # Page model
├── Index.js                                      # Client-side logic
├── helper.js                                     # Helper functions
└── systemNotificationRulePermission.js           # Permission helpers
```

#### **Key Features:**

-   **Syncfusion QueryBuilder**: UI component cho tạo điều kiện
-   **Responsive Modal**: Form tạo/sửa rules
-   **Real-time Validation**: Validation client-side
-   **Multi-language Support**: i18n với vi.json/en.json

#### **Context Providers**

```
TikTok.Web/Notification/ContextProviders/
└── SystemNotificationContextProvider.cs          # Context provider cho notifications
```

---

### 🏗️ **7. Localization (TikTok.Domain.Shared)**

#### **Localization Files**

```
TikTok.Domain.Shared/Localization/TikTok/
├── vi.json                                       # Vietnamese translations
└── en.json                                       # English translations
```

**Key Localization Keys:**

```json
{
    "SystemNotificationRules:CreateRule": "Tạo Quy Tắc",
    "SystemNotificationRules:RuleName": "Tên Quy Tắc",
    "SystemNotificationRules:EntityType": "Loại Thực Thể",
    "SystemNotificationRules:Conditions": "Điều Kiện",
    "SystemNotificationRules:GMVMaxCampaign": "GMVMaxCampaign",
    "SystemNotificationRules:GMVMaxProduct": "GMVMaxProduct"
}
```

---

## 🔄 Workflow & Business Logic

### **1. Rule Creation Flow**

```
User Input → Frontend Validation → API Call → Service Validation → Database Save
     ↓
1. User fills form (Rule Name, Entity Type, Conditions)
2. Frontend validates required fields
3. API receives CreateSystemNotificationRuleDto
4. Service validates business rules
5. Entity saved to database with audit info
```

### **2. Rule Evaluation Flow**

```
Data Change → Trigger Service → Rule Engine → Campaign Grouping → Notification
     ↓
1. Business data changes (Campaign/Product updates)
2. SystemNotificationTriggerService.TriggerRuleCheck() called
3. SystemRuleEngine.EvaluateRule() checks conditions
4. Matching entities grouped by CampaignId
5. Campaign-level notifications generated and sent
```

### **3. Product → Campaign Notification Flow**

```
Product Entities → Rule Evaluation → Campaign Grouping → Campaign Notifications
     ↓
1. Input: List<ProductEntity> với EntityType = "FactGmvMaxProductEntity"
2. Evaluate: Check conditions against each Product (e.g., ProductClickRate > 5%)
3. Group: Group matching Products by CampaignId
4. Generate: Create Campaign-level notifications with affected Product count
5. Output: List<SystemNotificationRequest> với EntityType = "FactGmvMaxCampaignEntity"
```

### **4. Query Builder Integration**

```
User Interface → JSON Generation → Rule Storage → Runtime Evaluation
     ↓
1. User uses Syncfusion QueryBuilder UI
2. Component generates JSON conditions
3. JSON stored in ConditionsJson field
4. SystemRuleEngine parses JSON and evaluates against entities
```

---

## 🎨 UI/UX Features

### **Main Dashboard**

-   **DataTable**: Hiển thị danh sách rules với pagination
-   **Search & Filter**: Tìm kiếm theo tên, entity type, trạng thái
-   **Action Buttons**: Create, Edit, Delete, Copy, Toggle Active

### **Create/Edit Modal**

-   **Compact Layout**: Modal-lg với grid system
-   **Essential Fields**: Rule Name, Entity Type, Conditions
-   **Query Builder**: Syncfusion component cho điều kiện phức tạp
-   **Permission-based**: Chỉ hiện IsDefault/IsPublic cho admin

### **Responsive Design**

-   **Bootstrap Grid**: Responsive layout
-   **Mobile-friendly**: Touch-friendly buttons
-   **Consistent Styling**: Matching với NotificationRules UI

---

## 🔧 Technical Implementation Details

### **Rule Evaluation Engine**

```csharp
// JSON Condition Example
{
  "condition": "and",
  "rules": [
    {
      "field": "ProductClickRate",
      "operator": "greaterthan",
      "value": "5"
    },
    {
      "field": "Cost",
      "operator": "between",
      "value": ["100", "1000"]
    }
  ]
}
```

### **Campaign Grouping Logic**

```csharp
// Input: [Product1, Product2, Product3] với CampaignId = "123"
// Output: Campaign notification với 3 affected products
var campaignGroups = GroupEntitiesByCampaign(products, "FactGmvMaxProductEntity");
// Result: { "123": [Product1, Product2, Product3] }
```

### **Auto-generated Notification Content**

```csharp
// System automatically generates:
var title = $"Campaign Alert: {rule.RuleName}";
var message = $"Rule '{rule.RuleName}' triggered for Campaign {campaignId}. Affected items: {entities.Count}";
```

---

## 📊 Database Schema

### **SystemNotificationRule Table**

```sql
CREATE TABLE SystemNotificationRules (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    RuleName NVARCHAR(200) NOT NULL,
    EntityType NVARCHAR(100) NOT NULL,
    ConditionsJson NVARCHAR(4000),
    IsActive BIT DEFAULT 1,
    IsDefault BIT DEFAULT 0,
    IsPublic BIT DEFAULT 0,
    LastTriggeredAt DATETIME2,
    TriggerCount BIGINT DEFAULT 0,
    CooldownMinutes INT DEFAULT 60,
    CreationTime DATETIME2,
    CreatorId UNIQUEIDENTIFIER,
    -- Audit fields...
)
```

### **SystemNotificationRuleConsumers Table**

```sql
CREATE TABLE SystemNotificationRuleConsumers (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    RuleId UNIQUEIDENTIFIER,
    UserId UNIQUEIDENTIFIER,
    IsActive BIT DEFAULT 1,
    -- Audit fields...
)
```

---

## 🚀 Key Benefits

### **1. Reduced Notification Spam**

-   **Before**: 1000+ Product notifications
-   **After**: 10-50 Campaign notifications
-   **Reduction**: 95%+ fewer notifications

### **2. Simplified User Experience**

-   **Auto-generated Content**: No need to write templates
-   **Campaign-focused**: Managers see campaign-level alerts
-   **Intuitive UI**: QueryBuilder for complex conditions

### **3. Flexible Rule Configuration**

-   **Cross-entity Support**: Product rules → Campaign notifications
-   **Complex Conditions**: AND/OR logic with multiple fields
-   **Cooldown Management**: Prevent notification spam

### **4. Enterprise-ready**

-   **Permission-based**: Role-based access control
-   **Audit Trail**: Full audit logging
-   **Multi-tenant**: Tenant isolation
-   **Scalable**: Efficient database queries

---

## 🔮 Future Enhancements

### **Planned Features**

1. **Notification Channels**: Email, SMS, Push notifications
2. **Advanced Scheduling**: Time-based rules
3. **Template Customization**: User-defined templates
4. **Analytics Dashboard**: Rule performance metrics
5. **API Webhooks**: External system integration

### **Performance Optimizations**

1. **Caching**: Rule evaluation results
2. **Batch Processing**: Bulk notification sending
3. **Background Jobs**: Async rule processing
4. **Database Indexing**: Optimized queries

---

## 📝 Development Notes

### **Architecture Decisions**

-   **Campaign-level Default**: Chosen to reduce notification volume
-   **Auto-generated Content**: Simplified user experience
-   **JSON Conditions**: Flexible rule definition
-   **Repository Pattern**: Clean separation of concerns

### **Code Quality**

-   **SOLID Principles**: Clean architecture
-   **Async/Await**: Non-blocking operations
-   **Error Handling**: Comprehensive exception handling
-   **Unit Tests**: High test coverage (planned)

---

_Documentation created: December 2024_
_System Version: 1.0.0_
_Author: Development Team_

---

## 🔄 Changelog (Latest Updates)

-   Frontend (`SystemNotificationRules/Index.js`):

    -   Hard-code entity types for select: `FactGmvMaxCampaignEntity`, `FactGmvMaxProductEntity`.
    -   QueryBuilder columns built from backend `fields/{entityType}`; includes allowedValues with dropdown template.
    -   Client-side validation mirrors backend: numeric/date/between/boolean checks and allowedValues enforcement.
    -   Added entity change warning modal when switching entity type if conditions already exist.
    -   Consumers management UI added (Ad Accounts/Business Centers) with endpoints under `/api/app/system-notification-rules`.
    -   All list filters now use `keyword`, `entityType`, `isDefault`; legacy TargetEntity/Frequency removed.

-   Frontend helpers:

    -   `helper.js`: Defines API routes including consumers endpoints: `GET {ruleId}/consumers`, `POST add-consumers`, `DELETE {ruleId}/consumers`.
    -   `operatorConfig.js`: Maps operator sets per data type for Syncfusion QueryBuilder.

-   HTTP API:

    -   `SystemNotificationRuleController` (`/api/system-notification-rules`):
        -   Endpoints: GET list/detail, POST create, PUT update, DELETE, PATCH toggle-active, GET fields/{entityType}, GET statistics, POST validate, POST {id}/trigger, plus helpers: entity-types/operators/recipient-types/field-categories.
    -   `SystemNotificationRuleConsumerController` (`/api/app/system-notification-rules`):
        -   Endpoints: GET {ruleId}/consumers, POST add-consumers, DELETE {ruleId}/consumers.

-   Application Services:

    -   `SystemNotificationRuleAppService`:
        -   Uses `SystemNotificationFieldService` for fields; requires `ConditionsJson` in validation; mapping provides display names; supports manual trigger.
    -   `SystemNotificationFieldService`:
        -   Centralized field definitions with `FieldConfigurations/` for `FactGmvMaxCampaign` and `FactGmvMaxProduct`, cached per-entity.
    -   `SystemNotificationTriggerService`:
        -   Simplified: all notifications default to Campaign level; groups by `CampaignId`; updates `LastTriggeredAt`, `TriggerCount`.
    -   `SystemRuleEngine`:
        -   Evaluates only `ConditionsJson` (no legacy single-field); operators include Equals/Contains/StartsWith/EndsWith/IsEmpty/IsNotEmpty, numeric compares, Between/In, date helpers (IsToday/IsYesterday/IsThisWeek/IsThisMonth).

-   Repositories:

    -   `SystemNotificationRuleRepository`:
        -   Search criteria supports `EntityType`, `IsActive`, `CreatorId`, `CreatedAfter/Before`, `IsDefault`, `IsPublic`, `SearchText`; removed `HasCustomTemplate/TargetEntity/NotificationFrequency`.
        -   Statistics excludes custom template metrics; `GetRulesNeedingCooldownUpdateAsync` returns empty (cooldown minutes removed from schema).
    -   `SystemNotificationRuleConsumerRepository`:
        -   Query helpers for rule→consumers and consumer scoping (bc/adAccount) retained.

-   Domain Entities:

    -   `SystemNotificationRule` now includes: `RuleName`, `EntityType`, `ConditionsJson`, `IsActive`, `IsDefault`, `IsPublic`, `LastTriggeredAt`, `TriggerCount`.
    -   Removed: explicit cooldown minutes field from docs and repository logic.

-   Contracts:
    -   `ISystemNotificationRuleAppService` includes: list/get/create/update/delete/toggle/statistics/validate/trigger/fields.
    -   `FieldMetadata` enriched with `AllowedValues` to support dropdowns in QueryBuilder.

These updates align the docs with the current code: ConditionsJson-only evaluation, campaign-level notifications by default, simplified repository filters, and new consumers management endpoints.

### Recipient Resolution (SystemNotification)

-   AdAccount consumers → lấy advertiserId → gửi cho user có quyền `AdAccounts.Default/Edit` trên advertiser tương ứng.
-   BusinessCenter consumers → gom tất cả ad accounts thuộc BC → gửi cho user có quyền trên các advertiser đó.
-   Fallback: nếu không resolve được users, gửi cho `CreatorId` của rule.
-   Tối ưu: cache recipients theo `ruleId` trong một lượt xử lý, dedupe danh sách người nhận.
