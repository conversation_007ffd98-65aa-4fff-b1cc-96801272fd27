using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Facts.FactGmvMaxCampaign;
using TikTok.Facts.FactGmvMaxProductCreative;

namespace TikTok.Domain.Repositories
{
    /// <summary>
    /// Repository interface cho FactGmvMaxProductCreativeEntity với Dapper
    /// Tương tự IFactGmvMaxProductDapperRepository nhưng cho Creative level analysis
    /// </summary>
    public interface IFactGmvMaxProductCreativeDapperRepository
    {
        // ✅ Core Data Methods
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        
        // ✅ NEW: Enhanced method with additional filters for better performance
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetListByDateRangeWithFiltersAsync(
            DateTime from, 
            DateTime to, 
            List<string>? allowedAdvertiserIds = null, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? creativeType = null,
            string? itemGroupId = null,
            string? itemId = null);
        Task<IEnumerable<GmvMaxProductCreativeTrendDto>> GetTrendsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null);
        Task<IEnumerable<GmvMaxProductCreativeTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit, List<string>? allowedAdvertiserIds = null);
        
        // ✅ Dashboard Methods (tương tự Product)
        Task<GmvMaxProductCreativeDashboardDto> GetDashboardAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        
        // ✅ Section-specific methods for independent loading
        Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD", 
            List<string>? allowedAdvertiserIds = null,
            string? creativeType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null,
            string? itemGroupId = null,
            string? itemId = null);
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null);
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null);
        
        // ✅ NEW: Creative-specific methods
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativePerformanceAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativeDeliveryDto>> GetCreativeDeliveryAnalysisAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativeContentTypeDto>> GetContentTypeAnalysisAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<DashboardCreativeRanking>> GetCreativeRankingAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<DashboardAccountRanking>> GetAccountRankingAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        
        // ✅ NEW: Creative filtering methods
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByCreativeTypeAsync(DateTime from, DateTime to, string creativeType, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByContentTypeAsync(DateTime from, DateTime to, string shopContentType, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByDeliveryStatusAsync(DateTime from, DateTime to, string deliveryStatus, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByTikTokAccountAsync(DateTime from, DateTime to, string ttAccountName, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByProductGroupAsync(DateTime from, DateTime to, string itemGroupId, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        
        // ✅ NEW: Creative performance analysis methods
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetLowPerformanceCreativesAsync(DateTime from, DateTime to, decimal roasThreshold = 1.5m, decimal tacosThreshold = 30.0m, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetHighPerformanceCreativesAsync(DateTime from, DateTime to, decimal roasThreshold = 3.0m, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativeAlertsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD");
    }
}

