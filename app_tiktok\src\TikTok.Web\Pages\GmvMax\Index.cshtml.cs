using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TikTok.Permissions;
using Volo.Abp.Authorization.Permissions;

namespace TikTok.Web.Pages.GmvMax
{
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;
        private readonly IPermissionChecker _permissionChecker;
        
        // ✅ Permission properties for UI visibility - Unified FactGmvMax permissions
        public bool HasViewCampaignTab { get; set; }
        public bool HasViewVideoTab { get; set; }
        public bool HasViewSpending { get; set; }
        public bool HasViewMetrics { get; set; }
        public bool HasViewAll { get; set; }
        public bool HasViewAllAdvertisers { get; set; }

        // ✅ Properties to determine which tab should be active by default
        public bool ShouldVideoTabBeActive => 
            !string.IsNullOrEmpty(Tab) && Tab.Equals("video", StringComparison.OrdinalIgnoreCase);

        public bool ShouldCampaignTabBeActive => 
            !ShouldVideoTabBeActive && (HasViewSpending || HasViewMetrics || HasViewAll || HasViewAllAdvertisers);

        [BindProperty(SupportsGet = true)]
        public DateTime? FromDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? ToDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? Currency { get; set; }

        // Alternative parameter names for URL query
        [BindProperty(SupportsGet = true, Name = "from")]
        public DateTime? From { get; set; }

        [BindProperty(SupportsGet = true, Name = "to")]
        public DateTime? To { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? Tab { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? CampaignId { get; set; }

        public IndexModel(ILogger<IndexModel> logger, IPermissionChecker permissionChecker)
        {
            _logger = logger;
            _permissionChecker = permissionChecker;
        }

        public async Task OnGet()
        {
            try
            {
                // ✅ Check unified FactGmvMax permissions
                HasViewCampaignTab = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.Default);
                HasViewVideoTab = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewVideo);
                HasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                HasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                HasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                HasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // Get date parameters from URL query - support both naming conventions
                var startDateToDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                var endDateToDay = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);

                var from = FromDate ?? From ?? DateTime.Now.AddDays(-7); // Default to 7 days
                var to = ToDate ?? To ?? endDateToDay;
                this.From = from;
                this.To = to;

                // Set default currency if not specified
                if (string.IsNullOrEmpty(Currency))
                {
                    Currency = "USD"; // Default currency
                }

                _logger.LogInformation("GMV Max page loaded with date range {From} to {To}, currency {Currency}, and tab {Tab}", from, to, Currency, Tab);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing GMV Max page from {From} to {To}", From, To);
                ModelState.AddModelError("", $"Error initializing GMV Max page: {ex.Message}");
            }
        }
    }
}
