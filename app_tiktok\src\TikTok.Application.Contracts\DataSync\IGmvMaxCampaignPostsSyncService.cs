using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu GMV Max Campaign Posts
    /// </summary>
    public interface IGmvMaxCampaignPostsSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo Business Center ID
        /// Lấy tất cả campaignId và advertiserId từ bảng RawGmvMaxCampaignsEntity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsAsync(string bcId);

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo Campaign ID (method gốc được giữ lại cho tương thích)
        /// </summary>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="unauthorizedAdvertiserIds">Danh sách advertiser IDs không có quyền (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsByCampaignAsync(string campaignId, string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null);

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts cho nhiều Campaign của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="campaignIds">Danh sách Campaign IDs (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignPostsSyncResult> SyncManyGmvMaxCampaignPostsAsync(string advertiserId, string bcId, List<string>? campaignIds = null);

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign Posts cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignPostsSyncResult> SyncAllGmvMaxCampaignPostsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ GMV Max Campaign Posts
    /// </summary>
    public class GmvMaxCampaignPostsSyncResult : SyncResultBase
    {
        /// <summary>
        /// Tổng số bản ghi đã xử lý
        /// </summary>
        public override int TotalSynced => NewRecords + UpdatedRecords + ErrorRecords;

        /// <summary>
        /// Thời gian bắt đầu đồng bộ
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc đồng bộ
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Thời gian thực hiện (tính bằng giây)
        /// </summary>
        public double DurationSeconds => (EndTime - StartTime).TotalSeconds;

        /// <summary>
        /// Số Business Center đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Advertiser đã đồng bộ
        /// </summary>
        public int AdvertiserCount { get; set; }

        /// <summary>
        /// Số Campaign đã đồng bộ
        /// </summary>
        public int CampaignCount { get; set; }


        /// <summary>
        /// Danh sách advertiser IDs không có quyền truy cập
        /// </summary>
        public List<string> UnauthorizedAdvertiserIds { get; set; } = new List<string>();

        /// <summary>
        /// Tổng số trang đã xử lý
        /// </summary>
        public int TotalPagesProcessed { get; set; }

        /// <summary>
        /// Tổng số posts từ API
        /// </summary>
        public int TotalPostsFromApi { get; set; }
    }

}
