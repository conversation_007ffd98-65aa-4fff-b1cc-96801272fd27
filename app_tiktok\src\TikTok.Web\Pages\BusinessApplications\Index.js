$(function () {
    var l = abp.localization.getResource('TikTok');

    var dataTable = $('#BusinessApplicationsTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: true,
            paging: true,
            order: [[1, 'asc']],
            searching: true,
            scrollX: true,
            ajax: abp.libs.datatables.createAjax(
                tikTok.businessApplications.businessApplication.getList,
                function (data) {
                    // Add status filter to the request
                    var statusFilter = $('#StatusFilter').val();
                    if (statusFilter !== '') {
                        data.status = parseInt(statusFilter);
                    }
                    return data;
                }
            ),
            columnDefs: [
                {
                    title: l('BusinessApplication:ApplicationId'),
                    data: 'applicationId',
                },
                {
                    title: l('BcId'),
                    data: 'bcId',
                },
                {
                    title: l('BcName'),
                    data: 'bcName',
                },
                {
                    title: l('BusinessApplication:Comment'),
                    data: 'comment',
                },
                {
                    title: l('BusinessApplication:IsActive'),
                    data: 'isActive',
                    render: function (data) {
                        return data ? l('Yes') : l('No');
                    },
                },
                {
                    title: l('BusinessApplication:Status'),
                    data: 'status',
                    render: function (data) {
                        var statusText = '';
                        switch (data) {
                            case 0:
                                statusText = l(
                                    'BusinessApplication:Status:DEFAULT'
                                );
                                break;
                            case 1:
                                statusText = l(
                                    'BusinessApplication:Status:PENDING'
                                );
                                break;
                            case 2:
                                statusText = l(
                                    'BusinessApplication:Status:PROCESSING'
                                );
                                break;
                            case 3:
                                statusText = l(
                                    'BusinessApplication:Status:PROCESSED'
                                );
                                break;
                            case 4:
                                statusText = l(
                                    'BusinessApplication:Status:ERROR'
                                );
                                break;
                        }
                        return statusText;
                    },
                },
                {
                    title: l('BusinessApplication:AccessTokenCreatedAt'),
                    data: 'accessTokenCreatedAt',
                    render: function (data) {
                        return data
                            ? moment(data).format('YYYY-MM-DD HH:mm:ss')
                            : '';
                    },
                },
                {
                    title: l('Authorize'),
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        return (
                            '<button class="btn btn-sm btn-success authorize-btn" data-app-id="' +
                            row.applicationId +
                            '" data-business-id="' +
                            row.id +
                            '">' +
                            '<i class="fas fa-key"></i> ' +
                            l('Authorize') +
                            '</button>'
                        );
                    },
                },
                {
                    title: l('BetaAuthorization'),
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        return (
                            '<button class="btn btn-sm btn-warning beta-authorize-btn" data-app-id="' +
                            row.applicationId +
                            '" data-business-id="' +
                            row.id +
                            '">' +
                            '<i class="fas fa-flask"></i> ' +
                            l('BetaAuthorization') +
                            '</button>'
                        );
                    },
                },
                {
                    title: l('Actions'),
                    rowAction: {
                        items: [
                            {
                                text: l('Edit'),
                                action: function (data) {
                                    editModal.open({ id: data.record.id });
                                },
                            },
                            {
                                text: l('Delete'),
                                confirmMessage: function (data) {
                                    return l(
                                        'BusinessApplicationDeletionConfirmationMessage',
                                        data.record.applicationId
                                    );
                                },
                                action: function (data) {
                                    tikTok.businessApplications.businessApplication
                                        .delete(data.record.id)
                                        .then(function () {
                                            abp.notify.info(
                                                l('SuccessfullyDeleted')
                                            );
                                            dataTable.ajax.reload();
                                        });
                                },
                            },
                            {
                                text: l('SyncBusinessCenter'),
                                action: function (data) {
                                    createJob(data.record.id, 1);
                                },
                            },
                            {
                                text: l('SyncTransaction'),
                                action: function (data) {
                                    createJob(data.record.id, 2);
                                },
                            },
                            {
                                text: l('SyncBalance'),
                                action: function (data) {
                                    createJob(data.record.id, 3);
                                },
                            },
                            {
                                text: l('SyncAsset'),
                                action: function (data) {
                                    createJob(data.record.id, 4);
                                },
                            },
                            {
                                text: l('SyncCampaign'),
                                action: function (data) {
                                    createJob(data.record.id, 5);
                                },
                            },
                            {
                                text: l('SyncReportIntegratedAdAccount'),
                                action: function (data) {
                                    createJob(data.record.id, 6);
                                },
                            },
                            {
                                text: l('SyncReportIntegratedCampaign'),
                                action: function (data) {
                                    createJob(data.record.id, 7);
                                },
                            },
                            {
                                text: l('SyncReportIntegratedAdGroup'),
                                action: function (data) {
                                    createJob(data.record.id, 8);
                                },
                            },
                            {
                                text: l('SyncLatestBalance'),
                                action: function (data) {
                                    createJob(data.record.id, 9);
                                },
                            },
                            {
                                text: l('SyncDetailedAdAccount'),
                                action: function (data) {
                                    createJob(data.record.id, 10);
                                },
                            },
                            {
                                text: l('SyncGmvMax'),
                                action: function (data) {
                                    createJob(data.record.id, 11);
                                },
                            },
                            {
                                text: l('SyncRawToFact'),
                                action: function (data) {
                                    syncRawToFact(data.record.id);
                                },
                            },
                            {
                                text: l('SyncBackSettings'),
                                action: function (data) {
                                    openSyncBackModal(
                                        data.record.id,
                                        data.record.bcId
                                    );
                                },
                            },
                            {
                                text: l('UpdateAdAccountsFromAssets'),
                                action: function (data) {
                                    updateAdAccountsFromAssets(data.record.id);
                                },
                            },
                            {
                                text: l('SyncGmvMaxIdentities'),
                                action: function (data) {
                                    createJob(data.record.id, 12);
                                },
                            },
                        ],
                    },
                },
            ],
        })
    );

    var createModal = new abp.ModalManager(
        abp.appPath + 'BusinessApplications/CreateModal'
    );
    var editModal = new abp.ModalManager(
        abp.appPath + 'BusinessApplications/EditModal'
    );
    var betaAuthModal = new abp.ModalManager(
        abp.appPath + 'BusinessApplications/BetaAuthModal'
    );
    // Modal cài đặt đồng bộ lùi được định nghĩa trực tiếp trong HTML

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewBusinessApplicationButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });

    // Show/hide clear button based on input content
    $('#BusinessApplicationsTable_filter').on('input', function () {
        if (this.value.length > 0) {
            $('.clear-search-btn').show();
        } else {
            $('.clear-search-btn').hide();
        }
    });

    // Handle Enter key for search
    $('#BusinessApplicationsTable_filter').on('keypress', function (e) {
        if (e.which === 13) {
            // Enter key
            dataTable.search(this.value).draw();
        }
    });

    // Clear search functionality
    $('.clear-search-btn').click(function () {
        $('#BusinessApplicationsTable_filter').val('');
        dataTable.search('').draw();
        $(this).hide();
    });

    // Status filter functionality
    $('#StatusFilter').change(function () {
        dataTable.ajax.reload();

        // Show/hide clear button for status filter
        if (this.value !== '') {
            $('.clear-status-btn').show();
        } else {
            $('.clear-status-btn').hide();
        }
    });

    // Clear status filter functionality
    $('.clear-status-btn').click(function () {
        $('#StatusFilter').val('');
        dataTable.ajax.reload();
        $(this).hide();
    });

    // Event handler cho nút authorize
    $('#BusinessApplicationsTable').on('click', '.authorize-btn', function (e) {
        e.preventDefault();
        var appId = $(this).data('app-id');
        var businessId = $(this).data('business-id');
        authorizeWithTikTok(appId, businessId);
    });

    // Event handler cho nút beta authorize
    $('#BusinessApplicationsTable').on(
        'click',
        '.beta-authorize-btn',
        function (e) {
            e.preventDefault();
            var appId = $(this).data('app-id');
            var businessId = $(this).data('business-id');
            betaAuthModal.open({ appId: appId, businessId: businessId });
        }
    );

    function authorizeWithTikTok(appId, businessId) {
        abp.message.confirm(
            l('AuthorizeConfirmationMessage'),
            l('Authorize'),
            function (isConfirmed) {
                if (isConfirmed) {
                    // Lấy redirect URI từ window object thay vì gọi API
                    var redirectUri = window.tikTokRedirectUri;

                    if (!redirectUri) {
                        abp.notify.error(l('RedirectUriNotConfigured'));
                        return;
                    }

                    // Tạo TikTok authorization URL
                    var authUrl =
                        'https://business-api.tiktok.com/portal/auth' +
                        '?app_id=' +
                        encodeURIComponent(appId) +
                        '&state=' +
                        encodeURIComponent(businessId) +
                        '&redirect_uri=' +
                        encodeURIComponent(redirectUri);

                    // Redirect đến TikTok authorization
                    window.location.href = authUrl;
                }
            }
        );
    }

    // Function để tạo job cho CommandType cụ thể
    function createJob(businessApplicationId, commandType) {
        abp.message.confirm(
            l('CreateJobConfirmationMessage'),
            l('CreateJob'),
            function (isConfirmed) {
                if (isConfirmed) {
                    tikTok.businessApplications.businessApplication
                        .createJob(businessApplicationId, commandType)
                        .then(function (result) {
                            if (result) {
                                abp.notify.success(l('JobCreatedSuccessfully'));
                            } else {
                                abp.notify.warning(l('JobAlreadyExists'));
                            }
                        })
                        .catch(function (error) {
                            abp.notify.error(l('JobCreationFailed'));
                            console.error('Error creating job:', error);
                        });
                }
            }
        );
    }

    // Function để đồng bộ Raw to Fact
    function syncRawToFact(businessApplicationId) {
        abp.message.confirm(
            l('SyncRawToFactConfirmationMessage'),
            l('SyncRawToFact'),
            function (isConfirmed) {
                if (isConfirmed) {
                    abp.ui.setBusy();
                    tikTok.businessApplications.businessApplication
                        .syncRawToFact(businessApplicationId)
                        .then(function (result) {
                            abp.ui.clearBusy();
                            var message =
                                l('SyncRawToFactCompleted') +
                                ' BC: ' +
                                result.businessCenterCount +
                                ', AdAccounts: ' +
                                result.adAccountCount +
                                ', New: ' +
                                result.newRecords +
                                ', Updated: ' +
                                result.updatedRecords;
                            abp.notify.success(message);
                        })
                        .catch(function (error) {
                            abp.ui.clearBusy();
                            abp.notify.error(l('SyncRawToFactFailed'));
                            console.error('Error syncing Raw to Fact:', error);
                        });
                }
            }
        );
    }

    // Function để mở modal cài đặt đồng bộ lùi
    function openSyncBackModal(businessApplicationId, bcId) {
        // Reset form
        $('#SyncBackForm')[0].reset();

        // Set business application ID and BcId
        $('#SyncBackForm').data(
            'business-application-id',
            businessApplicationId
        );
        $('#SyncBackForm').data('bc-id', bcId);

        // Mở modal
        $('#SyncBackModal').modal('show');
    }

    // Event handler cho nút bắt đầu đồng bộ lùi
    $('#StartSyncBackButton').click(function (e) {
        e.preventDefault();

        var businessApplicationId = $('#SyncBackForm').data(
            'business-application-id'
        );
        var bcId = $('#SyncBackForm').data('bc-id');
        var startDate = $('#SyncBackStartDate').val();
        var endDate = $('#SyncBackEndDate').val();
        var selectedCommandTypes = [];

        // Lấy các CommandType được chọn
        $('input[name="CommandTypes"]:checked').each(function () {
            selectedCommandTypes.push(parseInt($(this).val()));
        });

        // Validation
        if (!startDate || !endDate) {
            abp.notify.error(l('PleaseSelectStartAndEndDate'));
            return;
        }

        if (selectedCommandTypes.length === 0) {
            abp.notify.error(l('PleaseSelectAtLeastOneCommandType'));
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            abp.notify.error(l('StartDateMustBeBeforeEndDate'));
            return;
        }

        // Hiển thị thông tin đã chọn
        var commandTypeNames = [];
        selectedCommandTypes.forEach(function (type) {
            var name = '';
            switch (type) {
                case 1:
                    name = l('SyncBusinessCenter');
                    break;
                case 2:
                    name = l('SyncTransaction');
                    break;
                case 3:
                    name = l('SyncBalance');
                    break;
                case 4:
                    name = l('SyncAsset');
                    break;
                case 5:
                    name = l('SyncCampaign');
                    break;
                case 6:
                    name = l('SyncReportIntegratedAdAccount');
                    break;
                case 7:
                    name = l('SyncReportIntegratedCampaign');
                    break;
                case 8:
                    name = l('SyncReportIntegratedAdGroup');
                    break;
                case 9:
                    name = l('SyncLatestBalance');
                    break;
                case 10:
                    name = l('SyncDetailedAdAccount');
                    break;
                case 11:
                    name = l('SyncGmvMax');
                    break;
                case 12:
                    name = l('SyncGmvMaxIdentities');
                    break;
            }
            commandTypeNames.push(name);
        });

        var confirmMessage =
            l('SyncBackConfirmationMessage') +
            '\n' +
            l('StartDate') +
            ': ' +
            startDate +
            '\n' +
            l('EndDate') +
            ': ' +
            endDate +
            '\n' +
            l('CommandTypes') +
            ': ' +
            commandTypeNames.join(', ');

        abp.message.confirm(
            confirmMessage,
            l('SyncBackSettings'),
            function (isConfirmed) {
                if (isConfirmed) {
                    // Đóng modal
                    $('#SyncBackModal').modal('hide');

                    // Gọi API để xử lý đồng bộ lùi
                    abp.ui.setBusy();
                    tikTok.businessApplications.businessApplication
                        .createSyncBackJobs({
                            bcId: bcId,
                            startDate: startDate,
                            endDate: endDate,
                            commandTypes: selectedCommandTypes,
                        })
                        .then(function (result) {
                            abp.ui.clearBusy();
                            if (result.isSuccess) {
                                abp.notify.success(result.message);
                            } else {
                                abp.notify.warning(result.message);
                            }
                        })
                        .catch(function (error) {
                            abp.ui.clearBusy();
                            abp.notify.error(l('SyncBackJobsCreationFailed'));
                            console.error(
                                'Error creating sync back jobs:',
                                error
                            );
                        });
                }
            }
        );
    });

    // Function để cập nhật AdAccounts từ Assets
    function updateAdAccountsFromAssets(businessApplicationId) {
        abp.message.confirm(
            l('UpdateAdAccountsFromAssetsConfirmationMessage'),
            l('UpdateAdAccountsFromAssets'),
            function (isConfirmed) {
                if (isConfirmed) {
                    abp.ui.setBusy();
                    tikTok.businessApplications.businessApplication
                        .updateAdAccountsFromAssets(businessApplicationId)
                        .then(function (result) {
                            abp.ui.clearBusy();
                            var message =
                                l('UpdateAdAccountsFromAssetsCompleted') +
                                ' AdAccounts: ' +
                                result.adAccountCount +
                                ', New: ' +
                                result.newRecords +
                                ', Updated: ' +
                                result.updatedRecords;
                            abp.notify.success(message);
                        })
                        .catch(function (error) {
                            abp.ui.clearBusy();
                            abp.notify.error(
                                l('UpdateAdAccountsFromAssetsFailed')
                            );
                            console.error(
                                'Error updating AdAccounts from Assets:',
                                error
                            );
                        });
                }
            }
        );
    }
});
