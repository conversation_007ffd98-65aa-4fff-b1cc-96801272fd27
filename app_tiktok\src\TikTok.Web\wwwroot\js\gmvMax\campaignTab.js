/**
 * Campaign Tab Manager
 * Copy HOÀN TOÀN logic từ factGmvMaxCampaign.js và factGmvMaxProduct.js
 */

class CampaignTabManager {
    constructor(config, dataAggregator, preProcessedFilters = {}) {
        this.config = config;
        this.dataAggregator = dataAggregator;
        this.roiCards = null;

        // ✅ COPY HOÀN TOÀN từ factGmvMaxCampaign.js và factGmvMaxProduct.js
        this.productPivotTable = null;
        this.creativePivotTable = null;
        this.livePivotTable = null;

        // ✅ B2 & B3: Initialize filters with pre-processed values from URL
        this.currentFilters = {
            fromDate: config.dateRange.from,
            toDate: config.dateRange.to,
            currency: this.getCurrentCurrency(),
            campaignIds: preProcessedFilters.campaignIds || [], // ✅ Campaign filter from URL
            shopIds: [], // Shop filter
            searchText: '',
        };

        // Store URL params for reference
        this.urlParams = preProcessedFilters.urlParams || {};

        console.log(
            '🎯 CampaignTabManager initialized with filters:',
            this.currentFilters
        );

        this.isInitialized = false;
    }

    /**
     * Get current currency from localStorage (same as FactGmvMaxCampaign)
     */
    getCurrentCurrency() {
        return (
            (typeof localStorage !== 'undefined'
                ? localStorage.getItem('tiktok_currency')
                : null) || 'USD'
        );
    }

    /**
     * Initialize Campaign Tab
     */
    async init() {
        try {
            // Setup filter components
            this.setupFilterComponents();

            // Setup event listeners
            this.setupEventListeners();

            // Setup currency change listener (like FactGmvMaxCampaign)
            this.setupCurrencyChangeListener();

            // Initialize ROI Cards component
            this.roiCards = new RoiCardsComponent();

            // Load dropdown data for filters
            await this.loadDropdownData();

            this.isInitialized = true;
        } catch (error) {
            console.error('❌ Error initializing Campaign Tab:', error);
            throw error;
        }
    }

    /**
     * Load campaign data
     */
    async loadData(filters = null) {
        try {
            if (!this.isInitialized) {
                await this.init();
            }

            // Update filters if provided
            if (filters) {
                this.currentFilters = { ...this.currentFilters, ...filters };
            }
            // Show loading states for all sections immediately
            // ✅ TEMPORARILY DISABLED: Product loading states
            // this.showProductLoadingState();
            this.showCreativeLoadingState();
            this.showLiveLoadingState();
            // this.showProductPivotLoadingState();
            this.showCreativePivotLoadingState();
            this.showLivePivotLoadingState();

            // 🔥 IMPROVED: Load data sections independently for better UX

            // Load pivot data first (needed for pivot tables)
            // ✅ TEMPORARILY DISABLED: Product data fetching
            // const productPivotData = await this.dataAggregator.fetchProductData(
            //     this.currentFilters
            // );

            // Get Creative data for pivot tables
            const creativePivotData =
                await this.dataAggregator.fetchProductCreativeData(
                    this.currentFilters
                );
            console.log('🔍 CampaignTab - Creative data fetched:', {
                hasData: !!creativePivotData,
                factsCount:
                    creativePivotData?.factGmvMaxProductCreatives?.length || 0,
                sample: creativePivotData?.factGmvMaxProductCreatives?.[0],
            });

            // Get Live data for pivot tables
            const liveROIData = await this.dataAggregator.fetchCampaignData({
                ...this.currentFilters,
                shoppingAdsType: 'LIVE',
            });

            // 🆕 Load all sections in parallel - each handles its own loading state
            // ✅ TEMPORARILY DISABLED: Product sections - can be restored anytime
            await Promise.all([
                // this.loadProductRoiAnalysis(this.currentFilters), // ✅ DISABLED: Product ROI cards
                this.loadCreativeRoiAnalysis(this.currentFilters), // Will hide Creative cards loading when done
                this.loadLiveRoiAnalysis(this.currentFilters), // Will hide Live cards loading when done
                // this.loadProductPivotTable(productPivotData), // ✅ DISABLED: Product pivot table
                this.loadCreativePivotTable(creativePivotData), // Will hide Creative pivot loading when done
                this.loadLivePivotTable(liveROIData), // Will hide Live pivot loading when done
            ]);

            // Note: Individual sections now handle their own loading states
            // No need for global hideLoadingState() here
        } catch (error) {
            console.error('❌ Error loading Campaign Tab data:', error);
            // Hide loading states for all sections in case of general error
            // ✅ TEMPORARILY DISABLED: Product loading states
            // this.hideProductLoadingState();
            this.hideCreativeLoadingState();
            this.hideLiveLoadingState();
            // this.hideProductPivotLoadingState();
            this.hideCreativePivotLoadingState();
            this.hideLivePivotLoadingState();
            this.showErrorMessage('Lỗi tải dữ liệu campaign: ' + error.message);
        }
    }

    /**
     * 🆕 Load Product ROI analysis cards using dedicated API
     */
    async loadProductRoiAnalysis(filters) {
        try {
            // Note: Loading state is already shown by loadData()

            // 🆕 Fetch ROI analysis from dedicated API
            const productRoiAnalysis =
                await this.dataAggregator.fetchRoiAnalysis({
                    ...filters,
                    shoppingAdsType: 'PRODUCT',
                });

            // Render ROI cards to Product section container
            this.roiCards.renderToContainer(
                'product-roi-cards-container',
                productRoiAnalysis,
                'PRODUCT'
            );

            // Hide Product loading state after successful render
            this.hideProductLoadingState();
        } catch (error) {
            console.error('❌ Error loading Product ROI analysis:', error);
            // Hide loading state on error
            this.hideProductLoadingState();
            throw error;
        }
    }

    /**
     * 🆕 Load Creative ROI analysis cards using dedicated API
     * ✅ COPY NGUYÊN logic từ Product Cards - dùng shoppingAdsType: 'PRODUCT'
     */
    async loadCreativeRoiAnalysis(filters) {
        try {
            // Note: Loading state is already shown by loadData()

            // ✅ Sử dụng NGUYÊN logic lấy dữ liệu của Product Cards
            const creativeRoiAnalysis =
                await this.dataAggregator.fetchRoiAnalysis({
                    ...filters,
                    shoppingAdsType: 'PRODUCT', // ✅ COPY từ Product - không sửa gì cả
                });

            // Render ROI cards to Creative section container
            this.roiCards.renderToContainer(
                'creative-roi-cards-container',
                creativeRoiAnalysis,
                'PRODUCT' // ✅ Sửa từ 'CREATIVE' thành 'PRODUCT' để match với shoppingAdsType
            );

            // Hide Creative loading state after successful render
            this.hideCreativeLoadingState();
        } catch (error) {
            console.error('❌ Error loading Creative ROI analysis:', error);
            // Hide loading state on error
            this.hideCreativeLoadingState();
            throw error;
        }
    }

    /**
     * 🆕 Load Live ROI analysis cards using dedicated API
     */
    async loadLiveRoiAnalysis(filters) {
        try {
            // Note: Loading state is already shown by loadData()

            // 🆕 Fetch ROI analysis from dedicated API
            const liveRoiAnalysis = await this.dataAggregator.fetchRoiAnalysis({
                ...filters,
                shoppingAdsType: 'LIVE',
            });

            // Render ROI cards to Live section container
            this.roiCards.renderToContainer(
                'live-roi-cards-container',
                liveRoiAnalysis,
                'LIVE'
            );

            // Hide Live loading state after successful render
            this.hideLiveLoadingState();
        } catch (error) {
            console.error('❌ Error loading Live ROI analysis:', error);
            // Hide loading state on error
            this.hideLiveLoadingState();
            throw error;
        }
    }

    /**
     * 🆕 Load Product Pivot Table with dedicated loading state
     */
    async loadProductPivotTable(productPivotData) {
        try {
            // Note: Loading state is already shown by loadData()

            // Set data vào global scope như factGmvMaxProduct.js
            window.initialGmvMaxProductDataForGmvMax = productPivotData;

            // Initialize Product Pivot Table
            await this.initializeProductPivotTableOnly();

            // Hide Product pivot loading state after successful initialization
            this.hideProductPivotLoadingState();
        } catch (error) {
            console.error('❌ Error loading Product Pivot Table:', error);
            // Hide loading state on error
            this.hideProductPivotLoadingState();
            throw error;
        }
    }

    /**
     * 🆕 Load Creative Pivot Table with dedicated loading state
     */
    async loadCreativePivotTable(creativePivotData) {
        try {
            // Note: Loading state is already shown by loadData()

            // Set data vào global scope
            window.initialGmvMaxCreativeDataForGmvMax = creativePivotData;

            // Initialize Creative Pivot Table
            await this.initializeCreativePivotTableOnly();

            // Hide Creative pivot loading state after successful initialization
            this.hideCreativePivotLoadingState();
        } catch (error) {
            console.error('❌ Error loading Creative Pivot Table:', error);
            // Hide loading state on error
            this.hideCreativePivotLoadingState();
            throw error;
        }
    }

    /**
     * 🆕 Load Live Pivot Table with dedicated loading state
     */
    async loadLivePivotTable(liveROIData) {
        try {
            // Note: Loading state is already shown by loadData()

            // Set data vào global scope như factGmvMaxCampaign.js
            window.initialGmvMaxCampaignDataForGmvMax = liveROIData;

            // Initialize Live Pivot Table
            await this.initializeLivePivotTableOnly();

            // Hide Live pivot loading state after successful initialization
            this.hideLivePivotLoadingState();
        } catch (error) {
            console.error('❌ Error loading Live Pivot Table:', error);
            // Hide loading state on error
            this.hideLivePivotLoadingState();
            throw error;
        }
    }

    /**
     * @deprecated Legacy method - replaced by separate Product/Live methods
     */
    async loadRoiAnalysis(campaignData) {
        console.warn(
            '⚠️ loadRoiAnalysis() is deprecated. Use loadProductRoiAnalysis() and loadLiveRoiAnalysis() instead.'
        );
    }

    /**
     * Setup filter components (similar to VideoTabManager)
     */
    setupFilterComponents() {
        try {
            // Initialize Syncfusion DateRangePicker
            const dateRangePicker = new ej.calendars.DateRangePicker({
                startDate: new Date(this.currentFilters.fromDate),
                endDate: new Date(this.currentFilters.toDate),
                format: 'dd/MM/yyyy',
                placeholder: 'Chọn khoảng thời gian',
                change: (args) => {
                    if (args.startDate && args.endDate) {
                        this.currentFilters.fromDate = args.startDate
                            .toISOString()
                            .split('T')[0];
                        this.currentFilters.toDate = args.endDate
                            .toISOString()
                            .split('T')[0];
                    }
                },
            });
            dateRangePicker.appendTo('#campaign-date-range-picker');

            // Initialize Quick Date Dropdown
            const quickDateOptions = [
                { text: 'Chọn khoảng', value: '' },
                { text: 'Hôm nay', value: 'today' },
                { text: '7 ngày', value: '7d' },
                { text: '30 ngày', value: '30d' },
                { text: 'Quý này', value: '90d' },
            ];

            const quickDateDropdown = new ej.dropdowns.DropDownList({
                dataSource: quickDateOptions,
                fields: { text: 'text', value: 'value' },
                placeholder: 'Lọc nhanh',
                change: (args) => {
                    if (args.value) {
                        this.setQuickDateFilter(
                            args.value,
                            'campaign-date-range-picker'
                        );
                    }
                },
            });
            quickDateDropdown.appendTo('#campaign-quick-date-dropdown');

            // Initialize Text Search
            const keywordSearch = document.getElementById(
                'campaign-keyword-search'
            );
            if (keywordSearch) {
                // ✅ Clarify searchable fields in placeholder
                keywordSearch.setAttribute(
                    'placeholder',
                    'Tìm theo BCId, StoreId, CampaignId, AdvertiserId'
                );
                keywordSearch.addEventListener('input', (e) => {
                    this.currentFilters.searchText = e.target.value || '';
                });

                // Add Enter key support for quick search
                keywordSearch.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.loadData(this.currentFilters);
                    }
                });
            }

            // Initialize Campaign MultiSelect
            const campaignMultiSelect = new ej.dropdowns.MultiSelect({
                placeholder: 'Chọn campaign',
                mode: 'CheckBox',
                showCheckBox: true,
                showSelectAll: true,
                enableSelectionOrder: false,
                fields: { text: 'text', value: 'value' }, // ✅ Đảm bảo sử dụng value field
                change: (args) => {
                    this.currentFilters.campaignIds = args.value || [];
                },
            });
            campaignMultiSelect.appendTo('#campaign-campaign-multiselect');

            // Initialize Shop MultiSelect
            const shopMultiSelect = new ej.dropdowns.MultiSelect({
                placeholder: 'Chọn shop',
                mode: 'CheckBox',
                showCheckBox: true,
                showSelectAll: true,
                enableSelectionOrder: false,
                fields: { text: 'text', value: 'value' }, // ✅ Đảm bảo sử dụng value field
                change: (args) => {
                    this.currentFilters.shopIds = args.value || [];
                },
            });
            shopMultiSelect.appendTo('#campaign-shop-multiselect');
        } catch (error) {
            console.error(
                '❌ Error setting up campaign filter components:',
                error
            );
        }
    }

    /**
     * Setup event listeners for campaign filters and actions
     */
    setupEventListeners() {
        // Apply filters button
        const applyFiltersBtn = document.getElementById(
            'apply-campaign-filters'
        );
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.loadData(this.currentFilters);
            });
        }

        // Clear filters button
        const clearFiltersBtn = document.getElementById(
            'clear-campaign-filters'
        );
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // ✅ Product section action buttons
        const refreshProductBtn = document.getElementById(
            'refresh-product-data'
        );
        if (refreshProductBtn) {
            refreshProductBtn.addEventListener('click', () => {
                this.loadData(this.currentFilters);
            });
        }

        const toggleProductChartBtn = document.getElementById(
            'toggle-product-chart'
        );
        if (toggleProductChartBtn) {
            toggleProductChartBtn.addEventListener('click', () => {
                this.toggleProductChart();
            });
        }

        const exportProductExcelBtn = document.getElementById(
            'export-product-excel'
        );
        if (exportProductExcelBtn) {
            exportProductExcelBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportProductToExcel();
            });
        }

        const exportProductPdfBtn =
            document.getElementById('export-product-pdf');
        if (exportProductPdfBtn) {
            exportProductPdfBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportProductToPdf();
            });
        }

        // ✅ Creative section action buttons
        const refreshCreativeBtn = document.getElementById(
            'refresh-creative-data'
        );
        if (refreshCreativeBtn) {
            refreshCreativeBtn.addEventListener('click', () => {
                this.loadData(this.currentFilters);
            });
        }

        const toggleCreativeChartBtn = document.getElementById(
            'toggle-creative-chart'
        );
        if (toggleCreativeChartBtn) {
            toggleCreativeChartBtn.addEventListener('click', () => {
                this.toggleCreativeChart();
            });
        }

        const exportCreativeExcelBtn = document.getElementById(
            'export-creative-excel'
        );
        if (exportCreativeExcelBtn) {
            exportCreativeExcelBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportCreativeToExcel();
            });
        }

        const exportCreativePdfBtn = document.getElementById(
            'export-creative-pdf'
        );
        if (exportCreativePdfBtn) {
            exportCreativePdfBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportCreativeToPdf();
            });
        }

        // ✅ Live section action buttons
        const refreshLiveBtn = document.getElementById('refresh-live-data');
        if (refreshLiveBtn) {
            refreshLiveBtn.addEventListener('click', () => {
                this.loadData(this.currentFilters);
            });
        }

        const toggleLiveChartBtn = document.getElementById('toggle-live-chart');
        if (toggleLiveChartBtn) {
            toggleLiveChartBtn.addEventListener('click', () => {
                this.toggleLiveChart();
            });
        }

        const exportLiveExcelBtn = document.getElementById('export-live-excel');
        if (exportLiveExcelBtn) {
            exportLiveExcelBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportLiveToExcel();
            });
        }

        const exportLivePdfBtn = document.getElementById('export-live-pdf');
        if (exportLivePdfBtn) {
            exportLivePdfBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportLiveToPdf();
            });
        }
    }

    /**
     * Setup currency change listener (like FactGmvMaxCampaign)
     */
    setupCurrencyChangeListener() {
        // Listen for jQuery currencyChanged event (dispatched by global handler)
        $(document).on('currencyChanged', (event, newCurrency) => {
            // Update current filters
            this.currentFilters.currency = newCurrency;

            // Reload data with new currency
            this.loadData(this.currentFilters);
        });
    }

    /**
     * Load dropdown data for filters using new APIs
     */
    async loadDropdownData() {
        try {
            // Load campaigns and shops data in parallel
            const [campaignsResponse, shopsResponse] = await Promise.all([
                fetch('/api/dim-campaigns?format=simple'),
                fetch('/api/dim-stores?format=simple'),
            ]);

            // Handle campaigns
            if (campaignsResponse.ok) {
                const campaigns = await campaignsResponse.json();
                const campaignMultiSelect = document.querySelector(
                    '#campaign-campaign-multiselect'
                );
                if (
                    campaignMultiSelect &&
                    campaignMultiSelect.ej2_instances &&
                    campaigns.length > 0
                ) {
                    const multiSelectObj = campaignMultiSelect.ej2_instances[0];
                    multiSelectObj.dataSource = campaigns;
                    multiSelectObj.dataBind();

                    // ✅ Set pre-selected value from URL if available
                    if (
                        this.currentFilters.campaignIds &&
                        this.currentFilters.campaignIds.length > 0
                    ) {
                        multiSelectObj.value = this.currentFilters.campaignIds;
                    }
                } else if (campaigns.length === 0) {
                    this.showEmptyCampaignDropdownState();
                }
            } else {
                console.warn(
                    'Failed to load campaigns:',
                    campaignsResponse.statusText
                );
                this.showEmptyCampaignDropdownState();
            }

            // Handle shops
            if (shopsResponse.ok) {
                const shops = await shopsResponse.json();
                const shopMultiSelect = document.querySelector(
                    '#campaign-shop-multiselect'
                );
                if (
                    shopMultiSelect &&
                    shopMultiSelect.ej2_instances &&
                    shops.length > 0
                ) {
                    const multiSelectObj = shopMultiSelect.ej2_instances[0];
                    multiSelectObj.dataSource = shops;
                    multiSelectObj.dataBind();
                } else if (shops.length === 0) {
                    this.showEmptyShopDropdownState();
                }
            } else {
                console.warn('Failed to load shops:', shopsResponse.statusText);
                this.showEmptyShopDropdownState();
            }
        } catch (error) {
            console.error('❌ Error loading dropdown data:', error);
            this.showEmptyDropdownState();
        }
    }

    /**
     * Show empty state when dropdown data fails to load
     */
    showEmptyDropdownState() {
        this.showEmptyCampaignDropdownState();
        this.showEmptyShopDropdownState();
    }

    /**
     * Show empty state for campaign dropdown
     */
    showEmptyCampaignDropdownState() {
        try {
            const campaignMultiSelect = document.querySelector(
                '#campaign-campaign-multiselect'
            );
            if (campaignMultiSelect && campaignMultiSelect.ej2_instances) {
                const multiSelectObj = campaignMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = [
                    { text: 'Không có dữ liệu campaign', value: '' },
                ];
                multiSelectObj.dataBind();
            }
        } catch (error) {
            console.error(
                '❌ Error showing empty campaign dropdown state:',
                error
            );
        }
    }

    /**
     * Show empty state for shop dropdown
     */
    showEmptyShopDropdownState() {
        try {
            const shopMultiSelect = document.querySelector(
                '#campaign-shop-multiselect'
            );
            if (shopMultiSelect && shopMultiSelect.ej2_instances) {
                const multiSelectObj = shopMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = [
                    { text: 'Không có dữ liệu shop', value: '' },
                ];
                multiSelectObj.dataBind();
            }
        } catch (error) {
            console.error('❌ Error showing empty shop dropdown state:', error);
        }
    }

    /**
     * Clear all campaign filters
     */
    clearFilters() {
        this.currentFilters = {
            fromDate: this.config.dateRange.from,
            toDate: this.config.dateRange.to,
            currency: this.config.currency,
            campaignIds: [],
            shopIds: [],
            searchText: '',
        };

        // Reset filter components
        try {
            // Reset DateRangePicker
            const dateRangePicker = document.getElementById(
                'campaign-date-range-picker'
            );
            if (dateRangePicker && dateRangePicker.ej2_instances) {
                const dateRangeObj = dateRangePicker.ej2_instances[0];
                dateRangeObj.startDate = new Date(this.config.dateRange.from);
                dateRangeObj.endDate = new Date(this.config.dateRange.to);
            }

            // Reset Quick Date Dropdown
            const quickDateDropdown = document.getElementById(
                'campaign-quick-date-dropdown'
            );
            if (quickDateDropdown && quickDateDropdown.ej2_instances) {
                const quickDateObj = quickDateDropdown.ej2_instances[0];
                quickDateObj.value = '';
            }

            // Reset Text Search
            const keywordSearch = document.getElementById(
                'campaign-keyword-search'
            );
            if (keywordSearch) {
                keywordSearch.value = '';
                keywordSearch.setAttribute(
                    'placeholder',
                    'Tìm theo BCId, StoreId, CampaignId, AdvertiserId'
                );
            }

            // Reset Campaign MultiSelect
            const campaignMultiSelect = document.getElementById(
                'campaign-campaign-multiselect'
            );
            if (campaignMultiSelect && campaignMultiSelect.ej2_instances) {
                const campaignObj = campaignMultiSelect.ej2_instances[0];
                campaignObj.value = [];
            }

            // Reset Shop MultiSelect
            const shopMultiSelect = document.getElementById(
                'campaign-shop-multiselect'
            );
            if (shopMultiSelect && shopMultiSelect.ej2_instances) {
                const shopObj = shopMultiSelect.ej2_instances[0];
                shopObj.value = [];
            }
        } catch (error) {
            console.error(
                '❌ Error resetting campaign filter components:',
                error
            );
        }

        // Reload data
        this.loadData();
    }

    /**
     * Set quick date filter (copy from VideoTabManager)
     */
    setQuickDateFilter(value, dateRangePickerId) {
        try {
            const dateRangePicker = document.getElementById(dateRangePickerId);
            if (!dateRangePicker || !dateRangePicker.ej2_instances) {
                console.warn('DateRangePicker not found or not initialized');
                return;
            }

            const dateRangeObj = dateRangePicker.ej2_instances[0];
            const today = new Date();
            let startDate, endDate;

            switch (value) {
                case 'today':
                    startDate = new Date(today);
                    endDate = new Date(today);
                    break;
                case '7d':
                    startDate = new Date(
                        today.getTime() - 6 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                case '30d':
                    startDate = new Date(
                        today.getTime() - 29 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                case '90d':
                    startDate = new Date(
                        today.getTime() - 89 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                default:
                    return;
            }

            // Update DateRangePicker
            if (dateRangeObj && startDate && endDate) {
                dateRangeObj.startDate = startDate;
                dateRangeObj.endDate = endDate;

                // Update current filters
                this.currentFilters.fromDate = startDate
                    .toISOString()
                    .split('T')[0];
                this.currentFilters.toDate = endDate
                    .toISOString()
                    .split('T')[0];
            }
        } catch (error) {
            console.error(
                '❌ Error setting campaign quick date filter:',
                error
            );
        }
    }

    /**
     * ✅ Load pivot tables với real data từ APIs - COPY pattern từ màn hình hiện có
     */
    async loadPivotTables(productPivotData, liveROIData) {
        try {
            // ✅ Set data vào global scope như factGmvMaxProduct.js
            window.initialGmvMaxProductDataForGmvMax = productPivotData;

            // ✅ Set Live Campaign data vào global scope như factGmvMaxCampaign.js
            window.initialGmvMaxCampaignDataForGmvMax = liveROIData;

            // ✅ Initialize Product Pivot giống hệt factGmvMaxProduct.js
            await this.initializeProductPivotTable();

            // ✅ Initialize Live Campaign Pivot giống hệt factGmvMaxCampaign.js
            await this.initializeLivePivotTable();
        } catch (error) {
            console.error(
                '❌ Error loading pivot tables with real data:',
                error
            );
            throw error;
        }
    }

    /**
     * ✅ Initialize Product Pivot Table without loading management (for synchronized loading)
     */
    async initializeProductPivotTableOnly() {
        try {
            // ✅ COPY class TiktokGmvMaxProductPivotTable từ factGmvMaxProduct.js
            this.productPivotTable = new TiktokGmvMaxProductPivotTableForGmvMax(
                'productCampaignPivotTable'
            );
            await this.productPivotTable.initial();
        } catch (error) {
            console.error('❌ Error initializing Product Pivot Table:', error);
            throw error;
        }
    }

    /**
     * ✅ COPY HOÀN TOÀN: Initialize Product Pivot Table như factGmvMaxProduct.js
     * @deprecated Use initializeProductPivotTableOnly() for synchronized loading
     */
    async initializeProductPivotTable() {
        try {
            // Show loading, hide pivot
            const loadingElement = document.getElementById(
                'product-pivot-loading'
            );
            const pivotElement = document.getElementById(
                'productCampaignPivotTable'
            );

            if (loadingElement) loadingElement.style.display = 'block';
            if (pivotElement) pivotElement.style.display = 'none';

            // ✅ COPY class TiktokGmvMaxProductPivotTable từ factGmvMaxProduct.js
            this.productPivotTable = new TiktokGmvMaxProductPivotTableForGmvMax(
                'productCampaignPivotTable'
            );
            await this.productPivotTable.initial();

            // Hide loading, show pivot
            if (loadingElement) loadingElement.style.display = 'none';
            if (pivotElement) pivotElement.style.display = 'block';
        } catch (error) {
            console.error('❌ Error initializing Product Pivot Table:', error);

            // Hide loading on error
            const loadingElement = document.getElementById(
                'product-pivot-loading'
            );
            if (loadingElement) loadingElement.style.display = 'none';

            throw error;
        }
    }

    /**
     * ✅ Initialize Creative Pivot Table without loading management (for synchronized loading)
     */
    async initializeCreativePivotTableOnly() {
        try {
            // ✅ Initialize Creative Pivot Table
            this.creativePivotTable =
                new TiktokGmvMaxCreativePivotTableForGmvMax(
                    'creativePivotTable'
                );
            await this.creativePivotTable.initial();
        } catch (error) {
            console.error('❌ Error initializing Creative Pivot Table:', error);
            throw error;
        }
    }

    /**
     * ✅ Initialize Live Pivot Table without loading management (for synchronized loading)
     */
    async initializeLivePivotTableOnly() {
        try {
            // ✅ COPY class TiktokGmvMaxCampaignPivotTable từ factGmvMaxCampaign.js
            this.livePivotTable = new TiktokGmvMaxCampaignPivotTableForGmvMax(
                'liveCampaignPivotTable'
            );
            await this.livePivotTable.initial();
        } catch (error) {
            console.error(
                '❌ Error initializing Live Campaign Pivot Table:',
                error
            );
            throw error;
        }
    }

    /**
     * ✅ COPY HOÀN TOÀN: Initialize Live Campaign Pivot Table như factGmvMaxCampaign.js
     * @deprecated Use initializeLivePivotTableOnly() for synchronized loading
     */
    async initializeLivePivotTable() {
        try {
            // Show loading, hide pivot
            const loadingElement =
                document.getElementById('live-pivot-loading');
            const pivotElement = document.getElementById(
                'liveCampaignPivotTable'
            );

            if (loadingElement) loadingElement.style.display = 'block';
            if (pivotElement) pivotElement.style.display = 'none';

            // ✅ COPY class TiktokGmvMaxCampaignPivotTable từ factGmvMaxCampaign.js
            this.livePivotTable = new TiktokGmvMaxCampaignPivotTableForGmvMax(
                'liveCampaignPivotTable'
            );
            await this.livePivotTable.initial();

            // Hide loading, show pivot
            if (loadingElement) loadingElement.style.display = 'none';
            if (pivotElement) pivotElement.style.display = 'block';
        } catch (error) {
            console.error(
                '❌ Error initializing Live Campaign Pivot Table:',
                error
            );

            // Hide loading on error
            const loadingElement =
                document.getElementById('live-pivot-loading');
            if (loadingElement) loadingElement.style.display = 'none';

            throw error;
        }
    }

    /**
     * Show loading state for Product ROI cards
     */
    showProductLoadingState() {
        const productContainer = document.getElementById(
            'product-roi-cards-container'
        );
        if (productContainer) {
            productContainer.innerHTML = `
                <div class="col-12 text-center p-4">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải phân tích ROI Product...</p>
                </div>
            `;
        }
    }

    /**
     * Show loading state for Creative ROI cards
     */
    showCreativeLoadingState() {
        const creativeContainer = document.getElementById(
            'creative-roi-cards-container'
        );
        if (creativeContainer) {
            creativeContainer.innerHTML = `
                <div class="col-12 text-center p-4">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải phân tích ROI Creative...</p>
                </div>
            `;
        }
    }

    /**
     * Hide loading state for Creative ROI cards
     */
    hideCreativeLoadingState() {
        // Loading state will be replaced by actual Creative ROI content
    }

    /**
     * Show loading state for Creative Pivot Table
     */
    showCreativePivotLoadingState() {
        const loadingElement = document.getElementById(
            'creative-pivot-loading'
        );
        const pivotElement = document.getElementById('creativePivotTable');

        if (loadingElement) loadingElement.style.display = 'block';
        if (pivotElement) pivotElement.style.display = 'none';
    }

    /**
     * Hide loading state for Creative Pivot Table
     */
    hideCreativePivotLoadingState() {
        const loadingElement = document.getElementById(
            'creative-pivot-loading'
        );
        const pivotElement = document.getElementById('creativePivotTable');

        if (loadingElement) loadingElement.style.display = 'none';
        if (pivotElement) pivotElement.style.display = 'block';
    }

    /**
     * Show loading state for Live ROI cards
     */
    showLiveLoadingState() {
        const liveContainer = document.getElementById(
            'live-roi-cards-container'
        );
        if (liveContainer) {
            liveContainer.innerHTML = `
                <div class="col-12 text-center p-4">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải phân tích ROI Live...</p>
                </div>
            `;
        }
    }

    /**
     * Hide loading state for Product ROI cards
     */
    hideProductLoadingState() {
        // Loading state will be replaced by actual Product ROI content
    }

    /**
     * Hide loading state for Live ROI cards
     */
    hideLiveLoadingState() {
        // Loading state will be replaced by actual Live ROI content
    }

    /**
     * Show loading state for Product Pivot Table
     */
    showProductPivotLoadingState() {
        const loadingElement = document.getElementById('product-pivot-loading');
        const pivotElement = document.getElementById(
            'productCampaignPivotTable'
        );

        if (loadingElement) loadingElement.style.display = 'block';
        if (pivotElement) pivotElement.style.display = 'none';
    }

    /**
     * Hide loading state for Product Pivot Table
     */
    hideProductPivotLoadingState() {
        const loadingElement = document.getElementById('product-pivot-loading');
        const pivotElement = document.getElementById(
            'productCampaignPivotTable'
        );

        if (loadingElement) loadingElement.style.display = 'none';
        if (pivotElement) pivotElement.style.display = 'block';
    }

    /**
     * Show loading state for Live Pivot Table
     */
    showLivePivotLoadingState() {
        const loadingElement = document.getElementById('live-pivot-loading');
        const pivotElement = document.getElementById('liveCampaignPivotTable');

        if (loadingElement) loadingElement.style.display = 'block';
        if (pivotElement) pivotElement.style.display = 'none';
    }

    /**
     * Hide loading state for Live Pivot Table
     */
    hideLivePivotLoadingState() {
        const loadingElement = document.getElementById('live-pivot-loading');
        const pivotElement = document.getElementById('liveCampaignPivotTable');

        if (loadingElement) loadingElement.style.display = 'none';
        if (pivotElement) pivotElement.style.display = 'block';
    }

    /**
     * Show loading state for both sections (backward compatibility)
     * @deprecated Use showProductLoadingState() and showLiveLoadingState() instead
     */
    showLoadingState() {
        this.showProductLoadingState();
        this.showLiveLoadingState();
    }

    /**
     * Hide loading state for both sections (backward compatibility)
     * @deprecated Use hideProductLoadingState() and hideLiveLoadingState() instead
     */
    hideLoadingState() {
        this.hideProductLoadingState();
        this.hideLiveLoadingState();
    }

    /**
     * Show error message
     */
    showErrorMessage(message) {
        // Prefer toast if available
        try {
            if (typeof abp !== 'undefined' && abp.notify && abp.notify.error) {
                abp.notify.error(message);
                return;
            }
        } catch {}
        try {
            if (typeof toastr !== 'undefined' && toastr.error) {
                toastr.error(message);
                return;
            }
        } catch {}

        // Fallback: render inline into both product and live containers
        const renderInto = (containerId) => {
            const el = document.getElementById(containerId);
            if (el) {
                el.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${message}
                        </div>
                    </div>
                `;
            }
        };
        renderInto('product-roi-cards-container');
        renderInto('live-roi-cards-container');
    }

    /**
     * Show warning notification with fallbacks (ABP → Toastr → alert)
     */
    showWarning(message) {
        try {
            if (typeof abp !== 'undefined' && abp.notify && abp.notify.warn) {
                abp.notify.warn(message);
                return;
            }
        } catch {}
        try {
            if (typeof toastr !== 'undefined' && toastr.warning) {
                toastr.warning(message);
                return;
            }
        } catch {}
        alert(message);
    }

    /**
     * Handle window resize
     */
    handleResize() {
        if (this.productPivotTable && this.productPivotTable.pivotTableObj) {
            this.productPivotTable.pivotTableObj.refresh();
        }
        if (this.livePivotTable && this.livePivotTable.pivotTableObj) {
            this.livePivotTable.pivotTableObj.refresh();
        }
    }

    /**
     * ✅ Product section action methods
     */
    toggleProductChart() {
        if (this.productPivotTable && this.productPivotTable.pivotTableObj) {
            const currentView =
                this.productPivotTable.pivotTableObj.displayOption?.view;
            if (currentView === 'Chart') {
                this.productPivotTable.showGrid();
            } else {
                this.productPivotTable.showChart();
            }
        }
    }

    exportProductToExcel() {
        if (!this.productPivotTable || !this.productPivotTable.pivotTableObj) {
            this.showErrorMessage(
                'Bảng phân tích Product chưa được tải. Vui lòng thử lại sau.'
            );
            return;
        }

        const ds =
            this.productPivotTable.pivotTableObj?.dataSourceSettings
                ?.dataSource;
        if (!Array.isArray(ds) || ds.length === 0) {
            this.showWarning(
                'Không có dữ liệu để xuất Excel (Product). Vui lòng điều chỉnh bộ lọc.'
            );
            return;
        }

        this.productPivotTable.exportToExcel('GMV_Max_Product_Analysis');
    }

    exportProductToPdf() {
        this.showWarning(
            'Chức năng xuất PDF đang được phát triển. Vui lòng sử dụng xuất Excel.'
        );
    }

    /**
     * ✅ Creative section action methods
     */
    toggleCreativeChart() {
        if (this.creativePivotTable && this.creativePivotTable.pivotTableObj) {
            const currentView =
                this.creativePivotTable.pivotTableObj.displayOption?.view;
            if (currentView === 'Chart') {
                this.creativePivotTable.showGrid();
            } else {
                this.creativePivotTable.showChart();
            }
        }
    }

    exportCreativeToExcel() {
        if (
            !this.creativePivotTable ||
            !this.creativePivotTable.pivotTableObj
        ) {
            this.showErrorMessage(
                'Bảng phân tích Creative chưa được tải. Vui lòng thử lại sau.'
            );
            return;
        }

        const ds =
            this.creativePivotTable.pivotTableObj?.dataSourceSettings
                ?.dataSource;
        if (!Array.isArray(ds) || ds.length === 0) {
            this.showWarning(
                'Không có dữ liệu để xuất Excel (Creative). Vui lòng điều chỉnh bộ lọc.'
            );
            return;
        }

        this.creativePivotTable.exportToExcel('GMV_Max_Creative_Analysis');
    }

    exportCreativeToPdf() {
        this.showWarning(
            'Chức năng xuất PDF đang được phát triển. Vui lòng sử dụng xuất Excel.'
        );
    }

    /**
     * ✅ Live section action methods
     */
    toggleLiveChart() {
        if (this.livePivotTable && this.livePivotTable.pivotTableObj) {
            const currentView =
                this.livePivotTable.pivotTableObj.displayOption?.view;
            if (currentView === 'Chart') {
                this.livePivotTable.showGrid();
            } else {
                this.livePivotTable.showChart();
            }
        }
    }

    exportLiveToExcel() {
        if (!this.livePivotTable || !this.livePivotTable.pivotTableObj) {
            this.showErrorMessage(
                'Bảng phân tích Live chưa được tải. Vui lòng thử lại sau.'
            );
            return;
        }

        const ds =
            this.livePivotTable.pivotTableObj?.dataSourceSettings?.dataSource;
        if (!Array.isArray(ds) || ds.length === 0) {
            this.showWarning(
                'Không có dữ liệu để xuất Excel (Live). Vui lòng điều chỉnh bộ lọc.'
            );
            return;
        }

        this.livePivotTable.exportToExcel('GMV_Max_Live_Campaign_Analysis');
    }

    exportLiveToPdf() {
        this.showWarning(
            'Chức năng xuất PDF đang được phát triển. Vui lòng sử dụng xuất Excel.'
        );
    }

    /**
     * Destroy and cleanup
     */
    destroy() {
        if (this.productPivotTable && this.productPivotTable.pivotTableObj) {
            this.productPivotTable.pivotTableObj.destroy();
        }
        if (this.creativePivotTable && this.creativePivotTable.pivotTableObj) {
            this.creativePivotTable.pivotTableObj.destroy();
        }
        if (this.livePivotTable && this.livePivotTable.pivotTableObj) {
            this.livePivotTable.pivotTableObj.destroy();
        }
        if (this.roiCards) {
            this.roiCards.destroy();
        }
    }
}

// ✅ Make campaignTabManager globally accessible for ROI card modal details
window.CampaignTabManager = CampaignTabManager;
