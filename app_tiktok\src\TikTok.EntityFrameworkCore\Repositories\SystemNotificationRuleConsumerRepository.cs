using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Domain.Repositories;
using TikTok.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    public class SystemNotificationRuleConsumerRepository : EfCoreRepository<TikTokDbContext, SystemNotificationRuleConsumerEntity, Guid>, ISystemNotificationRuleConsumerRepository
    {
        public SystemNotificationRuleConsumerRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<List<SystemNotificationRuleConsumerEntity>> GetConsumersByRuleIdAsync(Guid ruleId)
        {
            return await (await GetQueryableAsync())
                .Where(x => x.SystemNotificationRuleId == ruleId)
                .ToListAsync();
        }

        public async Task<List<SystemNotificationRuleConsumerEntity>> GetRulesByConsumerAsync(Guid? bcId, Guid? adAccountId)
        {
            var query = (await GetQueryableAsync()).AsQueryable();

            if (bcId.HasValue && adAccountId.HasValue)
            {
                // Tìm rules áp dụng cho specific AdAccount
                query = query.Where(x => 
                    (x.BcId == bcId && x.AdAccountId == adAccountId) ||
                    (x.BcId == bcId && x.AdAccountId == null) ||
                    (x.BcId == null && x.AdAccountId == null)
                );
            }
            else if (bcId.HasValue)
            {
                // Tìm rules áp dụng cho Business Center
                query = query.Where(x => 
                    (x.BcId == bcId && x.AdAccountId == null) ||
                    (x.BcId == null && x.AdAccountId == null)
                );
            }
            else
            {
                // Tìm rules áp dụng cho tất cả
                query = query.Where(x => x.BcId == null && x.AdAccountId == null);
            }

            return await query.ToListAsync();
        }

        public async Task DeleteConsumersByRuleIdAsync(Guid ruleId)
        {
            var consumers = await GetConsumersByRuleIdAsync(ruleId);
            await DeleteManyAsync(consumers);
        }

        public async Task<bool> IsConsumerAssignedAsync(Guid ruleId, Guid? bcId, Guid? adAccountId)
        {
            return await (await GetQueryableAsync())
                .AnyAsync(x => x.SystemNotificationRuleId == ruleId && 
                              x.BcId == bcId && 
                              x.AdAccountId == adAccountId);
        }
    }
}
