/* Entity Change Warning Modal Styles - Compact Version */
#entityChangeWarningModal .modal-dialog {
    max-width: 400px; /* Smaller modal width */
    margin: 1.75rem auto;
}

#entityChangeWarningModal .modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

#entityChangeWarningModal .modal-header {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    border-bottom: none;
    padding: 1rem 1.25rem; /* Smaller padding */
}

#entityChangeWarningModal .modal-header .modal-title {
    font-size: 1.1rem; /* Smaller title */
    margin: 0;
}

#entityChangeWarningModal .modal-header .btn-close {
    filter: invert(1);
    padding: 0.5rem;
}

#entityChangeWarningModal .modal-body {
    padding: 1.5rem 1.25rem; /* Smaller padding */
}

#entityChangeWarningModal .modal-body .fa-exclamation-circle {
    font-size: 2rem !important; /* Smaller icon */
    margin-bottom: 0.75rem;
}

#entityChangeWarningModal .modal-body p {
    font-size: 0.95rem; /* Smaller text */
    margin-bottom: 1rem;
}

#entityChangeWarningModal .bg-light-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-left: 3px solid #ffc107;
    padding: 0.75rem; /* Smaller padding */
}

#entityChangeWarningModal .bg-light-warning small {
    font-size: 0.85rem; /* Smaller detail text */
}

#entityChangeWarningModal .modal-footer {
    padding: 1rem 1.25rem; /* Smaller padding */
}

#entityChangeWarningModal .modal-footer .btn {
    border-radius: 6px;
    font-weight: 600;
    padding: 0.5rem 1rem; /* Smaller button padding */
    font-size: 0.9rem; /* Smaller button text */
}

#entityChangeWarningModal .btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    border: none;
    color: #000;
}

#entityChangeWarningModal .btn-warning:hover {
    background: linear-gradient(135deg, #ff8f00 0%, #ff6f00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

#entityChangeWarningModal .btn-outline-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.2);
}

/* Animation for modal */
#entityChangeWarningModal .modal-dialog {
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Icon animation - gentler for compact modal */
#entityChangeWarningModal .fa-exclamation-circle {
    animation: gentlePulse 2.5s infinite;
}

@keyframes gentlePulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive adjustments for mobile */
@media (max-width: 576px) {
    #entityChangeWarningModal .modal-dialog {
        max-width: 90%;
        margin: 1rem auto;
    }

    #entityChangeWarningModal .modal-body {
        padding: 1rem;
    }

    #entityChangeWarningModal .modal-footer {
        padding: 0.75rem 1rem;
    }

    #entityChangeWarningModal .modal-footer .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}

/* ===== FORM VALIDATION STYLES ===== */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* ===== MODAL SCROLL STYLES FOR AD ACCOUNT SELECTION ===== */
#selectSystemNotificationAdAccountsModal .modal-dialog {
    max-height: 100vh;
    margin: 1.75rem auto;
}

#selectSystemNotificationAdAccountsModal .modal-content {
    max-height: 100vh;
    display: flex;
    flex-direction: column;
}

#selectSystemNotificationAdAccountsModal .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

#selectSystemNotificationAdAccountsModal .modal-footer {
    flex-shrink: 0;
}

/* ===== CUSTOM SCROLLBAR FOR LIST GROUPS ===== */
#systemNotificationAdAccountsSearchList::-webkit-scrollbar,
#systemNotificationSelectedAdAccountsList::-webkit-scrollbar {
    width: 8px;
}

#systemNotificationAdAccountsSearchList::-webkit-scrollbar-track,
#systemNotificationSelectedAdAccountsList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#systemNotificationAdAccountsSearchList::-webkit-scrollbar-thumb,
#systemNotificationSelectedAdAccountsList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#systemNotificationAdAccountsSearchList::-webkit-scrollbar-thumb:hover,
#systemNotificationSelectedAdAccountsList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}