using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.FactGmvMaxProductCreatives.Dtos
{
    /// <summary>
    /// DTO cho FactGmvMaxProductCreativeEntity - tương tự FactGmvMaxProductDto nhưng cho Creative level
    /// </summary>
    public class FactGmvMaxProductCreativeDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID chiến dịch từ TikTok API
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// ID store từ TikTok API
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// Business Key - Business Center ID từ TikTok
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Business Key - Advertiser ID từ TikTok
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ✅ NEW: Product Group ID từ TikTok API (thay vì ProductId)
        /// </summary>
        public string ItemGroupId { get; set; }

        /// <summary>
        /// ✅ NEW: TikTok Post ID từ TikTok API (Creative ID)
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// ✅ NEW: Creative/Video name từ TikTok API
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// ✅ NEW: Trạng thái delivery của creative
        /// </summary>
        public string? CreativeDeliveryStatus { get; set; }

        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// URL hình ảnh sản phẩm
        /// </summary>
        public string? ProductImageUrl { get; set; }

        /// <summary>
        /// Trạng thái sản phẩm (available, unavailable)
        /// </summary>
        public string? ProductStatus { get; set; }

        /// <summary>
        /// Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)
        /// </summary>
        public string? CreativeType { get; set; }

        /// <summary>
        /// Tên TikTok account
        /// </summary>
        public string? TtAccountName { get; set; }

        /// <summary>
        /// URL hình đại diện TikTok account
        /// </summary>
        public string? TtAccountProfileImageUrl { get; set; }

        /// <summary>
        /// Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)
        /// </summary>
        public string? TtAccountAuthorizationType { get; set; }

        /// <summary>
        /// Loại nội dung shop (VIDEO, PRODUCT_CARD)
        /// </summary>
        public string? ShopContentType { get; set; }

        // ========== Core Metrics ==========

        /// <summary>
        /// Số đơn hàng
        /// </summary>
        public int Orders { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (CPO)
        /// </summary>
        public decimal? CostPerOrder { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (CPO) (VND)
        /// </summary>
        public decimal? CostPerOrderVND { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (CPO) (USD)
        /// </summary>
        public decimal? CostPerOrderUSD { get; set; }

        /// <summary>
        /// Tổng chi phí quảng cáo
        /// </summary>
        public decimal Cost { get; set; }

        /// <summary>
        /// Tổng doanh thu (Gross Revenue)
        /// </summary>
        public decimal GrossRevenue { get; set; }

        /// <summary>
        /// Tổng doanh thu (Gross Revenue) (VND)
        /// </summary>
        public decimal? GrossRevenueVND { get; set; }

        /// <summary>
        /// Tổng doanh thu (Gross Revenue) (USD)
        /// </summary>
        public decimal? GrossRevenueUSD { get; set; }

        /// <summary>
        /// Tổng lượt hiển thị sản phẩm (organic + paid)
        /// </summary>
        public long? ProductImpressions { get; set; }

        /// <summary>
        /// Tổng lượt click sản phẩm (organic + paid)
        /// </summary>
        public long? ProductClicks { get; set; }

        /// <summary>
        /// Tỷ lệ click sản phẩm (ProductClicks/ProductImpressions)
        /// </summary>
        public decimal? ProductClickRate { get; set; }

        /// <summary>
        /// Tỷ lệ click-through của paid views từ video này
        /// </summary>
        public decimal? AdClickRate { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi của paid clicks từ video này
        /// </summary>
        public decimal? AdConversionRate { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 2 giây
        /// </summary>
        public decimal? AdVideoViewRate2s { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 6 giây
        /// </summary>
        public decimal? AdVideoViewRate6s { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 25% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP25 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 50% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP50 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 75% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP75 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video 100% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP100 { get; set; }

        /// <summary>
        /// ROAS (Return on Ad Spend) - Hiệu quả quảng cáo
        /// </summary>
        public decimal? ROAS { get; set; }

        /// <summary>
        /// TACOS (True ACOS) - Hiệu quả quảng cáo so với tổng doanh thu
        /// </summary>
        public decimal? TACOS { get; set; }

        // ========== Metadata ==========

        /// <summary>
        /// Tiền tệ theo mã ISO 4217
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Ngày báo cáo UTC format yyyy-MM-dd 00:00:00 (theo ngày)
        /// </summary>
        public DateTime Date { get; set; }

        // ========== Foreign Keys ==========

        /// <summary>
        /// Khóa ngoại liên kết với Dim_Date (YYYYMMDD)
        /// </summary>
        public int DimDateId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_AdAccount
        /// </summary>
        public Guid DimAdAccountId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_BusinessCenter
        /// </summary>
        public Guid DimBusinessCenterId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_Campaign
        /// </summary>
        public Guid DimCampaignId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_Store
        /// </summary>
        public Guid DimStoreId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_Product
        /// </summary>
        public Guid DimProductId { get; set; }

        /// <summary>
        /// ✅ NEW: Khóa ngoại liên kết với Dim_TTAccount
        /// </summary>
        public Guid? DimTTAccountId { get; set; }
    }
}

