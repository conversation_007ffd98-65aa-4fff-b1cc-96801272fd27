$(function () {
    var l = abp.localization.getResource('TikTok');
    var dataTable = $('#JobTypeConfigurationTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: false, // Change to false since we're not using server-side processing
            paging: true,
            order: [[3, 'asc']], // Sort by priority
            searching: false,
            scrollX: true,
            ajax: {
                url: abp.appPath + 'api/job-type-configurations',
                type: 'GET',
                dataSrc: function (json) {
                    return json || [];
                },
            },
            columnDefs: [
                {
                    title: l('CommandType'),
                    data: 'commandType',
                    render: function (data) {
                        return getCommandTypeDisplayName(data);
                    },
                },
                {
                    title: l('DisplayName'),
                    data: 'displayName',
                },
                {
                    title: l('IntervalSeconds'),
                    data: 'intervalSeconds',
                    render: function (data) {
                        return formatIntervalSeconds(data);
                    },
                },
                {
                    title: l('Priority'),
                    data: 'priority',
                },
                {
                    title: l('TimeoutMinutes'),
                    data: 'timeoutMinutes',
                    render: function (data) {
                        return data || 'Sử dụng chung';
                    },
                },
                {
                    title: l('IsActive'),
                    data: 'isActive',
                    render: function (data) {
                        return data
                            ? '<span class="badge bg-success">Hoạt động</span>'
                            : '<span class="badge bg-secondary">Không hoạt động</span>';
                    },
                },
                {
                    title: l('Actions'),
                    rowAction: {
                        items: [
                            {
                                text: l('Edit'),
                                action: function (data) {
                                    editModal.open({ id: data.record.id });
                                },
                            },
                            {
                                text: l('Delete'),
                                confirmMessage: function (data) {
                                    return l(
                                        'JobTypeConfigurationDeletionConfirmationMessage',
                                        data.record.displayName
                                    );
                                },
                                action: function (data) {
                                    abp.ajax({
                                        url:
                                            abp.appPath +
                                            'api/job-type-configurations/' +
                                            data.record.id,
                                        type: 'DELETE',
                                        success: function () {
                                            abp.notify.info(
                                                l('SuccessfullyDeleted')
                                            );
                                            dataTable.ajax.reload();
                                        },
                                        error: function (error) {
                                            abp.notify.error(
                                                'Failed to delete configuration'
                                            );
                                        },
                                    });
                                },
                            },
                        ],
                    },
                },
            ],
        })
    );

    var createModal = new abp.ModalManager(
        abp.appPath + 'JobTypeConfiguration/CreateModal'
    );
    var editModal = new abp.ModalManager(
        abp.appPath + 'JobTypeConfiguration/EditModal'
    );

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewJobTypeConfigurationButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });

    function getCommandTypeDisplayName(commandType) {
        const commandTypes = {
            1: 'SyncBusinessCenter',
            2: 'SyncTransaction',
            3: 'SyncBalance',
            4: 'SyncAsset',
            5: 'SyncCampaign',
            6: 'SyncReportIntegratedAdAccount',
            7: 'SyncReportIntegratedCampaign',
            8: 'SyncReportIntegratedAdGroup',
            9: 'SyncLatestBalance',
            10: 'SyncDetailedAdAccount',
            11: 'SyncGmvMax',
            12: 'SyncGmvMaxIdentities'
        };
        return commandTypes[commandType] || commandType;
    }

    function formatIntervalSeconds(seconds) {
        if (seconds < 60) {
            return seconds + ' giây';
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            return minutes + ' phút';
        } else if (seconds < 86400) {
            const hours = Math.floor(seconds / 3600);
            return hours + ' giờ';
        } else {
            const days = Math.floor(seconds / 86400);
            return days + ' ngày';
        }
    }
});
