using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho báo cáo chi tiết cấp creative của Product GMV Max Campaign. 
    /// Lưu trữ thông tin chi tiết về hiệu suất từng creative (video/product card) trong chiến dịch Product GMV Max, 
    /// bao gồm thông tin creative, TikTok account, loại authorization và các metrics hiệu suất chi tiết.
    /// </summary>
    public class RawGmvMaxProductCreativeReportEntity : AuditedEntity<Guid>
    {
        public RawGmvMaxProductCreativeReportEntity()
        {

        }

        public RawGmvMaxProductCreativeReportEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// ID Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID TikTok Shop
        /// </summary>
        [Required]
        [StringLength(100)]
        public string StoreId { get; set; }

        /// <summary>
        /// ID chiến dịch Product GMV Max
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// ID sản phẩm SPU
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemGroupId { get; set; }

        /// <summary>
        /// ID TikTok post (có thể là -1 nếu là Product Card)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemId { get; set; }

        /// <summary>
        /// Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)
        /// </summary>
        [Required]
        public CreativeType CreativeType { get; set; }

        /// <summary>
        /// Tên creative
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Tên TikTok account (có thể là 0 hoặc -1 nếu thiếu quyền)
        /// </summary>
        [StringLength(200)]
        public string? TtAccountName { get; set; }

        /// <summary>
        /// URL hình đại diện TikTok account
        /// </summary>
        [StringLength(1000)]
        public string? TtAccountProfileImageUrl { get; set; }

        /// <summary>
        /// Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)
        /// </summary>
        public TtAccountAuthorizationType? TtAccountAuthorizationType { get; set; }

        /// <summary>
        /// Loại nội dung shop (VIDEO, PRODUCT_CARD)
        /// </summary>
        public ShopContentType? ShopContentType { get; set; }

        /// <summary>
        /// Số lượng đơn hàng SKU từ creative này
        /// </summary>
        public int? Orders { get; set; }

        /// <summary>
        /// Tổng doanh thu gộp từ creative này
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal? GrossRevenue { get; set; }

        /// <summary>
        /// Tổng lượt hiển thị sản phẩm (organic + paid)
        /// </summary>
        public long? ProductImpressions { get; set; }

        /// <summary>
        /// Tổng lượt click sản phẩm (organic + paid)
        /// </summary>
        public long? ProductClicks { get; set; }

        /// <summary>
        /// Tỷ lệ click sản phẩm (ProductClicks/ProductImpressions)
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? ProductClickRate { get; set; }

        /// <summary>
        /// Tỷ lệ click-through của paid views từ video này
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdClickRate { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi của paid clicks từ video này
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdConversionRate { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 2 giây
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdVideoViewRate2s { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 6 giây
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdVideoViewRate6s { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 25% thời lượng
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdVideoViewRateP25 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 50% thời lượng
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdVideoViewRateP50 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 75% thời lượng
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdVideoViewRateP75 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video 100% thời lượng
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? AdVideoViewRateP100 { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:00:00 (theo giờ)
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Tỷ suất lợi nhuận (Return on Investment)
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? ROI { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal? CostPerOrder { get; set; }

        [Column(TypeName = "decimal(15,2)")]
        public decimal Cost { get; set; }
        public CreativeDeliveryStatus CreativeDeliveryStatus { get; set; }
        public int SequenceNumber { get; set; } = 0;
        /// <summary>
        /// Cập nhật dữ liệu từ dữ liệu mới
        /// </summary>
        /// <param name="newEntity">Dữ liệu mới</param>
        public void UpdateFromNewData(RawGmvMaxProductCreativeReportEntity newEntity)
        {
            BcId = newEntity.BcId;
            AdvertiserId = newEntity.AdvertiserId;
            StoreId = newEntity.StoreId;
            CampaignId = newEntity.CampaignId;
            ItemGroupId = newEntity.ItemGroupId;
            ItemId = newEntity.ItemId;
            CreativeType = newEntity.CreativeType;
            Title = newEntity.Title;
            TtAccountName = newEntity.TtAccountName;
            TtAccountProfileImageUrl = newEntity.TtAccountProfileImageUrl;
            TtAccountAuthorizationType = newEntity.TtAccountAuthorizationType;
            ShopContentType = newEntity.ShopContentType;
            Cost = newEntity.Cost;
            CreativeDeliveryStatus = newEntity.CreativeDeliveryStatus;
            Orders = newEntity.Orders;
            GrossRevenue = newEntity.GrossRevenue;
            ProductImpressions = newEntity.ProductImpressions;
            ProductClicks = newEntity.ProductClicks;
            ProductClickRate = newEntity.ProductClickRate;
            AdClickRate = newEntity.AdClickRate;
            AdConversionRate = newEntity.AdConversionRate;
            AdVideoViewRate2s = newEntity.AdVideoViewRate2s;
            AdVideoViewRate6s = newEntity.AdVideoViewRate6s;
            AdVideoViewRateP25 = newEntity.AdVideoViewRateP25;
            AdVideoViewRateP50 = newEntity.AdVideoViewRateP50;
            AdVideoViewRateP75 = newEntity.AdVideoViewRateP75;
            AdVideoViewRateP100 = newEntity.AdVideoViewRateP100;
            Currency = newEntity.Currency;
            Date = newEntity.Date;
            ROI = newEntity.ROI;
            CostPerOrder = newEntity.CostPerOrder;
        }

        /// <summary>
        /// Kiểm tra xem tất cả metrics có phải là 0 không
        /// </summary>
        /// <returns>True nếu tất cả metrics đều là 0, False nếu có ít nhất 1 metric khác 0</returns>
        public bool AreAllMetricsZero()
        {
            return (Orders ?? 0) == 0 &&
                   (GrossRevenue ?? 0) == 0 &&
                   (ProductImpressions ?? 0) == 0 &&
                   (ProductClicks ?? 0) == 0 &&
                   (ProductClickRate ?? 0) == 0 &&
                   (AdClickRate ?? 0) == 0 &&
                   (AdConversionRate ?? 0) == 0 &&
                   (AdVideoViewRate2s ?? 0) == 0 &&
                   (AdVideoViewRate6s ?? 0) == 0 &&
                   (AdVideoViewRateP25 ?? 0) == 0 &&
                   (AdVideoViewRateP50 ?? 0) == 0 &&
                   (AdVideoViewRateP75 ?? 0) == 0 &&
                   (AdVideoViewRateP100 ?? 0) == 0 &&
                   (ROI ?? 0) == 0 &&
                   (CostPerOrder ?? 0) == 0 &&
                   Cost == 0;
        }

        /// <summary>
        /// Kiểm tra xem dữ liệu có khác với dữ liệu hiện tại không
        /// </summary>
        /// <param name="newEntity">Dữ liệu mới</param>
        /// <param name="ignoreDate">Bỏ qua kiểm tra ngày: nếu true thì sẽ không kiểm tra ngày</param>
        /// <returns>True nếu dữ liệu khác, False nếu dữ liệu giống</returns>
        public bool IsDifferentFrom(RawGmvMaxProductCreativeReportEntity newEntity, bool ignoreDate = false)
        {
            return BcId != newEntity.BcId ||
                AdvertiserId != newEntity.AdvertiserId ||
                StoreId != newEntity.StoreId ||
                CampaignId != newEntity.CampaignId ||
                ItemGroupId != newEntity.ItemGroupId ||
                ItemId != newEntity.ItemId ||
                CreativeType != newEntity.CreativeType ||
                Title != newEntity.Title ||
                TtAccountName != newEntity.TtAccountName ||
                TtAccountProfileImageUrl != newEntity.TtAccountProfileImageUrl ||
                TtAccountAuthorizationType != newEntity.TtAccountAuthorizationType ||
                ShopContentType != newEntity.ShopContentType ||
                Cost != newEntity.Cost ||
                CreativeDeliveryStatus != newEntity.CreativeDeliveryStatus ||
                Orders != newEntity.Orders ||
                GrossRevenue != newEntity.GrossRevenue ||
                ProductImpressions != newEntity.ProductImpressions ||
                ProductClicks != newEntity.ProductClicks ||
                ProductClickRate != newEntity.ProductClickRate ||
                AdClickRate != newEntity.AdClickRate ||
                AdConversionRate != newEntity.AdConversionRate ||
                AdVideoViewRate2s != newEntity.AdVideoViewRate2s ||
                AdVideoViewRate6s != newEntity.AdVideoViewRate6s ||
                AdVideoViewRateP25 != newEntity.AdVideoViewRateP25 ||
                AdVideoViewRateP50 != newEntity.AdVideoViewRateP50 ||
                AdVideoViewRateP75 != newEntity.AdVideoViewRateP75 ||
                AdVideoViewRateP100 != newEntity.AdVideoViewRateP100 ||
                Currency != newEntity.Currency ||
                ROI != newEntity.ROI ||
                CostPerOrder != newEntity.CostPerOrder ||
                (ignoreDate ? false : Date != newEntity.Date);
        }
        public bool StatusChanged(RawGmvMaxProductCreativeReportEntity newEntity, bool ignoreDate = false)
        {
            return CreativeDeliveryStatus != newEntity.CreativeDeliveryStatus ||
                (ignoreDate ? false : Date != newEntity.Date);
        }
    }
}