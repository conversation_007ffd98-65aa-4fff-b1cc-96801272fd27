/**
 * Table Management Component for System Notification Rules
 * Handles table rendering, filtering, and data loading
 */

const TableManager = {
    tableEl: null,
    filterEntityType: null,
    filterRuleName: null,
    filterIsDefault: null,
    clearFiltersBtn: null,
    rulesTable: null,

    // Initialize table manager
    init() {
        this.tableEl = document.getElementById('SystemNotificationRulesTable');
        this.filterEntityType = document.getElementById('filterEntityType');
        this.filterRuleName = document.getElementById('filterRuleName');
        this.filterIsDefault = document.getElementById('filterIsDefault');
        this.clearFiltersBtn = document.getElementById('clearFilters');

        if (this.tableEl) {
            this.initEntityTypeFilters();
            this.setupEventListeners();
            this.loadRules();
        }
    },

    // Initialize entity type filters
    initEntityTypeFilters() {
        if (!this.filterEntityType) return;

        // Use centralized EntityTypesManager for entity types
        if (window.EntityTypesManager) {
            this.filterEntityType.innerHTML = window.EntityTypesManager.generateDropdownOptions({
                includeAll: true,
                activeOnly: true,
                allText: 'all'
            });
        } else {
            // Fallback to hardcoded options for backward compatibility
            this.filterEntityType.innerHTML =
                '<option value="">' +
                getLocalizedText('all') +
                '</option>' +
                '<option value="RawGmvMaxProductCreativeReportEntity">' +
                getLocalizedText('gmvMaxProductCreative') +
                '</option>' +
                '<option value="RawGmvMaxProductCampaignReportEntity">' +
                getLocalizedText('gmvMaxProductCampaign') +
                '</option>' +
                '<option value="RawGmvMaxLiveCampaignReportEntity">' +
                getLocalizedText('gmvMaxLiveCampaign') +
                '</option>';
        }
    },

    // Load rules from API
    loadRules() {
        const params = new URLSearchParams();
        if (this.filterRuleName?.value)
            params.append('searchText', this.filterRuleName.value);
        if (this.filterEntityType?.value)
            params.append('entityType', this.filterEntityType.value);
        if (this.filterIsDefault?.value)
            params.append('isDefault', this.filterIsDefault.value);

        const url = `${window.snr.api.list}?${params.toString()}`;
        window.snr
            .http(url)
            .then((data) => {
                this.renderTable(data?.items || []);
            })
            .catch(console.error);
    },

    // Render table with data
    renderTable(items) {
        if (!this.tableEl) return;

        const tbodyId = 'snr-table-body';
        let tbody = this.tableEl.querySelector('tbody');
        if (!tbody) {
            tbody = document.createElement('tbody');
            tbody.id = tbodyId;
            this.tableEl.appendChild(tbody);
        }

        tbody.innerHTML = items
            .map(
                (it) => `
            <tr>
                <td>${escapeHtml(it.ruleName)}</td>
                <td>${getEntityTypeDisplayName(
                    it.entityTypeDisplay || it.entityType
                )}</td>
                <!-- Removed legacy targetEntity column -->
                <td>${it.conditionCount ?? (it.conditionsJson ? 1 : 0)}</td>
                <!-- Removed legacy notificationFrequency column -->
                <td>${
                    it.isDefault
                        ? '<span class="badge bg-primary">' +
                          getLocalizedText('yes') +
                          '</span>'
                        : '<span class="badge bg-secondary">' +
                          getLocalizedText('no') +
                          '</span>'
                }</td>
                <td>${escapeHtml(it.creatorName || 'Hệ thống')}</td>
                <td>${formatDate(it.creationTime)}</td>
                <td>${this.renderActions(it)}</td>
            </tr>
        `
            )
            .join('');
    },

    // Render action buttons for each row
    renderActions(it) {
        const editBtn = window.systemNotificationRulePermission?.canEdit
            ? `<button class="btn btn-sm btn-outline-primary me-1" data-action="edit" data-id="${
                  it.id
              }" title="${getLocalizedText(
                  'editRule'
              )}"><i class="fas fa-edit"></i></button>`
            : '';
        const copyBtn = window.systemNotificationRulePermission?.canCreate
            ? `<button class="btn btn-sm btn-outline-secondary me-1" data-action="copy" data-id="${
                  it.id
              }" title="${getLocalizedText(
                  'copyRule'
              )}"><i class="fas fa-copy"></i></button>`
            : '';
        const consumersBtn = window.systemNotificationRulePermission
            ?.canManageRule
            ? `<button class="btn btn-sm btn-outline-info me-1" data-action="consumers" data-id="${
                  it.id
              }" data-name="${escapeHtml(
                  it.ruleName
              )}" title="${getLocalizedText(
                  'manageConsumers'
              )}"><i class="fas fa-users"></i></button>`
            : '';
        const delBtn = window.systemNotificationRulePermission?.canDelete
            ? `<button class="btn btn-sm btn-outline-danger" data-action="delete" data-id="${
                  it.id
              }" title="${getLocalizedText(
                  'deleteRule'
              )}"><i class="fas fa-trash"></i></button>`
            : '';
        return `${editBtn}${copyBtn}${consumersBtn}${delBtn}`;
    },

    // Setup event listeners
    setupEventListeners() {
        // Clear filters button
        if (this.clearFiltersBtn) {
            this.clearFiltersBtn.addEventListener('click', () => {
                this.filterRuleName.value = '';
                this.filterEntityType.value = '';
                this.filterIsDefault.value = '';
                this.loadRules();
            });
        }

        // Filter change events
        [
            this.filterRuleName,
            this.filterEntityType,
            this.filterIsDefault,
        ].forEach((el) => {
            if (el) {
                el.addEventListener('change', () => this.loadRules());
            }
        });

        // Table click events
        if (this.tableEl) {
            this.tableEl.addEventListener('click', (e) => {
                const btn = e.target.closest('button[data-action]');
                if (!btn) return;

                const id = btn.getAttribute('data-id');
                const act = btn.getAttribute('data-action');

                // Delegate to appropriate handlers
                if (act === 'edit') {
                    window.ModalManager?.openEditModal(id);
                } else if (act === 'copy') {
                    window.ModalManager?.copyRule(id);
                } else if (act === 'delete') {
                    window.ModalManager?.deleteRule(id);
                } else if (act === 'consumers') {
                    const ruleName = btn.getAttribute('data-name');
                    window.ConsumersManager?.openConsumersModal(id, ruleName);
                }
            });
        }
    },

    // Refresh table data
    refresh() {
        this.loadRules();
    },
};

// Export to global scope
window.TableManager = TableManager;
