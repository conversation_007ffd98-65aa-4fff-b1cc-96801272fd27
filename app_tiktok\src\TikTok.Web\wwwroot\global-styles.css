/* Your Global Styles */

:root .lpx-brand-logo {
    --lpx-logo: url('/images/logo/leptonx/logo-light.svg');
    --lpx-logo-icon: url('/images/logo/leptonx/logo-light-thumbnail.png');
}

/* Customine Syncfusion */

.e-pivotview .e-grouping-bar .e-pivot-button .e-content,
.e-pivotview .e-group-rows .e-pivot-button .e-content {
    color: #6c757d !important;
}

.lpx-brand-logo {
    background-size: auto 3rem;
    height: 3rem;
}

.lpx-logo-container {
    padding: 10px;
}

/* ===== CUSTOM TOPBAR STYLES ===== */

/* Ensure Font Awesome is loaded */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Notification Bell Styles */
.notification-bell-container {
    position: relative;
    overflow: visible;
    z-index: 1000;
}

.notification-bell-btn {
    border: none;
    background: transparent;
    color: #6c757d;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 4px;
}

.notification-bell-btn:hover {
    background: rgba(108, 117, 125, 0.1);
    color: #495057;
    transform: translateY(-1px);
}

.notification-icon {
    font-size: 1.2rem;
    color: inherit;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    width: auto;
    height: auto;
    min-height: 28px;
    min-width: 28px;
    font-size: 0.75rem;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    z-index: 10;
    animation: pulse 2s infinite;
    transform: translate(0%, 0%);
    line-height: 1;
    padding: 0;
    text-align: center;
}

/* Hidden by default; shown via .show for JS control */
.notification-badge {
    display: none;
}
.notification-badge.show {
    display: flex;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.notification-dropdown {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    padding: 0;
    margin-top: 8px;
    min-width: 350px;
    max-height: 420px;
    overflow-y: auto;
}

.notification-dropdown .dropdown-header {
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.notification-item {
    transition: all 0.2s ease;
    cursor: pointer;
    border-radius: 0;
}

/* Footer actions spacing for bell dropdown */
.notification-footer-actions {
    padding-bottom: 10px;
}
.notification-footer-actions .btn {
    margin-bottom: 4px;
}

.notification-item.unread {
    background: linear-gradient(
        90deg,
        rgba(13, 110, 253, 0.06) 0%,
        rgba(255, 255, 255, 1) 100%
    );
    border-left: 3px solid #0d6efd;
}

.notification-item:hover {
    background: #f8f9fa !important;
}

.notification-item:last-child {
    border-bottom: none !important;
}

.notification-dot {
    margin-top: 8px;
    margin-left: 8px;
    width: 8px;
    height: 8px;
    box-shadow: 0 0 0 2px #fff;
}

/* Icon color helpers matching design accents */
.text-gmv-primary {
    color: #0d6efd !important;
}
.text-gmv-warning {
    color: #ffb020 !important;
}
.text-gmv-success {
    color: #22c55e !important;
}
.text-gmv-danger {
    color: #ef4444 !important;
}

/* Currency Dropdown Styles */
.currency-dropdown-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.currency-dropdown .btn {
    border: 1px solid #e9ecef;
    background: white;
    color: #495057;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    min-width: 60px;
}

.currency-dropdown .btn:hover {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-1px);
}

.currency-dropdown .dropdown-menu {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
}

.currency-dropdown .dropdown-item {
    padding: 8px 16px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.currency-dropdown .dropdown-item:hover {
    background: #f8f9fa;
    color: #007bff;
}

/* Language and User Menu Customization */
.lpx-topbar .navbar-nav .nav-link {
    font-weight: 500;
    color: #495057 !important;
    transition: all 0.2s ease;
    border-radius: 6px;
    padding: 8px 12px !important;
}

.lpx-topbar .navbar-nav .nav-link:hover {
    color: #007bff !important;
    background: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

/* Language selector specific */
.lpx-topbar .language-selector .nav-link {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* User menu specific */
.lpx-topbar .user-menu .nav-link {
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.lpx-topbar .user-menu .nav-link::before {
    content: '👤';
    font-size: 1.1rem;
}

/* Topbar spacing and alignment */
.lpx-topbar .navbar-nav {
    gap: 8px;
}

.lpx-topbar .navbar-nav .nav-item {
    display: flex;
    align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Ensure notification bell is visible on mobile */
    .notification-bell-container {
        display: block !important;
        visibility: visible !important;
        margin-right: 4px;
    }

    .notification-bell-btn {
        padding: 3px;
        display: block !important;
        visibility: visible !important;
    }

    .notification-icon {
        font-size: 1rem;
    }

    .notification-badge {
        width: auto;
        height: auto;
        min-height: 20px;
        min-width: 20px;
        font-size: 0.55rem;
        top: 1px;
        right: 1px;
        border-width: 1px;
        line-height: 1;
        padding: 0;
    }

    .notification-dropdown {
        min-width: 280px;
        max-width: 90vw;
    }

    /* Ensure currency dropdown is visible on mobile */
    .currency-dropdown-container {
        display: block !important;
        visibility: visible !important;
    }

    .currency-dropdown {
        display: block !important;
        visibility: visible !important;
    }

    .currency-dropdown .btn {
        display: block !important;
        visibility: visible !important;
    }
}

/* Additional Topbar Enhancements */
.lpx-topbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: visible;
    min-height: 60px;
}

/* Ensure toolbar items are visible on all screen sizes */
.lpx-topbar .toolbar-item {
    display: block !important;
    visibility: visible !important;
}

.lpx-topbar .d-flex {
    display: flex !important;
    visibility: visible !important;
}

.lpx-topbar .navbar-brand {
    font-weight: 600;
    color: #495057 !important;
}

.lpx-topbar .navbar-nav .nav-item {
    margin: 0 4px;
}

/* Toolbar item spacing */
.lpx-topbar .toolbar-item {
    margin: 0 8px;
}

/* Custom topbar items alignment */
.lpx-topbar .d-flex {
    align-items: center;
    gap: 16px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .lpx-topbar {
        background: linear-gradient(135deg, #212529 0%, #343a40 100%);
        border-bottom-color: #495057;
    }

    .lpx-topbar .navbar-brand {
        color: #dee2e6 !important;
    }

    .notification-bell-btn {
        color: #adb5bd !important;
    }

    .notification-bell-btn:hover {
        background: rgba(173, 181, 189, 0.1) !important;
        color: #dee2e6 !important;
    }

    .notification-dropdown {
        background: #212529;
        border-color: #495057;
    }

    .notification-dropdown .dropdown-header {
        background: #343a40;
        border-color: #495057;
    }

    .notification-item:hover {
        background: #343a40 !important;
    }

    .currency-dropdown .btn {
        background: #343a40;
        border-color: #495057;
        color: #dee2e6;
    }

    .currency-dropdown .btn:hover {
        background: #495057;
        border-color: #6c757d;
    }

    .currency-dropdown .dropdown-menu {
        background: #212529;
        border-color: #495057;
    }

    .currency-dropdown .dropdown-item {
        color: #dee2e6;
    }

    .currency-dropdown .dropdown-item:hover {
        background: #343a40;
        color: #007bff;
    }
}
