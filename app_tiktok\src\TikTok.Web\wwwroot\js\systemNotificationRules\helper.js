document.addEventListener('DOMContentLoaded', function () {
    $('[data-toggle="tooltip"]').tooltip();
    setupDefaultRuleLogic();
});

/**
 * Setup logic for default rule checkbox
 * Disables manage consumers button when IsDefault is checked
 */
function setupDefaultRuleLogic() {
    const isDefaultCheckbox = document.getElementById('editIsDefault');

    if (!isDefaultCheckbox) {
        return;
    }

    isDefaultCheckbox.addEventListener('change', function () {
        const isChecked = this.checked;

        const manageConsumersButtons = document.querySelectorAll(
            '[data-action="manageConsumers"]'
        );
        manageConsumersButtons.forEach((btn) => {
            if (isChecked) {
                btn.disabled = true;
                btn.classList.add('disabled');
                btn.title = 'Quy tắc mặc định không cần set đối tượng sử dụng';
            } else {
                btn.disabled = false;
                btn.classList.remove('disabled');
                btn.title = 'Quản lý đối tượng sử dụng';
            }
        });
    });

    if (isDefaultCheckbox.checked) {
        isDefaultCheckbox.dispatchEvent(new Event('change'));
    }
}

window.setupDefaultRuleLogic = setupDefaultRuleLogic;

window.snr = window.snr || {};

window.snr.api = {
    list: '/api/system-notification-rules',
    detail: (id) => `/api/system-notification-rules/${id}`,
    create: '/api/system-notification-rules',
    update: (id) => `/api/system-notification-rules/${id}`,
    delete: (id) => `/api/system-notification-rules/${id}`,
    toggleActive: (id) => `/api/system-notification-rules/${id}/toggle-active`,
    entityTypes: '/api/system-notification-rules/entity-types',
    fields: (entity) =>
        `/api/system-notification-rules/fields/${encodeURIComponent(entity)}`,
    operators: '/api/system-notification-rules/operators',
    // Consumer management endpoints
    consumers: (id) =>
        `/api/system-notification-rule-consumers/${id}/consumers`,
    addConsumers: '/api/system-notification-rule-consumers/add-consumers',
    removeConsumers: (id) =>
        `/api/system-notification-rule-consumers/${id}/consumers`,
};

window.snr.http = async function (url, options = {}) {
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...options.headers,
    };

    // Add ABP authorization header if available
    if (window.abp && window.abp.auth && window.abp.auth.getToken) {
        const token = window.abp.auth.getToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
    }

    const res = await fetch(url, {
        headers: headers,
        credentials: 'include',
        ...options,
    });
    if (!res.ok) {
        const msg = await res.text();
        throw new Error(msg || `HTTP ${res.status}`);
    }
    const ct = res.headers.get('content-type') || '';
    return ct.includes('application/json') ? res.json() : res.text();
};
