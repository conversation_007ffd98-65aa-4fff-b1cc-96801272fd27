document.addEventListener('DOMContentLoaded', function () {
    const hasDefault = abp.auth.isGranted('TikTok.SystemNotificationRules');
    const canCreate = abp.auth.isGranted(
        'TikTok.SystemNotificationRules.Create'
    );
    const canEdit = abp.auth.isGranted('TikTok.SystemNotificationRules.Edit');
    const canDelete = abp.auth.isGranted(
        'TikTok.SystemNotificationRules.Delete'
    );
    const canViewAll = abp.auth.isGranted(
        'TikTok.SystemNotificationRules.ViewAll'
    );
    const canManageRule = abp.auth.isGranted(
        'TikTok.SystemNotificationRules.ManageRule'
    );

    if (
        !(
            hasDefault ||
            canCreate ||
            canEdit ||
            canDelete ||
            canViewAll ||
            canManageRule
        )
    ) {
        const mainContent = document.querySelector('abp-card');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="alert alert-danger text-center p-5">
                    <i class="fas fa-ban fa-3x mb-3"></i>
                    <h4>Không có quyền truy cập</h4>
                    <p>Bạn không có quyền truy cập vào trang quản lý hệ thống quy tắc thông báo.</p>
                </div>
            `;
        }
        return;
    }

    window.systemNotificationRulePermission = {
        canView: hasDefault,
        canViewAll: canViewAll,
        canCreate: canCreate,
        canEdit: canEdit,
        canDelete: canDelete,
        canManageRule: canManageRule,
        hasWritePermission: function () {
            return canCreate || canEdit || canDelete || canManageRule;
        },
    };

    const newRuleButton = document.getElementById('NewRuleButton');
    if (newRuleButton) {
        newRuleButton.style.display = canCreate ? 'inline-block' : 'none';
    }
});
