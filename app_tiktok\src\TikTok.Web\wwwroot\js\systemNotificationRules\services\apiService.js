/**
 * API Service Layer for System Notification Rules
 * Centralized API calls and data management
 */

const SystemNotificationApiService = {
    // Load rules with filters
    loadRules(filters = {}) {
        const params = new URLSearchParams();
        if (filters.searchText) params.append('searchText', filters.searchText);
        if (filters.entityType) params.append('entityType', filters.entityType);
        if (filters.isDefault) params.append('isDefault', filters.isDefault);

        const url = `${window.snr.api.list}?${params.toString()}`;
        return window.snr.http(url);
    },

    // Get rule detail by ID
    getRuleDetail(id) {
        return window.snr.http(window.snr.api.detail(id));
    },

    // Create new rule
    createRule(ruleData) {
        return window.snr.http(window.snr.api.create, {
            method: 'POST',
            body: JSON.stringify(ruleData),
            headers: { 'Content-Type': 'application/json' }
        });
    },

    // Update existing rule
    updateRule(id, ruleData) {
        return window.snr.http(window.snr.api.update(id), {
            method: 'PUT',
            body: JSON.stringify(ruleData),
            headers: { 'Content-Type': 'application/json' }
        });
    },

    // Delete rule
    deleteRule(id) {
        return window.snr.http(window.snr.api.delete(id), {
            method: 'DELETE'
        });
    },

    // Get fields for entity type
    getFields(entityType) {
        return window.snr.http(window.snr.api.fields(entityType));
    },

    // Get rule consumers
    getRuleConsumers(ruleId) {
        return window.snr.http(window.snr.api.consumers(ruleId));
    },

    // Add consumers to rule
    addConsumers(ruleId, consumerData) {
        console.log('addConsumers called with:', { ruleId, consumerData });
        console.log('window.snr.api.addConsumers:', window.snr.api.addConsumers);
        
        if (!window.snr || !window.snr.api || !window.snr.api.addConsumers) {
            throw new Error('window.snr.api.addConsumers is not available');
        }
        
        return window.snr.http(window.snr.api.addConsumers, {
            method: 'POST',
            body: JSON.stringify({
                SystemNotificationRuleId: ruleId,
                ...consumerData
            }),
            headers: { 'Content-Type': 'application/json' }
        });
    },

    // Remove consumers from rule
    removeConsumers(ruleId, consumerIds) {
        return window.snr.http(window.snr.api.removeConsumers(ruleId), {
            method: 'DELETE',
            body: JSON.stringify(consumerIds),
            headers: { 'Content-Type': 'application/json' }
        });
    },

    // Get available ad accounts
    getAdAccounts(searchParams = {}) {
        const searchInput = {
            maxResultCount: searchParams.maxResultCount || 50,
            skipCount: searchParams.skipCount || 0,
            sorting: searchParams.sorting || 'name',
            filterText: searchParams.filterText || '',
            customerFilterText: searchParams.customerFilterText || ''
        };

        return tikTok.adAccounts.adAccount.getList(searchInput);
    }
};

// Export to global scope
window.SystemNotificationApiService = SystemNotificationApiService;
