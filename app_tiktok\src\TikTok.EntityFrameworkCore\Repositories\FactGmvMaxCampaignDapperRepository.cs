using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Domain.Repositories;
using TikTok.EntityFrameworkCore.Repositories;
using Volo.Abp.EntityFrameworkCore;
using TikTok.Facts.FactGmvMaxCampaign;
using TikTok.Repositories;

namespace TikTok.EntityFrameworkCore.Repositories
{
    public class FactGmvMaxCampaignDapperRepository : DapperRepository<FactGmvMaxCampaignEntity, Guid>, IFactGmvMaxCampaignDapperRepository
    {
        public FactGmvMaxCampaignDapperRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }


        public async Task<IEnumerable<FactGmvMaxCampaignEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds, string? currency = "USD")
        {
            // ✅ Xử lý filter AdvertiserId: null=admin (xem tất cả), empty=không có quyền, có data=filter theo quyền
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT fc.*
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                WHERE fc.Date >= @From AND fc.Date <= @To
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                ORDER BY fc.Date";

            return await QueryAsync<FactGmvMaxCampaignEntity>(sql, new { From = from.Date, To = to.Date });
        }

        // ✅ NEW: Enhanced method with additional filters for better performance
        public async Task<IEnumerable<FactGmvMaxCampaignEntity>> GetListByDateRangeWithFiltersAsync(
            DateTime from, 
            DateTime to, 
            List<string>? allowedAdvertiserIds = null, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? shoppingAdsType = null,
            string? operationStatus = null)
        {
            // ✅ Build dynamic WHERE conditions for optimal performance
            var whereConditions = new List<string>();
            var parameters = new Dictionary<string, object>
            {
                { "From", from.Date },
                { "To", to.Date }
            };

            // ✅ Advertiser filter (permission-based)
            if (allowedAdvertiserIds == null)
            {
                // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                whereConditions.Add("1=0"); // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                whereConditions.Add($"fc.AdvertiserId IN ({advertiserIdList})");
            }

            // ✅ Campaign filter
            if (campaignIds != null && campaignIds.Any())
            {
                var campaignIdList = string.Join(",", campaignIds.Select(id => $"'{id}'"));
                whereConditions.Add($"fc.CampaignId IN ({campaignIdList})");
            }

            // ✅ Store filter
            if (shopIds != null && shopIds.Any())
            {
                var shopIdList = string.Join(",", shopIds.Select(id => $"'{id}'"));
                whereConditions.Add($"fc.StoreId IN ({shopIdList})");
            }

            // ✅ Search text filter (restricted fields)
            if (!string.IsNullOrEmpty(searchText))
            {
                whereConditions.Add("(fc.BcId LIKE @SearchText OR fc.StoreId LIKE @SearchText OR fc.CampaignId LIKE @SearchText OR fc.AdvertiserId LIKE @SearchText)");
                parameters["SearchText"] = $"%{searchText}%";
            }

            // ✅ Shopping ads type filter
            if (!string.IsNullOrEmpty(shoppingAdsType))
            {
                whereConditions.Add("fc.ShoppingAdsType = @ShoppingAdsType");
                parameters["ShoppingAdsType"] = shoppingAdsType;
            }

            // ✅ Operation status filter
            if (!string.IsNullOrEmpty(operationStatus))
            {
                whereConditions.Add("fc.OperationStatus = @OperationStatus");
                parameters["OperationStatus"] = operationStatus;
            }
            else
            {
                // Default to ENABLE if not specified
                whereConditions.Add("fc.OperationStatus = 'ENABLE'");
            }

            // ✅ Build final SQL with dynamic WHERE clause
            var whereClause = whereConditions.Any() ? "AND " + string.Join(" AND ", whereConditions) : "";
            
            string sql = $@"
                SELECT fc.*
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                WHERE fc.Date >= @From AND fc.Date <= @To
                {whereClause}
                ORDER BY fc.Date";

            return await QueryAsync<FactGmvMaxCampaignEntity>(sql, parameters);
        }



        public async Task<GmvMaxCampaignDashboardDto> GetDashboardAsync(string? currency, List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);
            var lastMonthEnd = currentMonth.AddDays(-1);

            // Tính toán tuần đúng theo thứ 2 đến chủ nhật
            // Tuần hiện tại: từ thứ 2 tuần này đến chủ nhật tuần này
            var currentWeekStart = GetWeekStart(now); // Thứ 2 tuần này
            var currentWeekEnd = currentWeekStart.AddDays(6); // Chủ nhật tuần này

            // Tính toán tuần tương ứng tháng trước
            var lastMonthWeekStart = GetWeekStartInMonth(lastMonth.AddDays(now.Day - 1));
            var lastMonthWeekEnd = lastMonthWeekStart.AddDays(6);

            // Tuần trước: từ thứ 2 tuần trước đến chủ nhật tuần trước
            var oneWeekAgoStart = currentWeekStart.AddDays(-7); // Thứ 2 tuần trước
            var oneWeekAgoEnd = oneWeekAgoStart.AddDays(6); // Chủ nhật tuần trước

            // 2 tuần trước: từ thứ 2 2 tuần trước đến chủ nhật 2 tuần trước
            var twoWeeksAgoStart = currentWeekStart.AddDays(-14); // Thứ 2 2 tuần trước
            var twoWeeksAgoEnd = twoWeeksAgoStart.AddDays(6); // Chủ nhật 2 tuần trước

            // ✅ Tạo advertiser filter clause
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho doanh thu và chi tiêu theo tháng - sử dụng Date index trực tiếp
            string monthlySql = $@"
                SELECT YEAR(fc.Date) AS [Year], MONTH(fc.Date) AS [Month], 
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                WHERE fc.Date >= @FromDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY YEAR(fc.Date), MONTH(fc.Date)
                ORDER BY [Year], [Month];";

            // ✅ OPTIMIZED: SQL cho doanh thu và chi tiêu theo tuần - sử dụng Date index trực tiếp
            string weeklySql = $@"
                SELECT YEAR(fc.Date) AS [Year], MONTH(fc.Date) AS [Month],
                       -- Tính tuần trong tháng: 1-7 = tuần 1, 8-14 = tuần 2, 15-21 = tuần 3, 22-31 = tuần 4
                       CASE 
                           WHEN DAY(fc.Date) BETWEEN 1 AND 7 THEN 1
                           WHEN DAY(fc.Date) BETWEEN 8 AND 14 THEN 2
                           WHEN DAY(fc.Date) BETWEEN 15 AND 21 THEN 3
                           WHEN DAY(fc.Date) BETWEEN 22 AND 31 THEN 4
                           ELSE 4
                       END AS [Week],
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                WHERE fc.Date >= @FromDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY YEAR(fc.Date), MONTH(fc.Date),
                         CASE 
                             WHEN DAY(fc.Date) BETWEEN 1 AND 7 THEN 1
                             WHEN DAY(fc.Date) BETWEEN 8 AND 14 THEN 2
                             WHEN DAY(fc.Date) BETWEEN 15 AND 21 THEN 3
                             WHEN DAY(fc.Date) BETWEEN 22 AND 31 THEN 4
                             ELSE 4
                         END
                ORDER BY [Year], [Month], [Week];";

            // ✅ OPTIMIZED: SQL cho xếp hạng store theo tuần - sử dụng Date index và StoreId index
            string storeRankingSql = $@"
                SELECT TOP 10 fc.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fc.ROAS IS NOT NULL THEN fc.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fc.DimStoreId
                WHERE fc.Date >= @FromDate AND fc.Date <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY fc.StoreId
                ORDER BY TotalRevenue DESC;";

            // ✅ OPTIMIZED: SQL cho xếp hạng store theo tuần (chi tiêu) - sử dụng Date index và StoreId index
            string storeCostRankingSql = $@"
                SELECT TOP 10 fc.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost,
                       SUM(fc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fc.ROAS IS NOT NULL THEN fc.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fc.DimStoreId
                WHERE fc.Date >= @FromDate AND fc.Date <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY fc.StoreId
                ORDER BY TotalCost DESC;";

            // Lấy dữ liệu 12 tháng gần nhất
            var fromDate = now.AddMonths(-12);
            var monthly = await QueryAsync<DashboardMonthlyRevenue>(monthlySql, new { FromDate = fromDate.Date });
            var weekly = await QueryAsync<DashboardWeeklyRevenue>(weeklySql, new { FromDate = fromDate.Date });

            // Lấy xếp hạng store tuần hiện tại (doanh thu)
            var currentWeekRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Lấy xếp hạng store 2 tuần trước (doanh thu)
            var twoWeeksAgoRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // Lấy xếp hạng store 1 tuần trước (doanh thu)
            var oneWeekAgoRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // ✅ NEW: Lấy xếp hạng store tuần hiện tại (chi tiêu)
            var currentWeekCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // ✅ NEW: Lấy xếp hạng store 2 tuần trước (chi tiêu)
            var twoWeeksAgoCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // ✅ NEW: Lấy xếp hạng store 1 tuần trước (chi tiêu)
            var oneWeekAgoCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // Tính toán doanh thu và chi tiêu tháng hiện tại và tháng trước
            decimal currentMonthRevenue = 0, lastMonthRevenue = 0;
            decimal currentMonthCost = 0, lastMonthCost = 0;
            foreach (var m in monthly)
            {
                if (m.Year == now.Year && m.Month == now.Month) 
                {
                    currentMonthRevenue = m.TotalRevenue;
                    currentMonthCost = m.TotalCost;
                }
                if (m.Year == lastMonth.Year && m.Month == lastMonth.Month) 
                {
                    lastMonthRevenue = m.TotalRevenue;
                    lastMonthCost = m.TotalCost;
                }
            }

            return new GmvMaxCampaignDashboardDto
            {
                CurrentMonth = new CampaignDashboardMonthInfo { Year = now.Year, Month = now.Month, TotalRevenue = currentMonthRevenue, TotalCost = currentMonthCost },
                LastMonth = new CampaignDashboardMonthInfo { Year = lastMonth.Year, Month = lastMonth.Month, TotalRevenue = lastMonthRevenue, TotalCost = lastMonthCost },
                WeeklyData = new List<DashboardWeeklyRevenue>(weekly),
                MonthlyData = new List<DashboardMonthlyRevenue>(monthly),
                CurrentWeekStoreRanking = new List<DashboardStoreRanking>(currentWeekRanking),
                TwoWeeksAgoStoreRanking = new List<DashboardStoreRanking>(twoWeeksAgoRanking),
                OneWeekAgoStoreRanking = new List<DashboardStoreRanking>(oneWeekAgoRanking),
                // ✅ NEW: Cost rankings
                CurrentWeekStoreCostRanking = new List<DashboardStoreCostRanking>(currentWeekCostRanking),
                TwoWeeksAgoStoreCostRanking = new List<DashboardStoreCostRanking>(twoWeeksAgoCostRanking),
                OneWeekAgoStoreCostRanking = new List<DashboardStoreCostRanking>(oneWeekAgoCostRanking)
            };
        }

        private DateTime GetWeekStartInMonth(DateTime date)
        {
            var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);
            var dayOfWeek = (int)firstDayOfMonth.DayOfWeek;
            var weekStart = firstDayOfMonth.AddDays(-dayOfWeek + 1);

            // Tìm tuần chứa ngày hiện tại
            while (weekStart.AddDays(6) < date)
            {
                weekStart = weekStart.AddDays(7);
            }

            return weekStart;
        }

        // Helper method to get the start of the week (Monday) for any date
        private DateTime GetWeekStart(DateTime date)
        {
            // Calculate days to subtract to get to Monday (0 = Sunday, 1 = Monday, etc.)
            var daysToSubtract = ((int)date.DayOfWeek + 6) % 7; // Convert Sunday=0 to Monday=0
            return date.AddDays(-daysToSubtract).Date;
        }


        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency, List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var from = new DateTime(now.Year, now.Month, 1); // Ngày đầu tháng
            var to = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59); // Ngày cuối tháng

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: Sử dụng Date index trực tiếp thay vì JOIN với DimDates
            string sql = $@"
                SELECT 
                    -- Financial Metrics
                    SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalCost,
                    SUM(ISNULL(fc.NetCost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalNetCost,
                    SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    
                    -- Performance Metrics
                    AVG(CASE WHEN fc.ROAS IS NOT NULL THEN fc.ROAS ELSE NULL END) AS AverageROAS,
                    AVG(CASE WHEN fc.TACOS IS NOT NULL THEN fc.TACOS ELSE NULL END) AS AverageTACOS,
                    
                    -- Volume Metrics
                    SUM(fc.Orders) AS TotalOrders,
                    
                    -- Campaign Metrics
                    COUNT(DISTINCT fc.CampaignId) AS CampaignCount,
                    COUNT(DISTINCT fc.DimProductId) AS ProductCount,
                    COUNT(DISTINCT fc.StoreId) AS ActiveStores,
                    COUNT(DISTINCT fc.DimAdAccountId) AS ActiveAdvertisers,
                    
                    -- Data Count
                    COUNT(*) AS DataPointCount
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                WHERE fc.Date >= @From AND fc.Date <= @To
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}";

            var result = await QuerySingleOrDefaultAsync<DashboardSummaryDto>(sql, new { From = from.Date, To = to.Date });

            if (result == null)
            {
                // Return empty result if no data found
                return new DashboardSummaryDto
                {
                    TotalCost = 0m,
                    TotalNetCost = 0m,
                    TotalGrossRevenue = 0m,
                    AverageROAS = 0m,
                    AverageTACOS = 0m,
                    TotalOrders = 0,
                    CampaignCount = 0,
                    ProductCount = 0,
                    ActiveStores = 0,
                    ActiveAdvertisers = 0,
                    DataPointCount = 0,
                    Month = now.Month,
                    Year = now.Year,
                    MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN")),
                    FromDate = from,
                    ToDate = to
                };
            }

            // Set additional properties
            result.Month = now.Month;
            result.Year = now.Year;
            result.MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"));
            result.FromDate = from;
            result.ToDate = to;

            return result;
        }


        public async Task<object> GetDetailedAnalysisDataAsync(List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var from = now.AddDays(-6).Date; // 7 ngày gần nhất (bao gồm hôm nay)
            var to = now.Date.AddDays(1).AddSeconds(-1); // Đến cuối ngày hôm nay

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: Sử dụng Date index trực tiếp thay vì JOIN với DimDates
            string sql = $@"
                SELECT 
                    fc.Date AS FullDate,
                    DAY(fc.Date) AS DayNumber,
                    DATENAME(WEEKDAY, fc.Date) AS DayName,
                    -- Financial Metrics
                    SUM(ISNULL(fc.Cost, 0)) AS TotalCost,
                    SUM(ISNULL(fc.NetCost, 0)) AS TotalNetCost,
                    SUM(ISNULL(fc.GrossRevenue, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fc.GrossRevenueVND, 0)) AS TotalGrossRevenueVND,
                    -- Orders
                    SUM(ISNULL(fc.Orders, 0)) AS TotalOrders,
                    -- Live Views (for LIVE campaigns only)
                    SUM(ISNULL(fc.LiveViews, 0)) AS TotalLiveViews,
                    SUM(ISNULL(fc.TenSecondLiveViews, 0)) AS TotalTenSecondLiveViews,
                    -- Calculated Metrics
                    CASE 
                        WHEN SUM(ISNULL(fc.Cost, 0)) > 0 
                        THEN (SUM(ISNULL(fc.GrossRevenue, 0)) / SUM(ISNULL(fc.Cost, 0))) * 100
                        ELSE 0 
                    END AS ROI,
                    CASE 
                        WHEN SUM(ISNULL(fc.Orders, 0)) > 0 
                        THEN SUM(ISNULL(fc.Cost, 0)) / SUM(ISNULL(fc.Orders, 0))
                        ELSE 0 
                    END AS CPA,
                    CASE 
                        WHEN SUM(ISNULL(fc.LiveViews, 0)) > 0 
                        THEN (SUM(ISNULL(fc.TenSecondLiveViews, 0)) / SUM(ISNULL(fc.LiveViews, 0))) * 100
                        ELSE 0 
                    END AS LiveEngagementRate
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                WHERE fc.Date >= @FromDate AND fc.Date <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY fc.Date, DAY(fc.Date), DATENAME(WEEKDAY, fc.Date)
                ORDER BY fc.Date ASC";

            var results = await QueryAsync<dynamic>(sql, new { FromDate = from, ToDate = to });

            return new
            {
                FromDate = from,
                ToDate = to,
                DailyData = results.Select(r => new
                {
                    Date = (DateTime)r.FullDate,
                    DayNumber = (int)r.DayNumber,
                    DayName = (string)r.DayName,
                    // Financial
                    Cost = (decimal)(r.TotalCost ?? 0),
                    NetCost = (decimal)(r.TotalNetCost ?? 0),
                    Revenue = (decimal)(r.TotalGrossRevenue ?? 0),
                    RevenueVND = (decimal)(r.TotalGrossRevenueVND ?? 0),
                    ROI = (decimal)(r.ROI ?? 0),
                    // Orders
                    Orders = (int)(r.TotalOrders ?? 0),
                    CPA = (decimal)(r.CPA ?? 0),
                    // Live Performance (for LIVE campaigns)
                    LiveViews = (long)(r.TotalLiveViews ?? 0),
                    TenSecondLiveViews = (long)(r.TotalTenSecondLiveViews ?? 0),
                    LiveEngagementRate = (decimal)(r.LiveEngagementRate ?? 0)
                }).ToList()
            };
        }

        // ✅ NEW: Section-specific methods for independent loading

        public async Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD", 
            List<string>? allowedAdvertiserIds = null,
            string? shoppingAdsType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null)
        {
            // ✅ Use provided date range or default to current month
            var now = DateTime.Now;
            var from = fromDate ?? new DateTime(now.Year, now.Month, 1);
            var to = toDate ?? new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59);

            // ✅ Build dynamic filters
            var filters = new List<string>();
            var parameters = new Dictionary<string, object>
            {
                { "FromDate", from },
                { "ToDate", to }
            };

            // ✅ Advertiser filter
            if (allowedAdvertiserIds == null)
            {
                // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                filters.Add("1=0"); // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                filters.Add($"fc.AdvertiserId IN ({advertiserIdList})");
            }

            // ✅ Shopping Ads Type filter
            if (!string.IsNullOrEmpty(shoppingAdsType))
            {
                filters.Add("fc.ShoppingAdsType = @ShoppingAdsType");
                parameters.Add("ShoppingAdsType", shoppingAdsType);
            }

            // ✅ Search text filter (restricted fields)
            if (!string.IsNullOrEmpty(searchText))
            {
                filters.Add(@"(fc.BcId LIKE @SearchText OR fc.StoreId LIKE @SearchText OR fc.CampaignId LIKE @SearchText OR fc.AdvertiserId LIKE @SearchText)");
                parameters.Add("SearchText", $"%{searchText}%");
            }

            // ✅ Shop IDs filter
            if (shopIds != null && shopIds.Any())
            {
                var shopIdList = string.Join(",", shopIds.Select(id => $"'{id}'"));
                filters.Add($"fc.StoreId IN ({shopIdList})");
            }

            // ✅ Campaign IDs filter
            if (campaignIds != null && campaignIds.Any())
            {
                var campaignIdList = string.Join(",", campaignIds.Select(id => $"'{id}'"));
                filters.Add($"fc.CampaignId IN ({campaignIdList})");
            }

            var whereClause = filters.Any() ? "AND " + string.Join(" AND ", filters) : "";

            // ✅ OPTIMIZED: Sử dụng currency field để tăng performance
            string sql = $@"
                SELECT 
                    SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalCost,
                    SUM(ISNULL(fc.NetCost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalNetCost,
                    SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fc.Orders, 0)) AS TotalOrders,
                    COUNT(DISTINCT fc.CampaignId) AS CampaignCount,
                    COUNT(DISTINCT fc.StoreId) AS ActiveStores,
                    COUNT(DISTINCT fc.AdvertiserId) AS ActiveAdvertisers,
                    CASE 
                        WHEN SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) > 0 
                        THEN SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) / SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0))
                        ELSE 0 
                    END AS AverageROAS,
                    CASE 
                        WHEN SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) > 0 
                        THEN SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) / SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0))
                        ELSE 0 
                    END AS AverageTACOS
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {whereClause}";

            var result = await QueryAsync<dynamic>(sql, parameters.ToDictionary(p => p.Key, p => p.Value));
            var firstResult = result.FirstOrDefault();

            return new SummaryCardsDto
            {
                TotalCost = (decimal)(firstResult?.TotalCost ?? 0),
                TotalNetCost = (decimal)(firstResult?.TotalNetCost ?? 0),
                TotalGrossRevenue = (decimal)(firstResult?.TotalGrossRevenue ?? 0),
                AverageROAS = (decimal)(firstResult?.AverageROAS ?? 0),
                AverageTACOS = (decimal)(firstResult?.AverageTACOS ?? 0),
                TotalOrders = (int)(firstResult?.TotalOrders ?? 0),
                CampaignCount = (int)(firstResult?.CampaignCount ?? 0),
                ActiveStores = (int)(firstResult?.ActiveStores ?? 0),
                ActiveAdvertisers = (int)(firstResult?.ActiveAdvertisers ?? 0),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho tháng hiện tại và tháng trước - với currency support
            string monthlySql = $@"
                SELECT 
                    CASE 
                        WHEN dd.FullDate >= @CurrentMonthStart AND dd.FullDate < @NextMonthStart THEN 'Current'
                        WHEN dd.FullDate >= @LastMonthStart AND dd.FullDate < @CurrentMonthStart THEN 'Last'
                        ELSE 'Other'
                    END AS MonthType,
                    SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalCost,
                    SUM(ISNULL(fc.NetCost{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalNetCost,
                    SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fc.Orders, 0)) AS TotalOrders
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                WHERE (dd.FullDate >= @CurrentMonthStart AND dd.FullDate < @NextMonthStart)
                   OR (dd.FullDate >= @LastMonthStart AND dd.FullDate < @CurrentMonthStart)
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY 
                    CASE 
                        WHEN dd.FullDate >= @CurrentMonthStart AND dd.FullDate < @NextMonthStart THEN 'Current'
                        WHEN dd.FullDate >= @LastMonthStart AND dd.FullDate < @CurrentMonthStart THEN 'Last'
                        ELSE 'Other'
                    END";

            // ✅ OPTIMIZED: SQL cho weekly data - chỉ lấy tháng hiện tại và tháng trước
            string weeklySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month],
                       -- Tính tuần trong tháng: 1-7 = tuần 1, 8-14 = tuần 2, 15-21 = tuần 3, 22-31 = tuần 4
                       CASE 
                           WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                           WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                           WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                           WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                           ELSE 4
                       END AS [Week],
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate),
                         CASE 
                             WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                             WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                             WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                             WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                             ELSE 4
                         END
                ORDER BY [Year], [Month], [Week];";

            var monthlyResults = await QueryAsync<dynamic>(monthlySql, new { 
                CurrentMonthStart = currentMonth,
                NextMonthStart = currentMonth.AddMonths(1),
                LastMonthStart = lastMonth
            });

            // ✅ OPTIMIZED: Chỉ lấy dữ liệu 2 tháng gần nhất cho weekly data
            var fromDate = lastMonth; // Tháng trước
            var toDate = currentMonth.AddMonths(1).AddDays(-1); // Cuối tháng hiện tại
            var weeklyResults = await QueryAsync<DashboardWeeklyRevenue>(weeklySql, new { FromDate = fromDate.Date, ToDate = toDate.Date });

            var currentMonthData = monthlyResults.FirstOrDefault(r => r.MonthType == "Current");
            var lastMonthData = monthlyResults.FirstOrDefault(r => r.MonthType == "Last");

            return new OverviewSectionDto
            {
                CurrentMonth = new MonthDataDto
                {
                    Year = now.Year,
                    Month = now.Month,
                    TotalCost = (decimal)(currentMonthData?.TotalCost ?? 0),
                    TotalRevenue = (decimal)(currentMonthData?.TotalGrossRevenue ?? 0),
                    MonthName = now.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"))
                },
                LastMonth = new MonthDataDto
                {
                    Year = lastMonth.Year,
                    Month = lastMonth.Month,
                    TotalCost = (decimal)(lastMonthData?.TotalCost ?? 0),
                    TotalRevenue = (decimal)(lastMonthData?.TotalGrossRevenue ?? 0),
                    MonthName = lastMonth.ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"))
                },
                WeeklyData = weeklyResults.Select(w => new WeeklyDataDto
                {
                    Year = w.Year,
                    Month = w.Month,
                    Week = w.Week,
                    TotalCost = w.TotalCost,
                    TotalRevenue = w.TotalRevenue
                }).ToList(),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            // ✅ OPTIMIZED: Chỉ lấy 6 tháng gần nhất (tính cả tháng hiện tại)
            var from = now.AddMonths(-5); // 6 tháng gần nhất

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho weekly data - chỉ lấy tháng hiện tại và tháng trước
            string weeklySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month],
                       -- Tính tuần trong tháng: 1-7 = tuần 1, 8-14 = tuần 2, 15-21 = tuần 3, 22-31 = tuần 4
                       CASE 
                           WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                           WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                           WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                           WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                           ELSE 4
                       END AS [Week],
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate),
                         CASE 
                             WHEN DAY(dd.FullDate) BETWEEN 1 AND 7 THEN 1
                             WHEN DAY(dd.FullDate) BETWEEN 8 AND 14 THEN 2
                             WHEN DAY(dd.FullDate) BETWEEN 15 AND 21 THEN 3
                             WHEN DAY(dd.FullDate) BETWEEN 22 AND 31 THEN 4
                             ELSE 4
                         END
                ORDER BY [Year], [Month], [Week];";

            // ✅ OPTIMIZED: SQL cho monthly data - chỉ lấy 6 tháng gần nhất
            string monthlySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month], 
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                WHERE dd.FullDate >= @FromDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate)
                ORDER BY [Year], [Month];";

            // ✅ OPTIMIZED: Chỉ lấy weekly data cho tháng hiện tại và tháng trước
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);
            var weeklyFromDate = lastMonth;
            var weeklyToDate = currentMonth.AddMonths(1).AddDays(-1);

            var weeklyResults = await QueryAsync<DashboardWeeklyRevenue>(weeklySql, new { FromDate = weeklyFromDate.Date, ToDate = weeklyToDate.Date });
            var monthlyResults = await QueryAsync<DashboardMonthlyRevenue>(monthlySql, new { FromDate = from.Date });

            return new ChartsDataDto
            {
                WeeklyData = weeklyResults.Select(w => new WeeklyDataDto
                {
                    Year = w.Year,
                    Month = w.Month,
                    Week = w.Week,
                    TotalCost = w.TotalCost,
                    TotalRevenue = w.TotalRevenue
                }).ToList(),
                MonthlyData = monthlyResults.Select(m => new MonthlyDataDto
                {
                    Year = m.Year,
                    Month = m.Month,
                    TotalCost = m.TotalCost,
                    TotalRevenue = m.TotalRevenue,
                    MonthName = new DateTime(m.Year, m.Month, 1).ToString("MMMM", new System.Globalization.CultureInfo("vi-VN"))
                }).ToList(),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            // ✅ OPTIMIZED: Chỉ lấy 7 ngày gần nhất (bao gồm hôm nay)
            var from = now.AddDays(-6).Date; // 7 ngày gần nhất (bao gồm hôm nay)
            var to = now.Date.AddDays(1).AddSeconds(-1); // Đến cuối ngày hôm nay

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ OPTIMIZED: SQL cho daily data - chỉ lấy các field cần thiết
            string dailySql = $@"
                SELECT 
                    dd.FullDate,
                    DAY(dd.FullDate) AS DayNumber,
                    DATENAME(WEEKDAY, dd.FullDate) AS DayName,
                    -- Financial Metrics (chỉ USD để tăng performance)
                    SUM(ISNULL(fc.Cost, 0)) AS TotalCost,
                    SUM(ISNULL(fc.NetCost, 0)) AS TotalNetCost,
                    SUM(ISNULL(fc.GrossRevenue, 0)) AS TotalGrossRevenue,
                    -- Orders
                    SUM(ISNULL(fc.Orders, 0)) AS TotalOrders,
                    -- Live Views (for LIVE campaigns only)
                    SUM(ISNULL(fc.LiveViews, 0)) AS TotalLiveViews,
                    SUM(ISNULL(fc.TenSecondLiveViews, 0)) AS TotalTenSecondLiveViews,
                    -- Calculated Metrics
                    CASE 
                        WHEN SUM(ISNULL(fc.Cost, 0)) > 0 
                        THEN (SUM(ISNULL(fc.GrossRevenue, 0)) / SUM(ISNULL(fc.Cost, 0))) * 100
                        ELSE 0 
                    END AS ROI,
                    CASE 
                        WHEN SUM(ISNULL(fc.Orders, 0)) > 0 
                        THEN SUM(ISNULL(fc.Cost, 0)) / SUM(ISNULL(fc.Orders, 0))
                        ELSE 0 
                    END AS CPA,
                    CASE 
                        WHEN SUM(ISNULL(fc.LiveViews, 0)) > 0 
                        THEN (SUM(ISNULL(fc.TenSecondLiveViews, 0)) / SUM(ISNULL(fc.LiveViews, 0))) * 100
                        ELSE 0 
                    END AS LiveEngagementRate
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY dd.FullDate, DAY(dd.FullDate), DATENAME(WEEKDAY, dd.FullDate)
                ORDER BY dd.FullDate ASC";

            var dailyResults = await QueryAsync<dynamic>(dailySql, new { FromDate = from, ToDate = to });

            return new DetailedChartsDto
            {
                FinancialAnalysis = new FinancialAnalysisDto
                {
                    DailyData = dailyResults.Select(r => new DailyDataDto
                    {
                        Date = (DateTime)r.FullDate,
                        Period = (string)r.DayName,
                        Cost = (decimal)(r.TotalCost ?? 0),
                        Revenue = (decimal)(r.TotalGrossRevenue ?? 0),
                        ROI = (decimal)(r.ROI ?? 0)
                    }).ToList()
                },
                OrdersAnalysis = new OrdersAnalysisDto
                {
                    DailyData = dailyResults.Select(r => new DailyDataDto
                    {
                        Date = (DateTime)r.FullDate,
                        Period = (string)r.DayName,
                        Orders = (int)(r.TotalOrders ?? 0),
                        CPA = (decimal)(r.CPA ?? 0)
                    }).ToList()
                },
                LivePerformance = new LivePerformanceDto
                {
                    DailyData = dailyResults.Select(r => new DailyDataDto
                    {
                        Date = (DateTime)r.FullDate,
                        Period = (string)r.DayName,
                        LiveViews = (int)(r.TotalLiveViews ?? 0),
                        TenSecondLiveViews = (int)(r.TotalTenSecondLiveViews ?? 0),
                        LiveEngagementRate = (decimal)(r.LiveEngagementRate ?? 0)
                    }).ToList()
                },
                FromDate = from,
                ToDate = to,
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            var now = DateTime.Now;
            
            // ✅ Sử dụng logic tuần giống GetDashboardAsync (thứ 2 đến chủ nhật)
            var currentWeekStart = GetWeekStart(now); // Thứ 2 tuần này
            var currentWeekEnd = currentWeekStart.AddDays(6); // Chủ nhật tuần này
            
            // Tuần trước: từ thứ 2 tuần trước đến chủ nhật tuần trước
            var oneWeekAgoStart = currentWeekStart.AddDays(-7); // Thứ 2 tuần trước
            var oneWeekAgoEnd = oneWeekAgoStart.AddDays(6); // Chủ nhật tuần trước
            
            // 2 tuần trước: từ thứ 2 2 tuần trước đến chủ nhật 2 tuần trước
            var twoWeeksAgoStart = currentWeekStart.AddDays(-14); // Thứ 2 2 tuần trước
            var twoWeeksAgoEnd = twoWeeksAgoStart.AddDays(6); // Chủ nhật 2 tuần trước

            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ SQL cho xếp hạng store theo tuần (doanh thu) - với currency support và store name
            string storeRankingSql = $@"
                SELECT TOP 10 fc.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fc.ROAS IS NOT NULL THEN fc.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fc.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY fc.StoreId
                ORDER BY TotalRevenue DESC;";

            // ✅ SQL cho xếp hạng store theo tuần (chi tiêu) - với currency support và store name
            string storeCostRankingSql = $@"
                SELECT TOP 10 fc.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fc.Cost{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalCost,
                       SUM(fc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fc.ROAS IS NOT NULL THEN fc.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxCampaigns] fc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fc.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fc.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fc.OperationStatus = 'ENABLE'
                {advertiserFilter}
                GROUP BY fc.StoreId
                ORDER BY TotalCost DESC;";

            // Lấy xếp hạng store tuần hiện tại (doanh thu)
            var currentWeekRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Lấy xếp hạng store 2 tuần trước (doanh thu)
            var twoWeeksAgoRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // Lấy xếp hạng store 1 tuần trước (doanh thu)
            var oneWeekAgoRanking = await QueryAsync<DashboardStoreRanking>(storeRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // ✅ Lấy xếp hạng store tuần hiện tại (chi tiêu)
            var currentWeekCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // ✅ Lấy xếp hạng store 2 tuần trước (chi tiêu)
            var twoWeeksAgoCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // ✅ Lấy xếp hạng store 1 tuần trước (chi tiêu)
            var oneWeekAgoCostRanking = await QueryAsync<DashboardStoreCostRanking>(storeCostRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            return new RankingsDataDto
            {
                CurrentWeekStoreRanking = currentWeekRanking.Select((r, index) => new StoreRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalRevenue = r.TotalRevenue,
                    TotalOrders = r.TotalOrders,
                    AverageROAS = r.AverageROAS,
                    Rank = index + 1
                }).ToList(),
                OneWeekAgoStoreRanking = oneWeekAgoRanking.Select((r, index) => new StoreRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalRevenue = r.TotalRevenue,
                    TotalOrders = r.TotalOrders,
                    AverageROAS = r.AverageROAS,
                    Rank = index + 1
                }).ToList(),
                TwoWeeksAgoStoreRanking = twoWeeksAgoRanking.Select((r, index) => new StoreRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalRevenue = r.TotalRevenue,
                    TotalOrders = r.TotalOrders,
                    AverageROAS = r.AverageROAS,
                    Rank = index + 1
                }).ToList(),
                CurrentWeekStoreCostRanking = currentWeekCostRanking.Select((r, index) => new StoreCostRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalCost = r.TotalCost,
                    TotalOrders = r.TotalOrders,
                    AverageCPA = r.AverageROAS, // Sử dụng AverageROAS thay vì AverageCPA
                    Rank = index + 1
                }).ToList(),
                OneWeekAgoStoreCostRanking = oneWeekAgoCostRanking.Select((r, index) => new StoreCostRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalCost = r.TotalCost,
                    TotalOrders = r.TotalOrders,
                    AverageCPA = r.AverageROAS, // Sử dụng AverageROAS thay vì AverageCPA
                    Rank = index + 1
                }).ToList(),
                TwoWeeksAgoStoreCostRanking = twoWeeksAgoCostRanking.Select((r, index) => new StoreCostRankingDto
                {
                    StoreId = r.StoreId,
                    StoreName = r.StoreName ?? $"Store {r.StoreId}",
                    TotalCost = r.TotalCost,
                    TotalOrders = r.TotalOrders,
                    AverageCPA = r.AverageROAS, // Sử dụng AverageROAS thay vì AverageCPA
                    Rank = index + 1
                }).ToList(),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

    }
}
