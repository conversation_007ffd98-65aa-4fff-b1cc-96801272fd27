using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Enums;
using TikTok.Domain.Repositories;
using TikTok.Entities;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using TikTok.Permissions;

namespace TikTok.Application.SystemNotification
{
    /// <summary>
    /// Universal trigger service để orchestrate system notification rule evaluation
    /// Hỗ trợ cả standard rules và cross-entity rules với single entry point
    /// </summary>
    public class SystemNotificationTriggerService : ISystemNotificationTriggerService
    {
        private readonly IRepository<SystemNotificationRule, Guid> _ruleRepository;
        private readonly SystemRuleEngine _systemRuleEngine;
        private readonly SystemTemplateRenderer _systemTemplateRenderer;
        private readonly ITikTokNotificationService _tikTokNotificationService;
        private readonly ILogger<SystemNotificationTriggerService> _logger;
        private readonly ISystemNotificationRuleConsumerRepository _consumerRepository;
        private readonly ISystemNotificationRecipientResolver _recipientResolver;
        private readonly IRepository<RawAdAccountEntity, Guid> _rawAdAccountRepository;
        private readonly IAdAccountResourceProvider _adAccountResourceProvider;
        private readonly IMemoryCache _cache;

        public SystemNotificationTriggerService(
            IRepository<SystemNotificationRule, Guid> ruleRepository,
            SystemRuleEngine systemRuleEngine,
            SystemTemplateRenderer systemTemplateRenderer,
            ITikTokNotificationService tikTokNotificationService,
            ILogger<SystemNotificationTriggerService> logger,
            ISystemNotificationRuleConsumerRepository consumerRepository,
            ISystemNotificationRecipientResolver recipientResolver,
            IRepository<RawAdAccountEntity, Guid> rawAdAccountRepository,
            IAdAccountResourceProvider adAccountResourceProvider,
            IMemoryCache cache)
        {
            _ruleRepository = ruleRepository;
            _systemRuleEngine = systemRuleEngine;
            _systemTemplateRenderer = systemTemplateRenderer;
            _tikTokNotificationService = tikTokNotificationService;
            _logger = logger;
            _consumerRepository = consumerRepository;
            _recipientResolver = recipientResolver;
            _rawAdAccountRepository = rawAdAccountRepository;
            _adAccountResourceProvider = adAccountResourceProvider;
            _cache = cache;
        }

        /// <summary>
        /// Universal trigger method - single entry point cho tất cả entity types
        /// SIMPLIFIED: All notifications now default to Campaign level
        /// </summary>
        public async Task TriggerRuleCheck<T>(
            IEnumerable<T> entities, 
            string entityType)
        {
            if (entities == null || !entities.Any())
                return;

            try
            {
                // Get all active rules for this entity type
                var rules = await GetActiveRulesForEntityType(entityType);
                if (!rules.Any())
                    return;

                // Process all rules and group notifications by Campaign
                var campaignNotifications = await ProcessRulesWithCampaignGrouping(entities, entityType, rules);

                // Send all notifications
                await SendNotifications(campaignNotifications);

                // Update rule cooldowns
                await UpdateRuleCooldowns(campaignNotifications);
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking calling services
                _logger.LogError(ex, "Error in SystemNotificationTriggerService: {Message}", ex.Message);
            }
        }

        public Task TriggerRuleCheck<T>(IEnumerable<T> entities, SystemNotificationEntityType entityType)
        {
            return TriggerRuleCheck(entities, entityType.ToCode());
        }

        /// <summary>
        /// Process all rules and group notifications by Campaign level
        /// Supports default rules that apply to all entities automatically
        /// </summary>
        private async Task<List<SystemNotificationRequest>> ProcessRulesWithCampaignGrouping<T>(
            IEnumerable<T> entities, 
            string entityType, 
            List<SystemNotificationRule> rules)
        {
            var allNotifications = new List<SystemNotificationRequest>();
            var recipientsCache = new Dictionary<Guid, List<string>>();

            foreach (var rule in rules.Where(r => r.IsActive))
            {
                List<T> targetEntities;
                
                if (rule.IsDefault)
                {
                    targetEntities = entities.ToList();
                    _logger.LogDebug("Processing default rule {RuleId} for {Count} entities", 
                        rule.Id, targetEntities.Count);
                }
                else
                {
                    targetEntities = await GetEntitiesWithConsumers(entities, rule.Id);
                    _logger.LogDebug("Processing non-default rule {RuleId} for {Count} entities with consumers", 
                        rule.Id, targetEntities.Count);
                }

                var matchingEntities = targetEntities
                    .Where(entity => _systemRuleEngine.EvaluateRule(entity, rule))
                    .ToList();
                
                if (matchingEntities.Any())
                {
                    _logger.LogInformation("Rule {RuleId} matched {Count} entities", 
                        rule.Id, matchingEntities.Count);

                    var campaignGroups = GroupEntitiesByCampaign(matchingEntities, entityType);
                    
                    foreach (var campaignGroup in campaignGroups)
                    {
                        var notification = await BuildCampaignLevelNotification(
                            rule, 
                            campaignGroup.Key, 
                            campaignGroup.Value.Cast<object>().ToList());
                        
                        if (!recipientsCache.TryGetValue(rule.Id, out var recipients))
                        {
                            if (rule.IsDefault)
                            {
                                recipients = await GetDefaultRuleRecipients(rule, entityType);
                                _logger.LogDebug("Resolved {Count} recipients for default rule {RuleId}", 
                                    recipients.Count, rule.Id);
                            }
                            else
                            {
                                recipients = await _recipientResolver.ResolveRecipientsForRuleAsync(rule);
                                _logger.LogDebug("Resolved {Count} recipients for non-default rule {RuleId}", 
                                    recipients.Count, rule.Id);
                            }
                            
                            recipients = recipients?.Distinct().ToList() ?? new List<string>();
                            recipientsCache[rule.Id] = recipients;
                        }
                        
                        notification.Recipients = recipients;
                        allNotifications.Add(notification);
                    }
                }
            }

            return allNotifications;
        }

        /// <summary>
        /// Group entities by CampaignId for campaign-level notifications
        /// </summary>
        private Dictionary<string, List<T>> GroupEntitiesByCampaign<T>(IEnumerable<T> entities, string entityType)
        {
            var campaignGroups = new Dictionary<string, List<T>>();

            foreach (var entity in entities)
            {
                string campaignId;
                
                if (entityType == SystemNotificationEntityType.RawGmvMaxProductCreativeReportEntity.ToCode())
                {
                    // For Product Creative entities, group by CampaignId
                    campaignId = _systemRuleEngine.GetPropertyValue(entity, "CampaignId")?.ToString() ?? "Unknown";
                }
                else if (entityType == "RawGmvMaxProductCampaignReportEntity")
                {
                    // For Product Campaign entities, group by CampaignId
                    campaignId = _systemRuleEngine.GetPropertyValue(entity, "CampaignId")?.ToString() ?? "Unknown";
                }
                else if (entityType == "RawGmvMaxLiveCampaignReportEntity")
                {
                    // For Live Campaign entities, group by CampaignId
                    campaignId = _systemRuleEngine.GetPropertyValue(entity, "CampaignId")?.ToString() ?? "Unknown";
                }
                else
                {
                    // Fallback for other entity types
                    campaignId = _systemRuleEngine.GetPropertyValue(entity, "CampaignId")?.ToString() ?? "Unknown";
                }

                if (!campaignGroups.ContainsKey(campaignId))
                    campaignGroups[campaignId] = new List<T>();
                
                campaignGroups[campaignId].Add(entity);
            }

            return campaignGroups;
        }

        /// <summary>
        /// Build notification for Campaign level
        /// </summary>
        private Task<SystemNotificationRequest> BuildCampaignLevelNotification(
            SystemNotificationRule rule, 
            string campaignId, 
            List<object> entities)
        {
            // Build user-friendly VN content based on level (Product vs Campaign)
            var rawRule = rule.RuleName ?? string.Empty;
            var normalizedRule = rawRule.StartsWith("Thông báo ", StringComparison.OrdinalIgnoreCase)
                ? rawRule.Substring("Thông báo ".Length)
                : rawRule;

            string title;
            string message;

            if (string.Equals(rule.EntityType, SystemNotificationEntityType.RawGmvMaxProductCreativeReportEntity.ToCode(), StringComparison.OrdinalIgnoreCase))
            {
                // Level = Product Creative
                var quantity = entities?.Count ?? 0;
                title = $"[Creatives] Campaign {campaignId} - {quantity} {normalizedRule}";
                message = $"[Creatives] Campaign {campaignId} đang có {quantity} {normalizedRule}. Vui lòng kiểm tra và xử lý.";
            }
            else if (string.Equals(rule.EntityType, "RawGmvMaxProductCampaignReportEntity", StringComparison.OrdinalIgnoreCase))
            {
                // Level = Product Campaign
                title = $"[Product] Campaign {campaignId} - {normalizedRule}";
                message = $"[Product] Campaign {campaignId} đang có {normalizedRule}. Vui lòng kiểm tra và xử lý.";
            }
            else if (string.Equals(rule.EntityType, "RawGmvMaxLiveCampaignReportEntity", StringComparison.OrdinalIgnoreCase))
            {
                // Level = Live Campaign
                title = $"[Live] Campaign {campaignId} - {normalizedRule}";
                message = $"[Live] Campaign {campaignId} đang có {normalizedRule}. Vui lòng kiểm tra và xử lý.";
            }
            else
            {
                // Level = Campaign (or others default to campaign-level message)
                title = $"[Campaign] Campaign {campaignId} - {normalizedRule}";
                message = $"[Campaign] Campaign {campaignId} đang có {normalizedRule}. Vui lòng kiểm tra và xử lý.";
            }

            return Task.FromResult(new SystemNotificationRequest
            {
                Title = title,
                Message = message,
                EntityId = campaignId,
                EntityType =rule.EntityType,
                RuleId = rule.Id,
                RuleName = rule.RuleName ?? string.Empty,
                Data = new Dictionary<string, object>
                {
                    ["CampaignId"] = campaignId,
                    ["RuleId"] = rule.Id,
                    ["AffectedCount"] = entities?.Count ?? 0,
                    ["EntityType"] = rule.EntityType ?? string.Empty
                }
            });
        }


        /// <summary>
        /// Get active rules for specific entity type
        /// </summary>
        private async Task<List<SystemNotificationRule>> GetActiveRulesForEntityType(string entityType)
        {
            var allRules = await _ruleRepository.GetListAsync();
            return allRules
                .Where(r => r.IsActive && r.EntityType == entityType)
                .OrderBy(r => r.CreationTime)
                .ToList();
        }

        /// <summary>
        /// Send notifications using existing notification service
        /// </summary>
        private async Task SendNotifications(List<SystemNotificationRequest> notifications)
        {
            foreach (var notification in notifications)
            {
                try
                {
                    await SendSystemNotification(notification);
                }
                catch (Exception ex)
                {
                    // Log individual notification errors but continue with others
                    _logger.LogError(ex, "Error sending notification {RuleId}: {Message}", notification.RuleId, ex.Message);
                }
            }
        }

        /// <summary>
        /// Update rule cooldowns after successful notifications
        /// </summary>
        private async Task UpdateRuleCooldowns(List<SystemNotificationRequest> notifications)
        {
            var ruleIds = notifications.Select(n => n.RuleId).Distinct().ToList();
            
            foreach (var ruleId in ruleIds)
            {
                try
                {
                    var rule = await _ruleRepository.GetAsync(ruleId);
                    rule.LastTriggeredAt = DateTime.UtcNow;
                    rule.TriggerCount++;
                    await _ruleRepository.UpdateAsync(rule);
                }
                catch (Exception ex)
                {
                    // Log error but don't throw
                    _logger.LogError(ex, "Error updating cooldown for rule {RuleId}: {Message}", ruleId, ex.Message);
                }
            }
        }

        /// <summary>
        /// Convert SystemNotificationRequest to existing notification format
        /// This method bridges the gap between new system notifications and existing notification service
        /// </summary>
        private async Task SendSystemNotification(SystemNotificationRequest notification)
        {
            try
            {
                if (notification.Recipients == null || notification.Recipients.Count == 0)
                {
                    _logger.LogWarning("System notification skipped due to empty recipients. RuleId={RuleId} EntityId={EntityId}", notification.RuleId, notification.EntityId);
                    return;
                }
                // Convert recipients to TikTokNotificationUserDto format
                var users = new List<TikTokNotificationUserDto>();
                foreach (var recipientId in notification.Recipients)
                {
                    var user = new TikTokNotificationUserDto
                    {
                        UserId = recipientId,
                        UserType = TikTok.Enums.TikTokUserType.ADVERTISER
                    };
                    if (!string.IsNullOrWhiteSpace(notification.Title))
                    {
                        user.Metadata["Title"] = notification.Title;
                    }
                    if (!string.IsNullOrWhiteSpace(notification.Message))
                    {
                        user.Metadata["Message"] = notification.Message;
                    }
                    if (!string.IsNullOrWhiteSpace(notification.RedirectUrl))
                    {
                        user.Metadata["RedirectUrl"] = notification.RedirectUrl;
                    }
                    // Include contextual metadata for downstream consumers
                    if (!string.IsNullOrWhiteSpace(notification.EntityType))
                    {
                        user.Metadata["EntityType"] = notification.EntityType;
                    }
                    if (!string.IsNullOrWhiteSpace(notification.EntityId))
                    {
                        user.Metadata["CampaignId"] = notification.EntityId;
                    }
                    if (notification.RuleId != Guid.Empty)
                    {
                        user.Metadata["RuleId"] = notification.RuleId;
                    }
                    if (notification.Data != null && notification.Data.TryGetValue("AffectedCount", out var affected))
                    {
                        user.Metadata["AffectedCount"] = affected ?? 0;
                    }
                    users.Add(user);
                }

                // Send notification using existing service with SystemNotificationBuildProvider
                // Prefer metadata via context if supported by the service overload
                await _tikTokNotificationService.SendNotificationToUsersAsync(
                    notification.EntityId,
                    "SystemNotification",
                    users);
            }
            catch (Exception ex)
            {
                // Log error but don't throw
                _logger.LogError(ex, "Error sending system notification: {Message}", ex.Message);
            }
        }


        #region Helper Methods for Default Rules

        /// <summary>
        /// Get all ad accounts for entity type
        /// </summary>
        private async Task<List<RawAdAccountEntity>> GetAllAdAccountsForEntityType(string entityType)
        {
            try
            {
                var allAdAccounts = await _rawAdAccountRepository.GetListAsync();
                _logger.LogDebug("Retrieved {Count} ad accounts for entity type {EntityType}", 
                    allAdAccounts.Count, entityType);
                return allAdAccounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ad accounts for entity type {EntityType}", entityType);
                return new List<RawAdAccountEntity>();
            }
        }

        /// <summary>
        /// Get recipients for default rules with caching
        /// Filters users by ReceiveNotification permission
        /// </summary>
        private async Task<List<string>> GetDefaultRuleRecipients(SystemNotificationRule rule, string entityType)
        {
            var cacheKey = $"default_rule_recipients_{rule.Id}_{entityType}";
            
            if (_cache.TryGetValue<List<string>>(cacheKey, out var cachedRecipients) && cachedRecipients != null)
            {
                _logger.LogDebug("Retrieved {Count} recipients from cache for default rule {RuleId}", 
                    cachedRecipients.Count, rule.Id);
                return cachedRecipients;
            }

            var recipients = new List<string>();
            
            try
            {
                _logger.LogInformation("Resolving recipients for default rule {RuleId} - {RuleName}", 
                    rule.Id, rule.RuleName);

                var allAdAccounts = await GetAllAdAccountsForEntityType(entityType);
                var advertiserIds = allAdAccounts
                    .Select(a => a.AdvertiserId)
                    .Where(id => !string.IsNullOrWhiteSpace(id))
                    .Distinct()
                    .ToList();

                if (advertiserIds.Any())
                {
                    _logger.LogDebug("Found {Count} advertiser IDs for default rule", advertiserIds.Count);

                    var assignPerms = await _adAccountResourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto
                    {
                        ResourceIds = advertiserIds,
                        Permissions = new List<string>
                        {
                            TikTokPermissions.AdAccounts.Default,
                            TikTokPermissions.AdAccounts.Edit
                        }
                    });

                    recipients = assignPerms
                        .Select(p => p.UserId.ToString())
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Distinct()
                        .ToList();

                    _logger.LogInformation("Found {Count} users with ad account permissions", recipients.Count);

                    // Filter by ReceiveNotification permission using RecipientResolver
                    recipients = await _recipientResolver.FilterByNotificationPermissionAsync(recipients);
                    
                    _logger.LogInformation("Resolved {Count} recipients for default rule {RuleId} after permission filtering", 
                        recipients.Count, rule.Id);
                }

                if (!recipients.Any() && rule.CreatorId.HasValue)
                {
                    _logger.LogWarning("No recipients found for default rule {RuleId}, using creator as fallback", 
                        rule.Id);
                    
                    var creatorId = rule.CreatorId.Value.ToString();
                    var creatorFiltered = await _recipientResolver.FilterByNotificationPermissionAsync(new List<string> { creatorId });
                    recipients.AddRange(creatorFiltered);
                }

                _cache.Set(cacheKey, recipients, TimeSpan.FromMinutes(15));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving recipients for default rule {RuleId}", rule.Id);
                
                if (!recipients.Any() && rule.CreatorId.HasValue)
                {
                    var creatorId = rule.CreatorId.Value.ToString();
                    var creatorFiltered = await _recipientResolver.FilterByNotificationPermissionAsync(new List<string> { creatorId });
                    recipients.AddRange(creatorFiltered);
                }
            }

            return recipients;
        }

        /// <summary>
        /// Get entities with consumers for non-default rules
        /// </summary>
        private async Task<List<T>> GetEntitiesWithConsumers<T>(IEnumerable<T> entities, Guid ruleId)
        {
            try
            {
                var consumers = await _consumerRepository.GetConsumersByRuleIdAsync(ruleId);
                
                if (!consumers.Any())
                {
                    _logger.LogDebug("No consumers found for rule {RuleId}, returning empty list", ruleId);
                    return new List<T>();
                }

                _logger.LogDebug("Found {Count} consumers for rule {RuleId}, returning all entities", 
                    consumers.Count, ruleId);
                
                return entities.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting entities with consumers for rule {RuleId}", ruleId);
                return new List<T>();
            }
        }

        #endregion

        #region Convenience Methods for Specific Entity Types

        /// <summary>
        /// Trigger rules for GMV Max Campaign entities
        /// </summary>
        public async Task TriggerCampaignRuleCheck(IEnumerable<object> campaigns)
        {
            await TriggerRuleCheck(campaigns, "FactGmvMaxCampaignEntity");
        }

        /// <summary>
        /// Trigger rules for GMV Max Product entities
        /// </summary>
        public async Task TriggerProductRuleCheck(IEnumerable<object> products)
        {
            await TriggerRuleCheck(products, "FactGmvMaxProductEntity");
        }

        /// <summary>
        /// Trigger rules for Product Creative entities
        /// </summary>
        public async Task TriggerProductCreativeRuleCheck(IEnumerable<object> productCreatives)
        {
            await TriggerRuleCheck(productCreatives, "RawGmvMaxProductCreativeReportEntity");
        }

        /// <summary>
        /// Trigger rules for Product Campaign entities
        /// </summary>
        public async Task TriggerProductCampaignRuleCheck(IEnumerable<object> productCampaigns)
        {
            await TriggerRuleCheck(productCampaigns, "RawGmvMaxProductCampaignReportEntity");
        }

        /// <summary>
        /// Trigger rules for Live Campaign entities
        /// </summary>
        public async Task TriggerLiveCampaignRuleCheck(IEnumerable<object> liveCampaigns)
        {
            await TriggerRuleCheck(liveCampaigns, "RawGmvMaxLiveCampaignReportEntity");
        }

        /// <summary>
        /// Bulk trigger for multiple entity types
        /// </summary>
        public async Task TriggerBulkRuleCheck(Dictionary<string, IEnumerable<object>> entitiesByType)
        {
            var tasks = entitiesByType.Select(kvp => 
                TriggerRuleCheck(kvp.Value, kvp.Key));
            
            await Task.WhenAll(tasks);
        }

        #endregion

        #region Integration Helper Methods

        /// <summary>
        /// Helper method để integrate với data sync services
        /// </summary>
        public async Task OnDataSyncCompleted(string entityType, IEnumerable<object> syncedEntities)
        {
            await TriggerRuleCheck(syncedEntities, entityType);
        }

        /// <summary>
        /// Helper method để integrate với dashboard updates
        /// </summary>
        public async Task OnDashboardDataUpdated(string entityType, IEnumerable<object> updatedEntities)
        {
            await TriggerRuleCheck(updatedEntities, entityType);
        }

        /// <summary>
        /// Helper method để integrate với campaign updates
        /// </summary>
        public async Task OnCampaignUpdated(object campaign)
        {
            await TriggerRuleCheck(new[] { campaign }, "FactGmvMaxCampaignEntity");
        }

        /// <summary>
        /// Helper method để integrate với product updates
        /// </summary>
        public async Task OnProductUpdated(object product)
        {
            await TriggerRuleCheck(new[] { product }, "FactGmvMaxProductEntity");
        }

        /// <summary>
        /// Helper method để integrate với product campaign updates
        /// </summary>
        public async Task OnProductCampaignUpdated(object productCampaign)
        {
            await TriggerRuleCheck(new[] { productCampaign }, "RawGmvMaxProductCampaignReportEntity");
        }

        /// <summary>
        /// Helper method để integrate với live campaign updates
        /// </summary>
        public async Task OnLiveCampaignUpdated(object liveCampaign)
        {
            await TriggerRuleCheck(new[] { liveCampaign }, "RawGmvMaxLiveCampaignReportEntity");
        }

        #endregion
    }
}
