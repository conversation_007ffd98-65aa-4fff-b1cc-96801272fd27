﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TikTok.Migrations
{
    /// <inheritdoc />
    public partial class AddFactGmvMaxProductCreative : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Dim_DimTTAccounts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TtAccountId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    TtAccountName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TtAccountProfileImageUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TtAccountAuthorizationType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    IsCurrent = table.Column<bool>(type: "bit", nullable: false),
                    RowVersion = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Dim_DimTTAccounts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Fact_FactGmvMaxProductCreatives",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DimDateId = table.Column<int>(type: "int", nullable: false),
                    DimBusinessCenterId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DimAdAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DimCampaignId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DimStoreId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DimTTAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DimProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CampaignId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    StoreId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    BcId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    AdvertiserId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ItemGroupId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ItemId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CreativeType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ShopContentType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CreativeDeliveryStatus = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CostVND = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CostUSD = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Orders = table.Column<int>(type: "int", nullable: false),
                    GrossRevenue = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    GrossRevenueVND = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    GrossRevenueUSD = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ProductImpressions = table.Column<long>(type: "bigint", nullable: true),
                    ProductClicks = table.Column<long>(type: "bigint", nullable: true),
                    ProductClickRate = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdClickRate = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdConversionRate = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdVideoViewRate2s = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdVideoViewRate6s = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdVideoViewRateP25 = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdVideoViewRateP50 = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdVideoViewRateP75 = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AdVideoViewRateP100 = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    ROAS = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    CostPerOrder = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CostPerOrderVND = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CostPerOrderUSD = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Fact_FactGmvMaxProductCreatives", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimAdAccounts_DimAdAccountId",
                        column: x => x.DimAdAccountId,
                        principalTable: "Dim_DimAdAccounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimBusinessCenters_DimBusinessCenterId",
                        column: x => x.DimBusinessCenterId,
                        principalTable: "Dim_DimBusinessCenters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimCampaigns_DimCampaignId",
                        column: x => x.DimCampaignId,
                        principalTable: "Dim_DimCampaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimDates_DimDateId",
                        column: x => x.DimDateId,
                        principalTable: "Dim_DimDates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimProducts_DimProductId",
                        column: x => x.DimProductId,
                        principalTable: "Dim_DimProducts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimStores_DimStoreId",
                        column: x => x.DimStoreId,
                        principalTable: "Dim_DimStores",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Fact_FactGmvMaxProductCreatives_Dim_DimTTAccounts_DimTTAccountId",
                        column: x => x.DimTTAccountId,
                        principalTable: "Dim_DimTTAccounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_AdvertiserId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "AdvertiserId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_CampaignId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_Date",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "Date");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimAdAccountId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimAdAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimBusinessCenterId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimBusinessCenterId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimCampaignId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimCampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimDateId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimDateId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimProductId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimStoreId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimStoreId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_DimTTAccountId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "DimTTAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_ItemGroupId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "ItemGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Fact_FactGmvMaxProductCreatives_StoreId",
                table: "Fact_FactGmvMaxProductCreatives",
                column: "StoreId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Fact_FactGmvMaxProductCreatives");

            migrationBuilder.DropTable(
                name: "Dim_DimTTAccounts");
        }
    }
}
