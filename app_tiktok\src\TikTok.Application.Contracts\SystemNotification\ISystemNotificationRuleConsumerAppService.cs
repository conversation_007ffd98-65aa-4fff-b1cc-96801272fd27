using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Application.Contracts.SystemNotification;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Interface cho SystemNotificationRuleConsumerAppService
    /// Quản lý consumers (Ad Accounts, Business Centers) cho System Notification Rules
    /// </summary>
    public interface ISystemNotificationRuleConsumerAppService
    {
        /// <summary>
        /// Lấy danh sách consumers của một rule
        /// </summary>
        /// <param name="ruleId">ID của rule</param>
        /// <returns>Danh sách consumers</returns>
        Task<SystemNotificationRuleConsumersResponse> GetRuleConsumersAsync(Guid ruleId);

        /// <summary>
        /// Thêm consumers vào rule
        /// </summary>
        /// <param name="input">Thông tin consumers cần thêm</param>
        /// <returns>Danh sách consumers đã thêm</returns>
        Task<List<SystemNotificationRuleConsumerDto>> AddConsumersToRuleAsync(AddSystemNotificationRuleConsumersDto input);

        /// <summary>
        /// Xóa consumers khỏi rule
        /// </summary>
        /// <param name="ruleId">ID của rule</param>
        /// <param name="consumerIds">Danh sách ID consumers cần xóa</param>
        Task RemoveConsumersFromRuleAsync(Guid ruleId, List<Guid> consumerIds);
    }
}
