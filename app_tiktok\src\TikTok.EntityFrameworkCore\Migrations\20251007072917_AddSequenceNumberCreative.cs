﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TikTok.Migrations
{
    /// <inheritdoc />
    public partial class AddSequenceNumberCreative : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SequenceNumber",
                table: "Raw_RawGmvMaxProductCreativeReports",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SequenceNumber",
                table: "Fact_FactGmvMaxProductCreatives",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SequenceNumber",
                table: "Raw_RawGmvMaxProductCreativeReports");

            migrationBuilder.DropColumn(
                name: "SequenceNumber",
                table: "Fact_FactGmvMaxProductCreatives");
        }
    }
}
