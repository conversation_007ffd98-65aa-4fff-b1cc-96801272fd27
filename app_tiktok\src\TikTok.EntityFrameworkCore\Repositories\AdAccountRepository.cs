using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Repository implementation cho tài khoản quảng cáo
    /// </summary>
    public class AdAccountRepository : EfCoreRepository<TikTokDbContext, RawAdAccountEntity, Guid>, IAdAccountRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="dbContextProvider">DbContext provider</param>
        public AdAccountRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        /// <summary>
        /// L<PERSON>y danh sách tài khoản quảng cáo theo Business Center ID
        /// </summary>
        /// <param name="ownerBcId">ID của Business Center</param>
        /// <param name="includeRemoved">Bao gồm cả tài khoản đã bị xóa</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        public async Task<List<RawAdAccountEntity>> GetByOwnerBcIdAsync(string ownerBcId, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.Where(x => x.OwnerBcId == ownerBcId);

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// Lấy tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <param name="includeRemoved">Bao gồm cả tài khoản đã bị xóa</param>
        /// <returns>Tài khoản quảng cáo</returns>
        public async Task<RawAdAccountEntity> GetByAdvertiserIdAsync(string advertiserId, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.Where(x => x.AdvertiserId == advertiserId);

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo với tìm kiếm và phân trang
        /// </summary>
        public async Task<List<RawAdAccountEntity>> GetListAsync(
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            string? filter = null,
            string? advertiserId = null,
            string? ownerBcId = null,
            string? name = null,
            string? company = null,
            AdAccountStatus? status = null,
            List<AdAccountStatus>? statuses = null,
            AdAccountRole? role = null,
            string? industry = null,
            string? address = null,
            string? country = null,
            AdAccountType? advertiserAccountType = null,
            string? currency = null,
            string? language = null,
            string? licenseNo = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? balanceFrom = null,
            decimal? balanceTo = null,
            bool? isRemoved = null,
            CancellationToken cancellationToken = default,
            List<string>? adAccountIds = null,
            List<string>? customers = null)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.AsQueryable();

            // Áp dụng các bộ lọc
            if (!string.IsNullOrWhiteSpace(filter))
            {
                query = query.Where(x =>
                    x.AdvertiserId.Contains(filter) ||
                    x.Name.Contains(filter) ||
                    x.Company.Contains(filter) ||
                    x.OwnerBcId.Contains(filter) ||
                    x.Industry.Contains(filter) ||
                    x.Address.Contains(filter) ||
                    x.Country.Contains(filter) ||
                    x.Currency.Contains(filter));
            }

            if (!string.IsNullOrWhiteSpace(advertiserId))
            {
                query = query.Where(x => x.AdvertiserId.Contains(advertiserId));
            }

            if (!string.IsNullOrWhiteSpace(ownerBcId))
            {
                query = query.Where(x => x.OwnerBcId.Contains(ownerBcId));
            }

            if (!string.IsNullOrWhiteSpace(name))
            {
                query = query.Where(x => x.Name.Contains(name));
            }

            if (!string.IsNullOrWhiteSpace(company))
            {
                query = query.Where(x => x.Company.Contains(company));
            }

            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            if (statuses != null && statuses.Any())
            {
                query = query.Where(x => statuses.Contains(x.Status));
            }

            if (role.HasValue)
            {
                query = query.Where(x => x.Role == role.Value);
            }

            if (!string.IsNullOrWhiteSpace(industry))
            {
                query = query.Where(x => x.Industry.Contains(industry));
            }

            if (!string.IsNullOrWhiteSpace(address))
            {
                query = query.Where(x => x.Address.Contains(address));
            }

            if (!string.IsNullOrWhiteSpace(country))
            {
                query = query.Where(x => x.Country.Contains(country));
            }

            if (advertiserAccountType.HasValue)
            {
                query = query.Where(x => x.AdvertiserAccountType == advertiserAccountType.Value);
            }

            if (!string.IsNullOrWhiteSpace(currency))
            {
                query = query.Where(x => x.Currency.Contains(currency));
            }

            if (!string.IsNullOrWhiteSpace(language))
            {
                query = query.Where(x => x.Language.Contains(language));
            }

            if (!string.IsNullOrWhiteSpace(licenseNo))
            {
                query = query.Where(x => x.LicenseNo.Contains(licenseNo));
            }

            if (createTimeFrom.HasValue)
            {
                query = query.Where(x => x.CreateTime >= createTimeFrom.Value);
            }

            if (createTimeTo.HasValue)
            {
                query = query.Where(x => x.CreateTime <= createTimeTo.Value);
            }

            if (balanceFrom.HasValue)
            {
                query = query.Where(x => x.Balance >= balanceFrom.Value);
            }

            if (balanceTo.HasValue)
            {
                query = query.Where(x => x.Balance <= balanceTo.Value);
            }

            if (isRemoved.HasValue)
            {
                query = query.Where(x => x.IsRemoved == isRemoved.Value);
            }
            else
            {
                // By default, exclude removed accounts
                query = query.Where(x => !x.IsRemoved);
            }

            if (adAccountIds != null)
            {
                query = query.Where(x => adAccountIds.Contains(x.AdvertiserId));
            }

            // Apply sorting
            if (!sorting.IsNullOrWhiteSpace())
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            // get customer
            if (customers != null)
            {
                var customerEntityIds = dbContext.Customers.Where(c => customers.Contains(c.CustomerId)).Select(c => c.Id);
                var validAdAccountIds = dbContext.CustomerAdAccounts.Where(c => customerEntityIds.Contains(c.CustomerId))
                    .Select(x => x.AdvertiserId);
                query = query.Where(x => validAdAccountIds.Contains(x.AdvertiserId));
            }
            return await query
                .GroupBy(x => new { x.OwnerBcId, x.AdvertiserId })
                .Select(g => g.OrderBy(x => x.CreationTime).First())
                .Skip(skipCount)
                .Take(maxResultCount)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy tổng số tài khoản quảng cáo với điều kiện tìm kiếm
        /// </summary>
        public async Task<long> GetCountAsync(
            string? filter = null,
            string? advertiserId = null,
            string? ownerBcId = null,
            string? name = null,
            string? company = null,
            AdAccountStatus? status = null,
            List<AdAccountStatus>? statuses = null,
            AdAccountRole? role = null,
            string? industry = null,
            string? address = null,
            string? country = null,
            AdAccountType? advertiserAccountType = null,
            string? currency = null,
            string? language = null,
            string? licenseNo = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? balanceFrom = null,
            decimal? balanceTo = null,
            bool? isRemoved = null,
            CancellationToken cancellationToken = default,
            List<string>? adAccountIds = null,
            List<string>? customerIds = null)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.AsQueryable();

            // Áp dụng các bộ lọc tương tự như GetListAsync
            if (!string.IsNullOrWhiteSpace(filter))
            {
                query = query.Where(x =>
                    x.AdvertiserId.Contains(filter) ||
                    x.Name.Contains(filter) ||
                    x.Company.Contains(filter) ||
                    x.OwnerBcId.Contains(filter) ||
                    x.Industry.Contains(filter) ||
                    x.Address.Contains(filter) ||
                    x.Country.Contains(filter) ||
                    x.Currency.Contains(filter));
            }

            if (!string.IsNullOrWhiteSpace(advertiserId))
            {
                query = query.Where(x => x.AdvertiserId.Contains(advertiserId));
            }

            if (!string.IsNullOrWhiteSpace(ownerBcId))
            {
                query = query.Where(x => x.OwnerBcId.Contains(ownerBcId));
            }

            if (!string.IsNullOrWhiteSpace(name))
            {
                query = query.Where(x => x.Name.Contains(name));
            }

            if (!string.IsNullOrWhiteSpace(company))
            {
                query = query.Where(x => x.Company.Contains(company));
            }

            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            if (statuses != null && statuses.Any())
            {
                query = query.Where(x => statuses.Contains(x.Status));
            }

            if (role.HasValue)
            {
                query = query.Where(x => x.Role == role.Value);
            }

            if (!string.IsNullOrWhiteSpace(industry))
            {
                query = query.Where(x => x.Industry.Contains(industry));
            }

            if (!string.IsNullOrWhiteSpace(address))
            {
                query = query.Where(x => x.Address.Contains(address));
            }

            if (!string.IsNullOrWhiteSpace(country))
            {
                query = query.Where(x => x.Country.Contains(country));
            }

            if (advertiserAccountType.HasValue)
            {
                query = query.Where(x => x.AdvertiserAccountType == advertiserAccountType.Value);
            }

            if (!string.IsNullOrWhiteSpace(currency))
            {
                query = query.Where(x => x.Currency.Contains(currency));
            }

            if (!string.IsNullOrWhiteSpace(language))
            {
                query = query.Where(x => x.Language.Contains(language));
            }

            if (!string.IsNullOrWhiteSpace(licenseNo))
            {
                query = query.Where(x => x.LicenseNo.Contains(licenseNo));
            }

            if (createTimeFrom.HasValue)
            {
                query = query.Where(x => x.CreateTime >= createTimeFrom.Value);
            }

            if (createTimeTo.HasValue)
            {
                query = query.Where(x => x.CreateTime <= createTimeTo.Value);
            }

            if (balanceFrom.HasValue)
            {
                query = query.Where(x => x.Balance >= balanceFrom.Value);
            }

            if (balanceTo.HasValue)
            {
                query = query.Where(x => x.Balance <= balanceTo.Value);
            }
            // currentUserId
            if (adAccountIds != null && adAccountIds.Any())
            {
                query = query.Where(x => adAccountIds.Contains(x.AdvertiserId));
            }
            if (isRemoved.HasValue)
            {
                query = query.Where(x => x.IsRemoved == isRemoved.Value);
            }
            else
            {
                // By default, exclude removed accounts
                query = query.Where(x => !x.IsRemoved);
            }

            // get customer
            if (customerIds != null)
            {
                var customerEntityIds = dbContext.Customers.Where(c => customerIds.Contains(c.CustomerId)).Select(c => c.Id);
                var validAdAccountIds = dbContext.CustomerAdAccounts.Where(c => customerEntityIds.Contains(c.Id)).Select(x => x.AdvertiserId);
                query = query.Where(x => validAdAccountIds.Contains(x.AdvertiserId));
            }

            return await query.LongCountAsync(cancellationToken);
        }

        /// <summary>
        /// Kiểm tra Advertiser ID đã tồn tại chưa
        /// </summary>
        /// <param name="advertiserId">Advertiser ID cần kiểm tra</param>
        /// <param name="excludeId">ID cần loại trừ (cho update)</param>
        /// <param name="includeRemoved">Bao gồm cả tài khoản đã bị xóa</param>
        /// <returns>True nếu đã tồn tại</returns>
        public async Task<bool> IsAdvertiserIdExistsAsync(string advertiserId, Guid? excludeId = null, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.Where(x => x.AdvertiserId == advertiserId);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Đánh dấu tài khoản quảng cáo đã bị xóa khỏi Business Center
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <param name="removedAt">Thời gian xóa</param>
        /// <returns>True nếu thành công</returns>
        public async Task<bool> MarkAsRemovedAsync(string advertiserId, DateTime? removedAt = null)
        {
            var dbContext = await GetDbContextAsync();
            var account = await dbContext.RawAdAccounts
                .FirstOrDefaultAsync(x => x.AdvertiserId == advertiserId && !x.IsRemoved);

            if (account == null)
            {
                return false;
            }

            account.IsRemoved = true;
            account.RemovedAt = removedAt ?? DateTime.UtcNow;

            await dbContext.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Khôi phục tài khoản quảng cáo đã bị xóa
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>True nếu thành công</returns>
        public async Task<bool> RestoreAsync(string advertiserId)
        {
            var dbContext = await GetDbContextAsync();
            var account = await dbContext.RawAdAccounts
                .FirstOrDefaultAsync(x => x.AdvertiserId == advertiserId && x.IsRemoved);

            if (account == null)
            {
                return false;
            }

            account.IsRemoved = false;
            account.RemovedAt = null;

            await dbContext.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Lấy danh sách ID tài khoản quảng cáo theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="includeRemoved">Bao gồm cả tài khoản đã bị xóa</param>
        /// <returns>Danh sách ID tài khoản quảng cáo</returns>
        public async Task<List<string>> GetByBcIdAsync(string bcId, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.Where(x => x.OwnerBcId == bcId);

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.Select(x => x.AdvertiserId).ToListAsync();
        }

        public async Task<List<string>> GetByBcIdAndStatusAsync(string bcId,AdAccountStatus status, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.Where(x => x.OwnerBcId == bcId&&x.Status==status);

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.Select(x => x.AdvertiserId).ToListAsync();
        }

        public async Task<List<RawAdAccountEntity>> GetUserAdAccountsAsync(List<Guid> supporterIds)
        {
            var dbContext = await GetDbContextAsync();

            return await dbContext.RawAdAccounts
                .Include(x => x.Supporters)
                .Where(x => x.Supporters.Any(s => s.IsActive && supporterIds.Contains(s.SupporterId)))
                .ToListAsync();
        }

        public async Task<RawAdAccountEntity> GetUserAdAccountAsync(string advertiserId, List<Guid> supporterIds)
        {
            var dbContext = await GetDbContextAsync();

            return await dbContext.RawAdAccounts
                .Include(x => x.Supporters)
                .FirstOrDefaultAsync(x =>
                    x.AdvertiserId == advertiserId &&
                    x.Supporters.Any(s => s.IsActive && supporterIds.Contains(s.SupporterId)));
        }

        public async Task<List<RawAdAccountEntity>> GetAllAdAccountsAsync()
        {
            var dbContext = await GetDbContextAsync();

            return await dbContext.RawAdAccounts
                .Include(x => x.Supporters)
                .ToListAsync();
        }
        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo theo danh sách Advertiser IDs
        /// </summary>
        /// <param name="advertiserIds">Danh sách Advertiser IDs</param>
        /// <param name="includeRemoved">Bao gồm cả tài khoản đã bị xóa</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        public async Task<List<RawAdAccountEntity>> GetListByAdvertiserIdsAsync(List<string> advertiserIds, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAdAccounts.Where(x => advertiserIds.Contains(x.AdvertiserId));

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.ToListAsync();
        }

    }
}