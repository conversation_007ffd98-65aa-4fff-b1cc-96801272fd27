﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TikTok.Migrations
{
    /// <inheritdoc />
    public partial class AddSystemNotificationRule : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SystemNotificationRule",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RuleName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    EntityType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ConditionsJson = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    LastTriggeredAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TriggerCount = table.Column<long>(type: "bigint", nullable: false),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false),
                    IsPublic = table.Column<bool>(type: "bit", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemNotificationRule", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SystemNotificationRuleConsumer",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SystemNotificationRuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BcId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AdAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ConsumerType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ConsumerName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ConsumerId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemNotificationRuleConsumer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SystemNotificationRuleConsumer_SystemNotificationRule_SystemNotificationRuleId",
                        column: x => x.SystemNotificationRuleId,
                        principalTable: "SystemNotificationRule",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_CreationTime",
                table: "SystemNotificationRule",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_CreatorId",
                table: "SystemNotificationRule",
                column: "CreatorId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_CreatorId_IsActive",
                table: "SystemNotificationRule",
                columns: new[] { "CreatorId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_EntityType",
                table: "SystemNotificationRule",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_EntityType_IsActive",
                table: "SystemNotificationRule",
                columns: new[] { "EntityType", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_IsActive",
                table: "SystemNotificationRule",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRule_LastTriggeredAt",
                table: "SystemNotificationRule",
                column: "LastTriggeredAt");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRuleConsumer_AdAccountId",
                table: "SystemNotificationRuleConsumer",
                column: "AdAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRuleConsumer_BcId",
                table: "SystemNotificationRuleConsumer",
                column: "BcId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRuleConsumer_BcId_AdAccountId",
                table: "SystemNotificationRuleConsumer",
                columns: new[] { "BcId", "AdAccountId" });

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRuleConsumer_ConsumerType",
                table: "SystemNotificationRuleConsumer",
                column: "ConsumerType");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRuleConsumer_SystemNotificationRuleId",
                table: "SystemNotificationRuleConsumer",
                column: "SystemNotificationRuleId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemNotificationRuleConsumer_SystemNotificationRuleId_ConsumerType",
                table: "SystemNotificationRuleConsumer",
                columns: new[] { "SystemNotificationRuleId", "ConsumerType" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SystemNotificationRuleConsumer");

            migrationBuilder.DropTable(
                name: "SystemNotificationRule");
        }
    }
}
