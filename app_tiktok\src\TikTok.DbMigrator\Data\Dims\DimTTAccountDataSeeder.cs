using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Data;
using TikTok.DbMigrator.Consts;
using TikTok.Entities.Dim;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace TikTok.DbMigrator.Data.Dims
{
    /// <summary>
    /// Data seeder cho DimTTAccount - TikTok Account dimension với SCD Type 2
    /// </summary>
    public class DimTTAccountDataSeeder : ICustomDataSeedContributor
    {
        private readonly IRepository<DimTTAccountEntity, Guid> _dimTTAccountRepository;
        private readonly IGuidGenerator _guidGenerator;
        private readonly ILogger<DimTTAccountDataSeeder> _logger;

        public int Order { get; set; } = 7; // Sau DimStore (Order = 6)

        public DimTTAccountDataSeeder(
            IRepository<DimTTAccountEntity, Guid> dimTTAccountRepository,
            IGuidGenerator guidGenerator,
            ILogger<DimTTAccountDataSeeder> logger)
        {
            _dimTTAccountRepository = dimTTAccountRepository;
            _guidGenerator = guidGenerator;
            _logger = logger;
        }

        public async Task RunSeedAsync(DataSeedContext context)
        {
            if (await _dimTTAccountRepository.AnyAsync())
            {
                _logger.LogDebug("DimTTAccount data already exists, skipping seeding.");
                return;
            }

            _logger.LogDebug("Starting DimTTAccount data seeding...");

            var dimTTAccountsToInsert = new List<DimTTAccountEntity>();

            // Generate sample TikTok accounts
            var sampleTTAccounts = GetSampleTTAccounts();

            foreach (var ttAccountInfo in sampleTTAccounts)
            {
                // Create current version (SCD Type 2)
                var currentDimTTAccount = CreateDimTTAccountEntity(ttAccountInfo, isCurrent: true);
                dimTTAccountsToInsert.Add(currentDimTTAccount);

                // Create historical version for some accounts (simulating SCD Type 2 changes)
                if (ShouldCreateHistoricalVersion(ttAccountInfo.TtAccountId))
                {
                    var historicalDimTTAccount = CreateHistoricalDimTTAccountEntity(ttAccountInfo);
                    dimTTAccountsToInsert.Add(historicalDimTTAccount);
                }
            }

            // Bulk insert for better performance
            await _dimTTAccountRepository.InsertManyAsync(dimTTAccountsToInsert);

            _logger.LogDebug($"Successfully seeded {dimTTAccountsToInsert.Count} DimTTAccount records.");
        }

        public Task SeedAsync(DataSeedContext context)
        {
            if (DbMigratorConst.IsEnableSeed)
            {
                return RunSeedAsync(context);
            }

            return Task.CompletedTask;
        }

        private DimTTAccountEntity CreateDimTTAccountEntity(TTAccountInfo ttAccountInfo, bool isCurrent = true)
        {
            var effectiveDate = isCurrent ? DateTime.UtcNow.AddDays(-30) : DateTime.UtcNow.AddDays(-60);
            var expiryDate = isCurrent ? (DateTime?)null : DateTime.UtcNow.AddDays(-31);

            return new DimTTAccountEntity
            {
                TtAccountId = ttAccountInfo.TtAccountId,
                TtAccountName = ttAccountInfo.TtAccountName,
                TtAccountProfileImageUrl = ttAccountInfo.TtAccountProfileImageUrl,
                TtAccountAuthorizationType = ttAccountInfo.TtAccountAuthorizationType,
                IsCurrent = isCurrent,
                RowVersion = isCurrent ? 1 : 0
            };
        }

        private DimTTAccountEntity CreateHistoricalDimTTAccountEntity(TTAccountInfo ttAccountInfo)
        {
            // Create a historical version with different values to simulate changes
            var historicalEntity = CreateDimTTAccountEntity(ttAccountInfo, isCurrent: false);

            // Modify some fields to simulate historical changes
            historicalEntity.TtAccountName = $"{ttAccountInfo.TtAccountName} (Old)";
            historicalEntity.TtAccountAuthorizationType = GetRandomHistoricalAuthorizationType();
            historicalEntity.RowVersion = 0;

            return historicalEntity;
        }

        private string GetRandomHistoricalAuthorizationType()
        {
            var historicalTypes = new[] { "TTS_TT", "AFFILIATE", "TT_USER" };
            var random = new Random();
            return historicalTypes[random.Next(historicalTypes.Length)];
        }

        private bool ShouldCreateHistoricalVersion(string ttAccountId)
        {
            // Create historical versions for some accounts to demonstrate SCD Type 2
            var accountsWithHistory = new[] { "TT001", "TT003", "TT005", "TT007" };
            return accountsWithHistory.Contains(ttAccountId);
        }

        private List<TTAccountInfo> GetSampleTTAccounts()
        {
            return new List<TTAccountInfo>
            {
                // Fashion & Beauty TikTok Accounts
                new TTAccountInfo
                {
                    TtAccountId = "TT001",
                    TtAccountName = "FashionHub_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/1234567890abcdef.jpg",
                    TtAccountAuthorizationType = "TTS_TT"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT002",
                    TtAccountName = "StyleStreet_Official",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/abcdef1234567890.jpg",
                    TtAccountAuthorizationType = "AFFILIATE"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT003",
                    TtAccountName = "LuxuryBoutique_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/9876543210fedcba.jpg",
                    TtAccountAuthorizationType = "BC_AUTH_TT"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT004",
                    TtAccountName = "BeautyParadise_Shop",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/fedcba0987654321.jpg",
                    TtAccountAuthorizationType = "TT_USER"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT005",
                    TtAccountName = "GlowShine_Beauty",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "AUTH_CODE"
                },

                // Electronics & Tech TikTok Accounts
                new TTAccountInfo
                {
                    TtAccountId = "TT006",
                    TtAccountName = "TechWorld_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "TTS_TT"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT007",
                    TtAccountName = "DigitalZone_Store",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "AFFILIATE"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT008",
                    TtAccountName = "GadgetHub_Official",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "BC_AUTH_TT"
                },

                // Food & Lifestyle TikTok Accounts
                new TTAccountInfo
                {
                    TtAccountId = "TT009",
                    TtAccountName = "FoodieCorner_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "TT_USER"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT010",
                    TtAccountName = "FreshMarket_Shop",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "AUTH_CODE"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT011",
                    TtAccountName = "HomeSweetHome_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "TTS_TT"
                },

                // Entertainment & Lifestyle TikTok Accounts
                new TTAccountInfo
                {
                    TtAccountId = "TT012",
                    TtAccountName = "EntertainmentHub_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "AFFILIATE"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT013",
                    TtAccountName = "LifestyleCorner_Shop",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "BC_AUTH_TT"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT014",
                    TtAccountName = "TrendyStore_Official",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "TT_USER"
                },
                new TTAccountInfo
                {
                    TtAccountId = "TT015",
                    TtAccountName = "CreativeShop_VN",
                    TtAccountProfileImageUrl = "https://p16-sign-va.tiktokcdn-us.com/obj/tos-useast2a-ve-2777/****************.jpg",
                    TtAccountAuthorizationType = "AUTH_CODE"
                },

                // Special cases for testing
                new TTAccountInfo
                {
                    TtAccountId = "0",
                    TtAccountName = "Unknown Account",
                    TtAccountProfileImageUrl = null,
                    TtAccountAuthorizationType = "UNSET"
                },
                new TTAccountInfo
                {
                    TtAccountId = "-1",
                    TtAccountName = "No Permission Account",
                    TtAccountProfileImageUrl = null,
                    TtAccountAuthorizationType = "UNSET"
                }
            };
        }

        private class TTAccountInfo
        {
            public string TtAccountId { get; set; }
            public string TtAccountName { get; set; }
            public string? TtAccountProfileImageUrl { get; set; }
            public string? TtAccountAuthorizationType { get; set; }
        }
    }
}
