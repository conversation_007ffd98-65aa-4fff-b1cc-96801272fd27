@page "~/SystemNotificationRules"
@using TikTok.Localization
@using TikTok.Web.Pages.SystemNotificationRules
@using TikTok.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Mvc.Rendering
@model IndexModel
@inject IStringLocalizer<TikTokResource> L
@inject IAuthorizationService AuthorizationService
@{
    ViewData["Title"] = L["SystemNotificationRules:Title"];
}

@section styles {
    <link rel="stylesheet" href="/css/rules/notificationRules.css" />
    <link rel="stylesheet" href="/css/systemNotifications/index.css" />
    <link href="https://cdn.syncfusion.com/ej2/24.2.9/material.css" rel="stylesheet">
    <script src="https://cdn.syncfusion.com/ej2/24.2.9/dist/ej2.min.js"></script>
    
}

@section scripts {
    <abp-script src="/js/syncfusionQueryBuilderLocalization.js" />
    <abp-script src="/js/systemNotificationRules/helper.js" />
    <abp-script src="/js/systemNotificationRules/operatorConfig.js" />
    <abp-script src="/js/systemNotificationRules/systemNotificationRulePermission.js" />
    
    <!-- Core modules - entityTypesConfig MUST load first -->
    <abp-script src="/js/systemNotificationRules/core/entityTypesConfig.js" />
    <abp-script src="/js/systemNotificationRules/core/constants.js" />
    <abp-script src="/js/systemNotificationRules/core/helpers.js" />
    <abp-script src="/js/systemNotificationRules/core/validation.js" />
    
    <!-- Services -->
    <abp-script src="/js/systemNotificationRules/services/apiService.js" />
    
    <!-- Components -->
    <abp-script src="/js/systemNotificationRules/components/table.js" />
    <abp-script src="/js/systemNotificationRules/components/queryBuilder.js" />
    <abp-script src="/js/systemNotificationRules/components/modals.js" />
    
    <!-- Consumers -->
    <abp-script src="/js/systemNotificationRules/consumers/consumersManager.js" />
    <abp-script src="/js/systemNotificationRules/consumers/adAccountSelector.js" />
    
    <script>
        // Pass localization keys to JavaScript - MUST be before Index.js
        window.systemNotificationRulesLocalization = {
            on: '@L["SystemNotificationRules:On"]',
            off: '@L["SystemNotificationRules:Off"]',
            targetEntity: '@L["SystemNotificationRules:TargetEntity"]',
            notificationFrequency: '@L["SystemNotificationRules:NotificationFrequency"]',
            loading: '@L["SystemNotificationRules:Loading"]',
            consumersTitle: '@Html.Raw(L["SystemNotificationRules:ConsumersTitle"])',
            adAccountsTab: '@L["SystemNotificationRules:AdAccountsTab"]',
            businessCentersTab: '@L["SystemNotificationRules:BusinessCentersTab"]',
            noAdAccounts: '@L["SystemNotificationRules:NoAdAccounts"]',
            noBusinessCenters: '@L["SystemNotificationRules:NoBusinessCenters"]',
            addAdAccounts: '@L["SystemNotificationRules:AddAdAccounts"]',
            selectAll: '@L["SystemNotificationRules:SelectAll"]',
            deleteSelected: '@L["SystemNotificationRules:DeleteSelected"]',
            close: '@L["SystemNotificationRules:Close"]',
            selectAdAccounts: '@L["SystemNotificationRules:SelectAdAccounts"]',
            searchAdAccounts: '@L["SystemNotificationRules:SearchAdAccounts"]',
            deselectAll: '@L["SystemNotificationRules:DeselectAll"]',
            addSelected: '@L["SystemNotificationRules:AddSelected"]',
            viewModeSimple: '@L["SystemNotificationRules:ViewModeSimple"]',
            viewModeDetail: '@L["SystemNotificationRules:ViewModeDetail"]',
            viewModeTable: '@L["SystemNotificationRules:ViewModeTable"]',
            deleteRuleConfirm: '@L["SystemNotificationRules:DeleteRuleConfirm"]',
            deleteConsumerConfirm: '@L["SystemNotificationRules:DeleteConsumerConfirm"]',
            deleteConsumerSuccess: '@L["SystemNotificationRules:DeleteConsumerSuccess"]',
            deleteConsumerError: '@L["SystemNotificationRules:DeleteConsumerError"]',
            loadConsumersError: '@L["SystemNotificationRules:LoadConsumersError"]',
            noAvailableAdAccounts: '@L["SystemNotificationRules:NoAvailableAdAccounts"]',
            selectAtLeastOneAdAccount: '@L["SystemNotificationRules:SelectAtLeastOneAdAccount"]',
            addAdAccountsSuccess: '@L["SystemNotificationRules:AddAdAccountsSuccess"]',
            addAdAccountsError: '@L["SystemNotificationRules:AddAdAccountsError"]',
            loadingAdAccounts: '@L["SystemNotificationRules:LoadingAdAccounts"]',
            select: '@L["SystemNotificationRules:Select"]',
            confirmDelete: '@L["SystemNotificationRules:ConfirmDelete"]',
            gmvMaxProductCreative: '@L["SystemNotificationRules:GMVMaxProductCreative"]',
            gmvMaxProductCampaign: '@L["SystemNotificationRules:GMVMaxProductCampaign"]',
            gmvMaxLiveCampaign: '@L["SystemNotificationRules:GMVMaxLiveCampaign"]',
            all: '@L["SystemNotificationRules:All"]',
            copy: '@L["SystemNotificationRules:Copy"]',
            notAvailable: '@L["SystemNotificationRules:NotAvailable"]',
            id: '@L["SystemNotificationRules:Id"]',
            bc: '@L["SystemNotificationRules:Bc"]',
            entityChangeWarning: '@L["SystemNotificationRules:EntityChangeWarning"]',
            entityChangeWarningTitle: '@L["SystemNotificationRules:EntityChangeWarningTitle"]',
            entityChangeWarningMessage: '@L["SystemNotificationRules:EntityChangeWarningMessage"]',
            entityChangeWarningDetail: '@L["SystemNotificationRules:EntityChangeWarningDetail"]',
            confirmChange: '@L["SystemNotificationRules:ConfirmChange"]',
            editRule: '@L["SystemNotificationRules:EditRule"]',
            copyRule: '@L["SystemNotificationRules:CopyRule"]',
            manageConsumers: '@L["SystemNotificationRules:ManageConsumers"]',
            toggleOn: '@L["SystemNotificationRules:ToggleOn"]',
            toggleOff: '@L["SystemNotificationRules:ToggleOff"]',
            deleteRule: '@L["SystemNotificationRules:DeleteRule"]',
            save: '@L["SystemNotificationRules:Save"]',
            createRule: '@L["SystemNotificationRules:CreateRule"]',
            yes: '@L["SystemNotificationRules:Yes"]',
            no: '@L["SystemNotificationRules:No"]',
            deleteRuleConfirmTitle: '@L["SystemNotificationRules:DeleteRuleConfirmTitle"]',
            deleteRuleConfirmMessage: '@L["SystemNotificationRules:DeleteRuleConfirmMessage"]',
            deleteRuleConfirmDetail: '@L["SystemNotificationRules:DeleteRuleConfirmDetail"]'
        };
    </script>
    
    <!-- Main application entry point -->
    <abp-script src="/js/systemNotificationRules/index.js" />
}

<abp-card>
    <abp-card-header>
        <abp-row>
            <abp-column size-md="_6">
                <abp-card-title>@L["SystemNotificationRules:HeroText"]</abp-card-title>
            </abp-column>
            <abp-column size-md="_6" class="text-end">
                @if (await AuthorizationService.IsGrantedAsync(TikTokPermissions.SystemNotificationRules.Create))
                {
                    <abp-button id="NewRuleButton" text="@L["SystemNotificationRules:NewRule"]" icon="plus" button-type="Primary" style="display: none;" />
                }
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <!-- Filter Controls -->
        <div class="row mb-3 row-gap-3">
            <div class="col-12 col-md-7">
                <label for="filterRuleName" class="form-label">@L["SystemNotificationRules:RuleName"]</label>
                <input type="text" class="form-control" id="filterRuleName" placeholder="@L["SystemNotificationRules:SearchByName"]">
            </div>
            <div class="col-6 col-md-2">
                <label for="filterEntityType" class="form-label">@L["SystemNotificationRules:EntityType"]</label>
                <select class="form-select" id="filterEntityType"></select>
            </div>
            <div class="col-6 col-md-2">
                <label for="filterIsDefault" class="form-label">@L["SystemNotificationRules:IsDefault"]</label>
                <select class="form-select" id="filterIsDefault">
                    <option value="">@L["SystemNotificationRules:All"]</option>
                    <option value="true">@L["SystemNotificationRules:Yes"]</option>
                    <option value="false">@L["SystemNotificationRules:No"]</option>
                </select>
            </div>
            <div class="col col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary" id="clearFilters" title="@L["SystemNotificationRules:ClearFilters"]">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <abp-table striped-rows="true" id="SystemNotificationRulesTable">
            <thead>
                <tr>
                    <th>@L["SystemNotificationRules:RuleName"]</th>
                    <th>@L["SystemNotificationRules:EntityType"]</th>
                    <th>@L["SystemNotificationRules:ConditionsCount"]</th>
                    <th>@L["SystemNotificationRules:IsDefault"]</th>
                    <th>@L["SystemNotificationRules:Owner"]</th>
                    <th>@L["SystemNotificationRules:CreationTime"]</th>
                    <th>@L["SystemNotificationRules:Actions"]</th>
                </tr>
            </thead>
            <tbody>
                <!-- Data will be loaded via DataTables -->
            </tbody>
        </abp-table>
    </abp-card-body>
</abp-card>

<!-- Modal for Creating/Editing System Notification Rules -->
<div class="modal fade" id="SystemNotificationRuleModal" tabindex="-1" role="dialog" aria-labelledby="SystemNotificationRuleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title fs-5 text-white" id="SystemNotificationRuleModalLabel">@L["SystemNotificationRules:CreateRule"]</h5>
                <button type="button" class="btn text-white" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="SystemNotificationRuleForm" class="needs-validation" novalidate>
                    <input type="hidden" id="editRuleId" name="id" />
                    
                    <div class="shadow-sm row w-100 rounded-3 p-2 mx-0 mb-3">
                        <div class="col-6">
                            <label for="editRuleName" class="form-label">@L["SystemNotificationRules:RuleName"]</label>
                            <input type="text" class="form-control" id="editRuleName" name="ruleName" required>
                            <div class="invalid-feedback" id="editRuleNameError" style="display: none;">
                                @L["SystemNotificationRules:RuleNameRequired"]
                            </div>
                        </div>
                        <div class="col-6">
                            <label for="editEntityType" class="form-label">@L["SystemNotificationRules:EntityType"]</label>
                            <select class="form-select" id="editEntityType" name="entityType" required>
                                <option value="">@L["SystemNotificationRules:SelectEntityType"]</option>
                                <option value="RawGmvMaxProductCreativeReportEntity">@L["SystemNotificationRules:GMVMaxProductCreative"]</option>
                                <option value="RawGmvMaxProductCampaignReportEntity">@L["SystemNotificationRules:GMVMaxProductCampaign"]</option>
                                <option value="RawGmvMaxLiveCampaignReportEntity">@L["SystemNotificationRules:GMVMaxLiveCampaign"]</option>
                            </select>
                            <div class="invalid-feedback" id="editEntityTypeError" style="display: none;">
                                @L["SystemNotificationRules:EntityTypeRequired"]
                            </div>
                        </div>
                    </div>

                    @{
                        var canEditManageRule = await AuthorizationService.IsGrantedAsync(TikTokPermissions.SystemNotificationRules.ManageRule);
                    }
                    @if (canEditManageRule)
                    {
                        <div class="mb-3">
                            <div class="shadow-sm row rounded-3 p-2">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editIsDefault" name="isDefault">
                                        <label class="form-check-label fw-bold" for="editIsDefault">
                                            @L["SystemNotificationRules:IsDefault"]
                                        </label>
                                    </div>
                                    <small class="text-muted">@L["SystemNotificationRules:IsDefaultDescription"]</small>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editIsPublic" name="isPublic">
                                        <label class="form-check-label fw-bold" for="editIsPublic">
                                            @L["SystemNotificationRules:IsPublic"]
                                        </label>
                                    </div>
                                    <small class="text-muted">@L["SystemNotificationRules:IsPublicDescription"]</small>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="mb-3">
                        <label class="form-label fw-bold">@L["SystemNotificationRules:Conditions"]</label>
                        <div id="editQuerybuilder" class="border rounded p-3 bg-light"></div>
                        <input type="hidden" id="editConditionsJson" name="conditionsJson" />
                        <small class="text-muted">@L["SystemNotificationRules:ConditionsDescription"]</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["SystemNotificationRules:Cancel"]</button>
                <button type="button" class="btn btn-primary" id="saveRuleButton" title="@L["SystemNotificationRules:Save"]">@L["SystemNotificationRules:Save"]</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for System Notification Rule Consumers -->
<div class="modal fade" id="systemNotificationRuleConsumersModal" tabindex="-1" role="dialog" aria-labelledby="systemNotificationRuleConsumersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title fs-5 text-white" id="systemNotificationRuleConsumersModalLabel">@L["SystemNotificationRules:ConsumersTitle"]</h5>
                <button type="button" class="btn text-white" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- Tab Navigation -->
                <div class="w-100 overflow-x-auto overflow-y-hidden pb-2">
                    <ul class="nav nav-tabs w-max-content mb-0" id="systemNotificationRuleConsumersTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="system-notification-ad-accounts-tab" data-bs-toggle="tab"
                                data-bs-target="#system-notification-ad-accounts" type="button" role="tab" aria-controls="system-notification-ad-accounts"
                                aria-selected="true">
                                <i class="fas fa-ad"></i> @L["SystemNotificationRules:AdAccountsTab"]
                                <span class="badge bg-primary ms-2" id="systemNotificationAdAccountsCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-notification-business-centers-tab" data-bs-toggle="tab"
                                data-bs-target="#system-notification-business-centers" type="button" role="tab" aria-controls="system-notification-business-centers"
                                aria-selected="false">
                                <i class="fas fa-building"></i> @L["SystemNotificationRules:BusinessCentersTab"]
                                <span class="badge bg-success ms-2" id="systemNotificationBusinessCentersCount">0</span>
                        </button>
                    </li>
                </ul>
                </div>

                <!-- Tab Content -->
                <div class="tab-content py-0" id="systemNotificationRuleConsumersTabContent">
                    <!-- Ad Accounts Tab -->
                    <div class="tab-pane fade show active" id="system-notification-ad-accounts" role="tabpanel" aria-labelledby="system-notification-ad-accounts-tab">
                        <div class="card mt-1">
                            <div class="card-body">
                                <div class="my-2 row mx-0 row-gap-3 justify-content-between">
                                    <div class="col-12 col-md-4 px-md-0">
                                        <label for="systemNotificationAdAccountSearch" class="form-label">@L["SystemNotificationRules:SearchAdAccounts"]</label>
                                        <input type="text" class="form-control" id="systemNotificationAdAccountSearch"
                                            placeholder="@L["SystemNotificationRules:SearchAdAccounts"]">
                                    </div>

                                    <div class="col-12 col-md-8 row mx-0 align-items-end row-gap-3">
                                        <div class="col-6 col-md-4 px-1">
                                            <button id="addSystemNotificationAdAccountBtn" type="button" 
                                                class="btn btn-primary w-100 btn-nowrap" style="display: none;">
                                                <i class="fas fa-plus"></i>
                                                @L["SystemNotificationRules:AddAdAccounts"]
                                            </button>
                                        </div>

                                        <div class="dropdown col-6 col-md-4 px-1">
                                            <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" id="systemNotificationViewModeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-th-large"></i>
                                                @L["SystemNotificationRules:ViewModeDetail"]
                                            </button>
                                            <ul id="systemNotificationRuleConsumersViewModeDropdown" class="dropdown-menu" aria-labelledby="systemNotificationViewModeDropdown">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="toggleSystemNotificationViewMode('simple')" data-mode="simple">
                                                        <i class="fas fa-list"></i> @L["SystemNotificationRules:ViewModeSimple"]
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item active" href="#" onclick="toggleSystemNotificationViewMode('detail')" data-mode="detail">
                                                        <i class="fas fa-th-large"></i> @L["SystemNotificationRules:ViewModeDetail"]
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="toggleSystemNotificationViewMode('table')" data-mode="table">
                                                        <i class="fas fa-table"></i> @L["SystemNotificationRules:ViewModeTable"]
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>

                                        <div class="col-6 col-md-2 px-1">
                                            <button id="selectAllSystemNotificationAdAccountsBtn" 
                                                type="button" class="btn btn-outline-secondary w-100 btn-nowrap" style="display: none;"
                                                data-toggle="tooltip" data-placement="top" title="@L["SystemNotificationRules:SelectAll"]">
                                                <i class="fas fa-check-square"></i>
                                                @L["SystemNotificationRules:SelectAll"]
                                            </button>
                                        </div>

                                        <div class="col-6 col-md-2 px-1">
                                            <button id="deleteSelectedSystemNotificationAdAccountsBtn" type="button" class="btn btn-danger w-100 btn-nowrap d-none">
                                                <i class="fas fa-trash"></i>
                                                @L["SystemNotificationRules:DeleteSelected"]
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div id="systemNotificationAdAccountsList" class="consumers-list row">
                                    <!-- Ad accounts will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Centers Tab -->
                    <div class="tab-pane fade" id="system-notification-business-centers" role="tabpanel" aria-labelledby="system-notification-business-centers-tab">
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="my-2 d-flex justify-content-end">
                                    <button id="selectAllSystemNotificationBusinessCentersBtn" type="button" class="btn btn-outline-secondary me-2" style="display: none;">
                                        <i class="fas fa-check-square"></i>
                                        @L["SystemNotificationRules:SelectAll"]
                                    </button>
                                    <button id="deleteSelectedSystemNotificationBusinessCentersBtn" type="button" class="btn btn-danger d-none">
                                        <i class="fas fa-trash"></i>
                                        @L["SystemNotificationRules:DeleteSelected"]
                                    </button>
                                </div>
                                <div id="systemNotificationBusinessCentersList" class="consumers-list">
                                    <!-- Business centers will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["SystemNotificationRules:Close"]</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Selecting Ad Accounts for System Notification Rules -->
<div class="modal fade" id="selectSystemNotificationAdAccountsModal" tabindex="-1" role="dialog" aria-labelledby="selectSystemNotificationAdAccountsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title fs-5 text-white" id="selectSystemNotificationAdAccountsModalLabel">Chọn Tài khoản Quảng cáo</h5>
                <button type="button" class="btn text-white" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- Search Section -->
                <div class="my-3 mx-0 d-flex">
                    <div class="flex-grow-1 px-1">
                        <label for="systemNotificationAdAccountSearchInput" class="form-label">Tìm kiếm tài khoản</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="systemNotificationAdAccountSearchInput" placeholder="Nhập tên, ID tài khoản hoặc ID Business Center">
                            <button class="btn btn-outline-secondary" type="button" id="systemNotificationClearSearchBtn" title="Xóa tìm kiếm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <small class="text-muted">
                            Tìm kiếm tài khoản quảng cáo theo tên/ ID tài khoản, Advertiser ID hoặc Business Center ID
                        </small>
                    </div>
                    <div class="flex-grow-1 px-1">
                        <label for="systemNotificationAdAccountSearchDependClientInput" class="form-label">Tìm kiếm theo khách hàng</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="systemNotificationAdAccountSearchDependClientInput" placeholder="Nhập tên, ID khách hàng">
                            <button class="btn btn-outline-secondary" type="button" id="systemNotificationClearSearchDependClientBtn" title="Xóa tìm kiếm">
                                <i class="fas fa-times"></i>
                        </button>
                        </div>
                        <small class="text-muted">
                            Tìm kiếm tài khoản quảng cáo theo tên/ ID khách hàng
                        </small>
                    </div>
                    <div class="flex-grow-3 px-1">
                        <button class="btn btn-outline-secondary" type="button" id="systemNotificationSearchAdAccountsBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="systemNotificationAdAccountsSearchLoading" class="text-center d-none">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">Đang tìm kiếm...</p>
                </div>

                <!-- Search Results -->
                <div class="row mx-0">
                    <div id="systemNotificationAdAccountsSearchResults" class="col-6">
                        <h6 class="mb-3">Kết quả tìm kiếm</h6>
                        <div id="systemNotificationAdAccountsSearchList" class="list-group" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                            <!-- Search results will be populated here -->
                        </div>
                    </div>

                    <!-- Selected Ad Accounts -->
                    <div id="systemNotificationSelectedAdAccountsSection" class="col-6">
                        <h6 class="mb-3">Tài khoản đã chọn <span class="badge bg-primary" id="systemNotificationSelectedAdAccountsCount">0</span></h6>
                        <div id="systemNotificationSelectedAdAccountsList" class="list-group" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                            <!-- Selected ad accounts will be shown here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy bỏ</button>
                <button type="button" class="btn btn-primary" id="systemNotificationConfirmAdAccountsSelection">Xác nhận</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal cảnh báo khi đổi Entity Type -->
<div class="modal fade" id="entityChangeWarningModal" tabindex="-1" role="dialog" aria-labelledby="entityChangeWarningModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm" role="document">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-warning text-dark border-0">
                <h5 class="modal-title fs-5 fw-bold" id="entityChangeWarningModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @L["SystemNotificationRules:EntityChangeWarningTitle"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-3">
                    <i class="fas fa-exclamation-circle text-warning" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center mb-3 fs-5">
                    @L["SystemNotificationRules:EntityChangeWarningMessage"]
                </p>
                <div class="alert alert-warning border-0 bg-light-warning">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle text-warning me-2"></i>
                        <small class="mb-0">
                            @L["SystemNotificationRules:EntityChangeWarningDetail"]
                        </small>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 p-4 pt-0">
                <div class="d-flex gap-2 w-100">
                    <button type="button" class="btn btn-outline-secondary flex-fill" id="entityChangeCancelBtn">
                        <i class="fas fa-times me-1"></i>
                        @L["SystemNotificationRules:Cancel"]
                    </button>
                    <button type="button" class="btn btn-warning flex-fill" id="entityChangeConfirmBtn">
                        <i class="fas fa-check me-1"></i>
                        @L["SystemNotificationRules:ConfirmChange"]
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal xác nhận xóa rule -->
<div class="modal fade" id="deleteRuleConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteRuleConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 400px;">
        <div class="modal-content border-danger shadow">
            <div class="modal-header bg-danger text-white border-0 py-2">
                <h6 class="modal-title fw-bold mb-0 text-white" id="deleteRuleConfirmModalLabel">
                    <i class="fas fa-exclamation-triangle me-2 text-white"></i>
                    @L["SystemNotificationRules:DeleteRuleConfirmTitle"]
                </h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-3 bg-light-danger">
                <div class="text-center mb-2">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 2rem;"></i>
                </div>
                <p class="text-center mb-2 text-danger fw-medium" id="deleteRuleConfirmMessage">
                    @L["SystemNotificationRules:DeleteRuleConfirmMessage"]
                </p>
            </div>
            <div class="modal-footer border-0 p-3 pt-0 bg-light-danger">
                <div class="d-flex gap-2 w-100">
                    <button type="button" class="btn btn-outline-secondary flex-fill" id="deleteRuleCancelBtn">
                        @L["SystemNotificationRules:Cancel"]
                    </button>
                    <button type="button" class="btn btn-danger flex-fill fw-bold" id="deleteRuleConfirmBtn">
                        <i class="fas fa-trash me-1"></i>
                        @L["SystemNotificationRules:DeleteRule"]
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>