using System;
using System.Collections.Generic;
using Volo.Abp.DependencyInjection;
using TikTok.Application.Contracts.SystemNotification;

namespace TikTok.Application.SystemNotification.FieldConfigurations
{
    /// <summary>
    /// Cấu h<PERSON>nh field definitions cho RawGmvMaxProductCreativeReportEntity
    /// </summary>
    public class RawGmvMaxProductCreativeFieldConfiguration : ITransientDependency
    {
        public const string EntityType = "RawGmvMaxProductCreativeReportEntity";

        public List<FieldMetadata> GetFieldDefinitions()
        {
            // Chỉ giữ các trường cốt lõi phục vụ rule creative/video
            return new List<FieldMetadata>
            {
                new FieldMetadata(
                    name: "CreativeType",
                    displayName: "Creative Type",
                    dataType: "String",
                    category: "Creative",
                    description: "Loại creative",
                    allowedValues: new[] { "ADS_AND_ORGANIC", "ORGANIC", "REMOVED" }
                ),
                new FieldMetadata(
                    name: "CreativeDeliveryStatus",
                    displayName: "Creative Delivery Status",
                    dataType: "String",
                    category: "Creative",
                    description: "Trạng thái gửi creative",
                    allowedValues: new[] { "IN_QUEUE", "LEARNING", "DELIVERING", "NOT_DELIVERYIN", "AUTHORIZATION_NEEDED", "EXCLUDED", "UNAVAILABLE", "REJECTED" }
                ),
                new FieldMetadata("AdClickRate", "Ad Click Rate", "Decimal", "Performance", "Tỷ lệ click quảng cáo"),
                new FieldMetadata("AdConversionRate", "Ad Conversion Rate", "Decimal", "Performance", "Tỷ lệ chuyển đổi quảng cáo"),
                new FieldMetadata("ProductClickRate", "Product Click Rate", "Decimal", "Performance", "Tỷ lệ click sản phẩm"),
                new FieldMetadata("Cost", "Cost", "Decimal", "Performance", "Chi phí"),
                new FieldMetadata("Orders", "Orders", "Number", "Performance", "Số đơn hàng")
            };
        }
    }
}


