using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using TikTok.Domain.Entities.SystemNotification;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Interface cho System Rule Engine
    /// Provides contract cho rule evaluation functionality
    /// </summary>
    public interface ISystemRuleEngine : ITransientDependency
    {
        /// <summary>
        /// Evaluate standard rules cho entities
        /// </summary>
        /// <typeparam name="T">Entity type để evaluate</typeparam>
        /// <param name="entities">Entities để check against rules</param>
        /// <param name="entityType">Entity type string representation</param>
        /// <param name="rules">Rules để evaluate</param>
        /// <returns>List of notification requests generated</returns>
        Task<List<SystemNotificationRequest>> EvaluateRules<T>(
            IEnumerable<T> entities, 
            string entityType,
            List<SystemNotificationRule> rules);


        /// <summary>
        /// Evaluate a rule for a single entity
        /// Core method được sử dụng mostly nhất trong testing
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity để evaluate</param>
        /// <param name="rule">Rule để check</param>
        /// <returns>True nếu rule match với entity</returns>
        bool EvaluateRule<T>(T entity, SystemNotificationRule rule);

        /// <summary>
        /// Get property value from entity using reflection
        /// Utility method cho rule evaluation
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity để extract property value</param>
        /// <param name="propertyName">Property name</param>
        /// <returns>Property value hoặc null</returns>
        object? GetPropertyValue<T>(T entity, string propertyName);
    }
}
