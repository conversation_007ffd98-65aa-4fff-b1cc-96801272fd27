using System;
using System.Threading.Tasks;
using TikTok.FactGmvMaxProductCreatives.Dtos;
using System.Collections.Generic;
using TikTok.Facts.FactGmvMaxProductCreative;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.FactGmvMaxProductCreatives
{
    /// <summary>
    /// Service interface cho FactGmvMaxProductCreative
    /// Tương tự IFactGmvMaxProductService nhưng cho Creative level analysis
    /// </summary>
    public interface IFactGmvMaxProductCreativeService
    {
        // ✅ Core Data Methods
        Task<GetFactGmvMaxProductCreativeDataResponse> GetListAsync(DateTime fromDate, DateTime toDate, string? currency = "USD", bool hasViewAllAdvertisers = false);
        Task<GetFactGmvMaxProductCreativeDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        
        // ✅ NEW: Enhanced method with additional filters for better performance
        Task<GetFactGmvMaxProductCreativeDataResponse> GetListWithFiltersAsync(
            DateTime fromDate, 
            DateTime toDate, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? creativeType = null,
            string? itemGroupId = null,
            string? itemId = null);
        Task<FactGmvMaxProductCreativeSummaryDto> GetSummaryAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<GmvMaxProductCreativeTrendDto>> GetTrendsAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<GmvMaxProductCreativeTopSellingDto>> GetTopSellingAsync(DateTime fromDate, DateTime toDate, int limit);
        
        // ✅ Dashboard Methods (tương tự Product)
        Task<GmvMaxProductCreativeDashboardDto> GetDashboardAsync(string? currency = "USD");
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD");
        Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD");
        
        // ✅ Section-specific methods for independent loading
        Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD",
            string? creativeType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null,
            string? itemGroupId = null,
            string? itemId = null);
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD");
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD");
        Task<DetailedChartsDto> GetDetailedChartsAsync();
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD");
        
        // ✅ NEW: Creative-specific methods
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativePerformanceAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativeDeliveryDto>> GetCreativeDeliveryAnalysisAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativeContentTypeDto>> GetContentTypeAnalysisAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        Task<IEnumerable<DashboardCreativeRanking>> GetCreativeRankingAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        Task<IEnumerable<DashboardAccountRanking>> GetAccountRankingAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        
        // ✅ NEW: Creative filtering methods
        Task<GetFactGmvMaxProductCreativeDataResponse> GetByCreativeTypeAsync(DateTime fromDate, DateTime toDate, string creativeType, string? currency = "USD");
        Task<GetFactGmvMaxProductCreativeDataResponse> GetByContentTypeAsync(DateTime fromDate, DateTime toDate, string shopContentType, string? currency = "USD");
        Task<GetFactGmvMaxProductCreativeDataResponse> GetByDeliveryStatusAsync(DateTime fromDate, DateTime toDate, string deliveryStatus, string? currency = "USD");
        Task<GetFactGmvMaxProductCreativeDataResponse> GetByTikTokAccountAsync(DateTime fromDate, DateTime toDate, string ttAccountName, string? currency = "USD");
        Task<GetFactGmvMaxProductCreativeDataResponse> GetByProductGroupAsync(DateTime fromDate, DateTime toDate, string itemGroupId, string? currency = "USD");
        
        // ✅ NEW: Creative performance analysis methods
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetLowPerformanceCreativesAsync(DateTime fromDate, DateTime toDate, decimal roasThreshold = 1.5m, decimal tacosThreshold = 30.0m, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetHighPerformanceCreativesAsync(DateTime fromDate, DateTime toDate, decimal roasThreshold = 3.0m, string? currency = "USD");
        Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativeAlertsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
    }
}

