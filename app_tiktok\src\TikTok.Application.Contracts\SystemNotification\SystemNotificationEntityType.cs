using System;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Entity types supported by the System Notification rules.
    /// Backed by code strings for storage/compatibility.
    /// </summary>
    public enum SystemNotificationEntityType
    {
        RawGmvMaxProductCreativeReportEntity,
        RawGmvMaxProductCampaignReportEntity,
        RawGmvMaxLiveCampaignReportEntity
    }

    public static class SystemNotificationEntityTypeExtensions
    {
        /// <summary>
        /// Convert enum to persisted code string used across the system.
        /// </summary>
        public static string ToCode(this SystemNotificationEntityType entityType)
        {
            return entityType switch
            {
                SystemNotificationEntityType.RawGmvMaxProductCampaignReportEntity => "RawGmvMaxProductCampaignReportEntity",
                SystemNotificationEntityType.RawGmvMaxProductCreativeReportEntity => "RawGmvMaxProductCreativeReportEntity",
                SystemNotificationEntityType.RawGmvMaxLiveCampaignReportEntity => "RawGmvMaxLiveCampaignReportEntity",
                _ => entityType.ToString()
            };
        }

        /// <summary>
        /// Try parse a code string into enum. Returns null if unknown.
        /// </summary>
        public static SystemNotificationEntityType? TryParse(string? code)
        {
            if (string.IsNullOrWhiteSpace(code)) return null;
            return code switch
            {
                "RawGmvMaxProductCampaignReportEntity" => SystemNotificationEntityType.RawGmvMaxProductCampaignReportEntity,
                "RawGmvMaxProductCreativeReportEntity" => SystemNotificationEntityType.RawGmvMaxProductCreativeReportEntity,
                "RawGmvMaxLiveCampaignReportEntity" => SystemNotificationEntityType.RawGmvMaxLiveCampaignReportEntity,
                _ => null
            };
        }
    }
}


