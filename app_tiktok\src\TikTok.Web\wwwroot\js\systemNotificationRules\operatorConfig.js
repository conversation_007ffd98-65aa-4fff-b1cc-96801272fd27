(function () {
    // Simple localization passthrough: if a localization helper exists, use it for labels.
    // Otherwise, fall back to the raw operator key.
    function getLabel(ns, key) {
        try {
            // Prefer a NotificationRules-like localization object if available
            if (
                window.syncfusionLocalization &&
                window.syncfusionLocalization[ns] &&
                window.syncfusionLocalization[ns][key]
            ) {
                return window.syncfusionLocalization[ns][key];
            }
            // Fallback: use SystemNotificationRules localization dictionary if present
            if (
                typeof window.systemNotificationRulesLocalization === 'object'
            ) {
                return window.systemNotificationRulesLocalization[key] || key;
            }
        } catch (_) {
            /* noop */
        }
        return key;
    }

    var dateOperators = [
        { value: 'equal', key: getLabel('dateOperators', 'equal') },
        { value: 'greaterthan', key: getLabel('dateOperators', 'greaterthan') },
        {
            value: 'greaterthanorequal',
            key: getLabel('dateOperators', 'greaterthanorequal'),
        },
        { value: 'lessthan', key: getLabel('dateOperators', 'lessthan') },
        {
            value: 'lessthanorequal',
            key: getLabel('dateOperators', 'lessthanorequal'),
        },
        { value: 'notequal', key: getLabel('dateOperators', 'notequal') },
        { value: 'between', key: getLabel('dateOperators', 'between') },
        { value: 'notbetween', key: getLabel('dateOperators', 'notbetween') },
    ];

    var stringOperators = [
        { value: 'equal', key: getLabel('stringOperators', 'equal') },
        { value: 'notequal', key: getLabel('stringOperators', 'notequal') },
        { value: 'contains', key: getLabel('stringOperators', 'contains') },
        {
            value: 'notcontains',
            key: getLabel('stringOperators', 'notcontains'),
        },
        { value: 'startswith', key: getLabel('stringOperators', 'startswith') },
        { value: 'endswith', key: getLabel('stringOperators', 'endswith') },
    ];

    var numberOperators = [
        { value: 'equal', key: getLabel('numberOperators', 'equal') },
        { value: 'notequal', key: getLabel('numberOperators', 'notequal') },
        {
            value: 'greaterthan',
            key: getLabel('numberOperators', 'greaterthan'),
        },
        {
            value: 'greaterthanorequal',
            key: getLabel('numberOperators', 'greaterthanorequal'),
        },
        { value: 'lessthan', key: getLabel('numberOperators', 'lessthan') },
        {
            value: 'lessthanorequal',
            key: getLabel('numberOperators', 'lessthanorequal'),
        },
        { value: 'between', key: getLabel('numberOperators', 'between') },
        { value: 'notbetween', key: getLabel('numberOperators', 'notbetween') },
    ];

    var booleanOperators = [
        { value: 'equal', key: getLabel('booleanOperators', 'equal') },
        { value: 'notequal', key: getLabel('booleanOperators', 'notequal') },
    ];

    window.systemNotificationOperatorConfig = {
        dateOperators: dateOperators,
        stringOperators: stringOperators,
        numberOperators: numberOperators,
        booleanOperators: booleanOperators,
    };
})();
