using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using TikTok.Domain.Entities.SystemNotification;

namespace TikTok.Domain.Repositories
{
    /// <summary>
    /// Repository interface cho SystemNotificationRule
    /// Cung cấp các methods chuyên biệt cho system notification rules
    /// </summary>
    public interface ISystemNotificationRuleRepository : IRepository<SystemNotificationRule, Guid>
    {
        /// <summary>
        /// Universal method để lấy rules với các filter options
        /// </summary>
        Task<List<SystemNotificationRule>> GetRulesAsync(
            string? entityType = null,
            bool? isActive = true,
            Guid? creatorId = null,
            bool? hasCustomTemplate = null,
            bool? isCrossEntity = null,
            DateTime? createdAfter = null,
            DateTime? createdBefore = null,
            bool? isDefault = null,
            bool? isPublic = null,
            string? searchText = null,
            int skipCount = 0,
            int maxResultCount = 0);

        /// <summary>
        /// Lấy statistics về rules
        /// </summary>
        Task<SystemNotificationRuleStats> GetRuleStatisticsAsync();

        /// <summary>
        /// Lấy rules cần update cooldown (deprecated - CooldownMinutes field has been removed)
        /// </summary>
        Task<List<SystemNotificationRule>> GetRulesNeedingCooldownUpdateAsync();

        /// <summary>
        /// Lấy rules theo search criteria
        /// </summary>
        Task<List<SystemNotificationRule>> SearchRulesAsync(SystemNotificationRuleSearchCriteria criteria);

        /// <summary>
        /// Đếm số rules theo search criteria
        /// </summary>
        Task<long> GetCountAsync(SystemNotificationRuleSearchCriteria criteria);
    }

    /// <summary>
    /// Search criteria cho system notification rules
    /// </summary>
    public class SystemNotificationRuleSearchCriteria
    {
        public string? EntityType { get; set; }
        public string? FieldName { get; set; }
        public string? Operator { get; set; }
        public string? RecipientType { get; set; }
        public string[]? Tags { get; set; }
        public int? MinPriority { get; set; }
        public int? MaxPriority { get; set; }
        public bool? IsActive { get; set; }
        public bool? HasCustomTemplate { get; set; }
        public bool? IsCrossEntity { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public Guid? CreatorId { get; set; }
        public string? SearchText { get; set; } // Search in RuleName, Description
        
        // New search criteria
        public bool? IsDefault { get; set; }
        public bool? IsPublic { get; set; }
        public int? TargetEntity { get; set; }
        public int? NotificationFrequency { get; set; }
        
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 100;
    }

    /// <summary>
    /// Statistics về system notification rules
    /// </summary>
    public class SystemNotificationRuleStats
    {
        public int TotalRules { get; set; }
        public int ActiveRules { get; set; }
        public int InactiveRules { get; set; }
        public int CrossEntityRules { get; set; }
        public int StandardRules { get; set; }
        public int RulesWithCustomTemplates { get; set; }
        public int RulesWithCooldown { get; set; }
        public int RulesByEntityType { get; set; }
        public int RulesByRecipientType { get; set; }
        public Dictionary<string, int> RulesByEntityTypeBreakdown { get; set; } = new();
        public Dictionary<string, int> RulesByRecipientTypeBreakdown { get; set; } = new();
        public Dictionary<string, int> RulesByOperatorBreakdown { get; set; } = new();
        public Dictionary<string, int> RulesByFieldBreakdown { get; set; } = new();
        public long TotalTriggerCount { get; set; }
        public DateTime? LastTriggerTime { get; set; }
        public DateTime? OldestRuleCreated { get; set; }
        public DateTime? NewestRuleCreated { get; set; }
    }
}
