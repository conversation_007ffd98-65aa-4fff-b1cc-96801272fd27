/**
 * Query Builder Component for System Notification Rules
 * Handles Syncfusion QueryBuilder initialization and management
 */

const QueryBuilderManager = {
    // Initialize create query builder
    initCreateQueryBuilder() {
        const qbEl = document.getElementById('editQuerybuilder');
        qbEl.innerHTML = '';
        
        // fetch fields whenever entity changes (not just once)
        const selectEl = document.getElementById('editEntityType');
        selectEl.onchange = async function () {
            const entity = this.value;
            if (!entity) {
                qbEl.innerHTML = '';
                return;
            }

            // Kiểm tra nếu có conditions đã tạo (chỉ hiện cảnh báo khi có điều kiện thực sự)
            const qb = qbEl.__qb;
            if (qb && qb.rule && qb.rule.rules && qb.rule.rules.length > 0) {
                console.log('🔍 Checking conditions:', qb.rule.rules);
                
                // Kiểm tra xem có điều kiện thực sự hay chỉ là rule mặc định
                const hasRealConditions = qb.rule.rules.some(rule => {
                    console.log('🔍 Checking rule:', rule);
                    
                    // Bỏ qua rule mặc định (chỉ có field và operator, không có value thực sự)
                    // Kiểm tra value có ý nghĩa thực sự
                    if (rule.value === '' || rule.value === null || rule.value === undefined) {
                        console.log('❌ Rule has empty value, skipping');
                        return false;
                    }
                    
                    // Kiểm tra nếu là array (cho operator 'between', 'in', 'notin')
                    if (Array.isArray(rule.value)) {
                        const hasValidArrayValue = rule.value.some(v => v !== '' && v !== null && v !== undefined);
                        console.log('🔍 Array value check:', rule.value, 'hasValid:', hasValidArrayValue);
                        return hasValidArrayValue;
                    }
                    
                    // Kiểm tra string có nội dung thực sự
                    if (typeof rule.value === 'string') {
                        const hasValidStringValue = rule.value.trim() !== '';
                        console.log('🔍 String value check:', rule.value, 'hasValid:', hasValidStringValue);
                        return hasValidStringValue;
                    }
                    
                    // Các kiểu khác (number, boolean) đều coi là có giá trị
                    console.log('✅ Rule has valid value:', rule.value);
                    return true;
                });
                
                console.log('🔍 Has real conditions:', hasRealConditions);
                
                if (hasRealConditions) {
                    console.log('⚠️ Showing warning modal');
                    // Lưu thông tin để xử lý sau khi user xác nhận
                    window.ModalManager.pendingEntityChange = entity;
                    window.ModalManager.pendingSelectElement = this;

                    // Hiển thị modal cảnh báo
                    $('#entityChangeWarningModal').modal('show');
                    return;
                } else {
                    console.log('✅ No real conditions, proceeding with entity change');
                }
            }

            // Nếu không có conditions, tiếp tục đổi entity
            await QueryBuilderManager.changeEntityType(this, entity, qbEl);
        };
    },

    // Initialize edit query builder
    initEditQueryBuilder(entity, conditions) {
        const qbEl = document.getElementById('editQuerybuilder');
        qbEl.innerHTML = '';
        if (!entity) return;
        
        SystemNotificationApiService.getFields(entity).then((fields) => {
            this.renderQueryBuilder(qbEl, fields, conditions || []);
        });
        
        // allow reloading fields when entity changes in Edit modal
        const selectEl = document.getElementById('editEntityType');
        if (selectEl) {
            selectEl.onchange = async function () {
                const newEntity = this.value;
                if (!newEntity) {
                    qbEl.innerHTML = '';
                    return;
                }

                // Kiểm tra nếu có conditions đã tạo (chỉ hiện cảnh báo khi có điều kiện thực sự)
                const qb = qbEl.__qb;
                if (qb && qb.rule && qb.rule.rules && qb.rule.rules.length > 0) {
                    console.log('🔍 Checking conditions:', qb.rule.rules);
                    
                    // Kiểm tra xem có điều kiện thực sự hay chỉ là rule mặc định
                    const hasRealConditions = qb.rule.rules.some(rule => {
                        console.log('🔍 Checking rule:', rule);
                        
                        // Bỏ qua rule mặc định (chỉ có field và operator, không có value thực sự)
                        // Kiểm tra value có ý nghĩa thực sự
                        if (rule.value === '' || rule.value === null || rule.value === undefined) {
                            console.log('❌ Rule has empty value, skipping');
                            return false;
                        }
                        
                        // Kiểm tra nếu là array (cho operator 'between', 'in', 'notin')
                        if (Array.isArray(rule.value)) {
                            const hasValidArrayValue = rule.value.some(v => v !== '' && v !== null && v !== undefined);
                            console.log('🔍 Array value check:', rule.value, 'hasValid:', hasValidArrayValue);
                            return hasValidArrayValue;
                        }
                        
                        // Kiểm tra string có nội dung thực sự
                        if (typeof rule.value === 'string') {
                            const hasValidStringValue = rule.value.trim() !== '';
                            console.log('🔍 String value check:', rule.value, 'hasValid:', hasValidStringValue);
                            return hasValidStringValue;
                        }
                        
                        // Các kiểu khác (number, boolean) đều coi là có giá trị
                        console.log('✅ Rule has valid value:', rule.value);
                        return true;
                    });
                    
                    console.log('🔍 Has real conditions:', hasRealConditions);
                    
                    if (hasRealConditions) {
                        console.log('⚠️ Showing warning modal');
                        // Lưu thông tin để xử lý sau khi user xác nhận
                        window.ModalManager.pendingEntityChange = newEntity;
                        window.ModalManager.pendingSelectElement = this;

                        // Hiển thị modal cảnh báo
                        $('#entityChangeWarningModal').modal('show');
                        return;
                    } else {
                        console.log('✅ No real conditions, proceeding with entity change');
                    }
                }

                // Nếu không có conditions, tiếp tục đổi entity
                await QueryBuilderManager.changeEntityType(this, newEntity, qbEl);
            };
        }
    },

    // Change entity type
    async changeEntityType(selectElement, entity, qbEl) {
        // Lưu giá trị hiện tại để có thể khôi phục
        selectElement.dataset.previousValue = selectElement.value;

        const fields = await SystemNotificationApiService.getFields(entity);
        this.renderQueryBuilder(qbEl, fields, []);
    },

    // Render query builder
    renderQueryBuilder(container, fields, conditions) {
        // Destroy existing QueryBuilder if it exists
        if (container.__qb) {
            try {
                container.__qb.destroy();
            } catch (e) {
                console.warn('Error destroying existing QueryBuilder:', e);
            }
            container.__qb = null;
        }

        // Clear container content
        container.innerHTML = '';

        // ✅ Enhanced schema with proper Syncfusion format
        const columns = (fields || []).map((f) => {
            const column = {
                field: f.name,
                label: f.displayName || f.name,
                type: mapDataTypeToSyncfusion(f.dataType || 'string'),
            };

            // ✅ FE controls operators by dataType (parity with NotificationRules)
            const cfg = window.systemNotificationOperatorConfig || {};
            const t = column.type;
            if (t === 'string') column.operators = cfg.stringOperators || [];
            else if (t === 'number') column.operators = cfg.numberOperators || [];
            else if (t === 'boolean') column.operators = cfg.booleanOperators || [];
            else if (t === 'date') column.operators = cfg.dateOperators || [];
            else column.operators = cfg.stringOperators || [];

            // ✅ Store allowedValues and install Syncfusion template (DropDownList)
            if (f.allowedValues && f.allowedValues.length > 0) {
                column._allowedValues = f.allowedValues;
                column.template = {
                    create: function () {
                        var el = document.createElement('input');
                        el.setAttribute('type', 'text');
                        return el;
                    },
                    write: function (args) {
                        var data = (column._allowedValues || []).map(function (v) {
                            return { value: v, text: v };
                        });
                        var dropDownObj = new ej.dropdowns.DropDownList({
                            dataSource: data,
                            value: args.values ? args.values : '',
                            fields: { text: 'text', value: 'value' },
                            placeholder: '-- Select Value --',
                            change: function (e) {
                                var qbInst = container.__qb;
                                if (qbInst) qbInst.notifyChange(e.itemData.value, e.element);
                            },
                        });
                        dropDownObj.appendTo('#' + args.elements.id);
                    },
                    destroy: function (args) {
                        try {
                            var cmp = ej.base.getComponent(
                                document.getElementById(args.elementId),
                                'dropdownlist'
                            );
                            if (cmp) cmp.destroy();
                        } catch (_) {}
                    },
                };
            }

            return column;
        });

        // ✅ Debug logging
        console.log('QueryBuilder columns:', columns);
        console.log('QueryBuilder conditions:', conditions);
        console.log('Fields with allowedValues:', columns.filter((col) => col._allowedValues));

        // ✅ Build safe default rule if none provided (parity with NotificationRules)
        var ruleConfig;
        if (conditions && conditions.length > 0) {
            ruleConfig = { condition: 'and', rules: conditions };
        } else if (columns && columns.length > 0) {
            ruleConfig = {
                condition: 'and',
                rules: [
                    {
                        label: 'Field',
                        field: columns[0].field,
                        type: columns[0].type || 'string',
                        operator: 'equal',
                        value: '',
                    },
                ],
            };
        } else {
            ruleConfig = { condition: 'and', rules: [] };
        }

        const qb = new ej.querybuilder.QueryBuilder({
            width: '100%',
            columns,
            rule: ruleConfig,
            allowValidation: true,
            showButtons: {
                ruleDelete: true,
                groupDelete: true,
            },
            locale: window.currentLang || 'en',
            // Guard rule values when operator/field changes
            actionBegin: function (args) {
                try {
                    if (
                        args.requestType === 'condition-operator-change' ||
                        args.requestType === 'value-change' ||
                        args.requestType === 'field-operator-change' ||
                        args.requestType === 'rule-change'
                    ) {
                        const rule = args.rule || args?.target?.rule || null;
                        if (!rule) return;
                        const op = (rule.operator || '')
                            .toString()
                            .toLowerCase()
                            .replace(/\s+/g, '');
                        if (op === 'between') {
                            if (!Array.isArray(rule.value)) rule.value = ['', ''];
                        } else {
                            if (rule.value == null || Array.isArray(rule.value)) {
                                rule.value = '';
                            }
                        }
                    }
                } catch (e) {
                    console.warn('actionBegin guard error', e);
                }
            },
            // ✅ Custom value editor for dropdown fields
            valueEditor: {
                create: function (args) {
                    console.log('ValueEditor create called with args:', args);
                    const fieldConfig = columns.find((col) => col.field === args.column.field);
                    console.log('FieldConfig found:', fieldConfig);

                    if (fieldConfig && fieldConfig._allowedValues && fieldConfig._allowedValues.length > 0) {
                        console.log('Creating dropdown for field:', args.column.field, 'with values:', fieldConfig._allowedValues);
                        // ✅ Create dropdown for allowedValues
                        const dropdown = document.createElement('select');
                        dropdown.className = 'form-control';

                        // Add empty option
                        const emptyOption = document.createElement('option');
                        emptyOption.value = '';
                        emptyOption.textContent = '-- Select Value --';
                        dropdown.appendChild(emptyOption);

                        // Add allowed values
                        fieldConfig._allowedValues.forEach((value) => {
                            const option = document.createElement('option');
                            option.value = value;
                            option.textContent = value;
                            dropdown.appendChild(option);
                        });

                        return dropdown;
                    }

                    console.log('Creating default input for field:', args.column.field);
                    // ✅ Default input for other fields
                    const input = document.createElement('input');
                    input.type = getInputType(fieldConfig?.type || 'string');
                    input.className = 'form-control';
                    return input;
                },
                write: function (args, ui) {
                    try {
                        if (!ui) return;
                        const type = args.column?.type || 'string';
                        if (ui.tagName === 'SELECT') {
                            ui.value = args.value == null ? '' : String(args.value);
                            return;
                        }
                        if (ui.tagName === 'INPUT') {
                            if (type === 'number') {
                                ui.value = args.value != null ? Number(args.value) : '';
                            } else if (type === 'boolean') {
                                ui.type = 'checkbox';
                                ui.checked = Boolean(args.value);
                            } else if (type === 'date') {
                                ui.value = args.value || '';
                            } else {
                                ui.value = args.value != null ? args.value : '';
                            }
                        }
                    } catch (e) {
                        console.warn('valueEditor.write error', e);
                    }
                },
                read: function (args, ui) {
                    if (!ui) return args.value ?? '';
                    const type = args.column?.type || 'string';
                    if (ui.tagName === 'SELECT') return ui.value;
                    if (ui.tagName === 'INPUT') {
                        if (type === 'number') {
                            const n = Number(ui.value);
                            return isNaN(n) ? null : n;
                        }
                        if (type === 'boolean') return Boolean(ui.checked);
                        return ui.value;
                    }
                    return args.value ?? '';
                },
                destroy: function (args, ui) {
                    if (ui && ui.parentNode) ui.parentNode.removeChild(ui);
                },
            },
        });

        qb.appendTo(container);
        container.__qb = qb;
        container.__fields = fields || [];
    },

    // Collect conditions JSON
    collectConditionsJson(containerId) {
        const el = document.getElementById(containerId);
        const qb = el && el.__qb;
        if (!qb) return null;
        const rule = qb.getValidRules(qb.rule);
        return rule ? JSON.stringify(rule) : null;
    }
};

// Export to global scope
window.QueryBuilderManager = QueryBuilderManager;
