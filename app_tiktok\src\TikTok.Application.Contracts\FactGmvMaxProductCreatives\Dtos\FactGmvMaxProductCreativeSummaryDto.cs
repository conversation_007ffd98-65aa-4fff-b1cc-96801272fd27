using System;

namespace TikTok.FactGmvMaxProductCreatives.Dtos
{
    /// <summary>
    /// Summary DTO cho ProductCreative - tương tự FactGmvMaxProductSummaryDto nhưng cho Creative level
    /// </summary>
    public class FactGmvMaxProductCreativeSummaryDto
    {
        // ========== Date Range ==========
        
        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y kết thúc
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Tổng số bản ghi
        /// </summary>
        public int TotalRecords { get; set; }

        // ========== Financial Metrics (USD) ==========

        /// <summary>
        /// Tổng chi phí quảng cáo (USD)
        /// </summary>
        public decimal TotalCostUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu (USD)
        /// </summary>
        public decimal TotalGrossRevenueUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu quảng cáo (USD)
        /// </summary>
        public decimal TotalAdsRevenueUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu tự nhiên (USD)
        /// </summary>
        public decimal TotalOrganicRevenueUSD { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (USD)
        /// </summary>
        public decimal AvgCostPerOrderUSD { get; set; }

        /// <summary>
        /// Giá sản phẩm trung bình (USD)
        /// </summary>
        public decimal AverageProductPriceUSD { get; set; }

        // ========== Performance Metrics ==========

        /// <summary>
        /// ROAS trung bình
        /// </summary>
        public decimal AverageROAS { get; set; }

        /// <summary>
        /// ACOS trung bình (%)
        /// </summary>
        public decimal AverageACOS { get; set; }

        /// <summary>
        /// ROI trung bình (%)
        /// </summary>
        public decimal AverageROI { get; set; }

        /// <summary>
        /// TACOS trung bình (%)
        /// </summary>
        public decimal AverageTACOS { get; set; }

        /// <summary>
        /// CTR trung bình (%)
        /// </summary>
        public decimal AverageCTR { get; set; }

        /// <summary>
        /// CPM trung bình (USD)
        /// </summary>
        public decimal AverageCPM { get; set; }

        /// <summary>
        /// CPC trung bình (USD)
        /// </summary>
        public decimal AverageCPC { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi trung bình (%)
        /// </summary>
        public decimal AverageConversionRate { get; set; }

        // ========== Volume Metrics ==========

        /// <summary>
        /// Tổng số đơn hàng
        /// </summary>
        public int TotalOrders { get; set; }

        /// <summary>
        /// Tổng số lượng sản phẩm bán được
        /// </summary>
        public int TotalQuantitySold { get; set; }

        /// <summary>
        /// Tổng lượt hiển thị
        /// </summary>
        public long TotalImpressions { get; set; }

        /// <summary>
        /// Tổng lượt nhấp
        /// </summary>
        public long TotalClicks { get; set; }

        // ========== Creative Metrics ==========

        /// <summary>
        /// Số creative duy nhất (thay vì UniqueProducts)
        /// </summary>
        public int UniqueCreatives { get; set; }

        /// <summary>
        /// Số chiến dịch duy nhất
        /// </summary>
        public int UniqueCampaigns { get; set; }

        /// <summary>
        /// Số store duy nhất
        /// </summary>
        public int UniqueStores { get; set; }

        /// <summary>
        /// Số tài khoản quảng cáo duy nhất
        /// </summary>
        public int UniqueAdAccounts { get; set; }

        /// <summary>
        /// ✅ NEW: Số TikTok Account duy nhất
        /// </summary>
        public int UniqueTikTokAccounts { get; set; }

        /// <summary>
        /// Số danh mục sản phẩm duy nhất
        /// </summary>
        public int UniqueCategories { get; set; }

        /// <summary>
        /// Số ngày có dữ liệu
        /// </summary>
        public int UniqueDates { get; set; }

        /// <summary>
        /// ✅ NEW: Số Product Group duy nhất
        /// </summary>
        public int UniqueProductGroups { get; set; }

        // ========== Performance Classification ==========

        /// <summary>
        /// Số creative có ROAS > 3.0 (Xuất sắc)
        /// </summary>
        public int ExcellentROASCreatives { get; set; }

        /// <summary>
        /// Số creative có ROAS 2.0-3.0 (Tốt)
        /// </summary>
        public int GoodROASCreatives { get; set; }

        /// <summary>
        /// Số creative có ROAS < 2.0 (Cần cải thiện)
        /// </summary>
        public int PoorROASCreatives { get; set; }

        /// <summary>
        /// Số creative có ACOS > 50% (Chi phí cao)
        /// </summary>
        public int HighACOSCreatives { get; set; }

        /// <summary>
        /// Số creative bán chạy (top 20% theo số lượng bán)
        /// </summary>
        public int BestSellingCreatives { get; set; }

        /// <summary>
        /// Số creative có lợi nhuận cao (TACOS < 20%)
        /// </summary>
        public int HighProfitCreatives { get; set; }

        // ========== Creative-specific Metrics ==========

        /// <summary>
        /// ✅ NEW: Creative có doanh thu cao nhất
        /// </summary>
        public string TopRevenueCreativeId { get; set; } = string.Empty;

        /// <summary>
        /// ✅ NEW: Creative có ROAS cao nhất
        /// </summary>
        public string TopROASCreativeId { get; set; } = string.Empty;

        /// <summary>
        /// ✅ NEW: Creative bán chạy nhất
        /// </summary>
        public string BestSellingCreativeId { get; set; } = string.Empty;

        /// <summary>
        /// ✅ NEW: TikTok Account hiệu suất tốt nhất
        /// </summary>
        public string TopPerformingTikTokAccount { get; set; } = string.Empty;

        /// <summary>
        /// ✅ NEW: Creative Type hiệu suất tốt nhất (ADS_AND_ORGANIC, ORGANIC, etc.)
        /// </summary>
        public string TopPerformingCreativeType { get; set; } = string.Empty;

        /// <summary>
        /// ✅ NEW: Content Type hiệu suất tốt nhất (VIDEO, PRODUCT_CARD)
        /// </summary>
        public string TopPerformingContentType { get; set; } = string.Empty;

        /// <summary>
        /// ✅ NEW: Delivery Status distribution
        /// </summary>
        public int ActiveCreatives { get; set; }
        public int PausedCreatives { get; set; }
        public int DisabledCreatives { get; set; }

        // ========== Video Performance Metrics ==========

        /// <summary>
        /// ✅ NEW: Video View Rate 2s trung bình (%)
        /// </summary>
        public decimal AverageVideoViewRate2s { get; set; }

        /// <summary>
        /// ✅ NEW: Video View Rate 6s trung bình (%)
        /// </summary>
        public decimal AverageVideoViewRate6s { get; set; }

        /// <summary>
        /// ✅ NEW: Video View Rate 25% trung bình (%)
        /// </summary>
        public decimal AverageVideoViewRateP25 { get; set; }

        /// <summary>
        /// ✅ NEW: Video View Rate 50% trung bình (%)
        /// </summary>
        public decimal AverageVideoViewRateP50 { get; set; }

        /// <summary>
        /// ✅ NEW: Video View Rate 75% trung bình (%)
        /// </summary>
        public decimal AverageVideoViewRateP75 { get; set; }

        /// <summary>
        /// ✅ NEW: Video View Rate 100% trung bình (%)
        /// </summary>
        public decimal AverageVideoViewRateP100 { get; set; }

        /// <summary>
        /// ✅ NEW: Product Click Rate trung bình (%)
        /// </summary>
        public decimal AverageProductClickRate { get; set; }
    }
}

