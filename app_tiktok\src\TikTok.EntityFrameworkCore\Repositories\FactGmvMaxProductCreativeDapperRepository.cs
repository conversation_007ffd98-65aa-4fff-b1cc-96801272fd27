using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;
using TikTok.Facts.FactGmvMaxProductCreative;
using TikTok.Repositories;
using TikTok.Facts.FactGmvMaxCampaign;
using TikTok.Facts.FactGmvMaxProduct;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Dapper Repository cho FactGmvMaxProductCreativeEntity
    /// Tương tự FactGmvMaxProductDapperRepository nhưng cho Creative level analysis
    /// </summary>
    public class FactGmvMaxProductCreativeDapperRepository : DapperRepository<FactGmvMaxProductCreativeEntity, Guid>, IFactGmvMaxProductCreativeDapperRepository
    {
        public FactGmvMaxProductCreativeDapperRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        // ✅ Core Data Methods

        public async Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetListByDateRangeAsync(DateTime from, DateTime to)
        {
            return await GetListByDateRangeAsync(from, to, null);
        }

        public async Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetListByDateRangeAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds, string? currency = "USD")
        {
            // ✅ Xử lý filter AdvertiserId: null=admin (xem tất cả), empty=không có quyền, có data=filter theo quyền
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fpc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ Performance Optimization: Optimized query with proper indexing and caching
            string sql = $@"
                SELECT fpc.*
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc WITH (NOLOCK)
                WHERE fpc.DimDateId IN (
                    SELECT Id FROM [dbo].[Dim_DimDates] WITH (NOLOCK)
                    WHERE FullDate >= @From AND FullDate <= @To
                )
                {advertiserFilter}
                ORDER BY fpc.Date DESC, fpc.AdvertiserId, fpc.ItemId
                OPTION (RECOMPILE, OPTIMIZE FOR UNKNOWN)";

            return await QueryAsync<FactGmvMaxProductCreativeEntity>(sql, new { From = from.Date, To = to.Date }, commandTimeout: 300);
        }

        // ✅ NEW: Enhanced method with additional filters for better performance
        public async Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetListByDateRangeWithFiltersAsync(
            DateTime from, 
            DateTime to, 
            List<string>? allowedAdvertiserIds = null, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? creativeType = null,
            string? itemGroupId = null,
            string? itemId = null)
        {
            // ✅ Build dynamic WHERE conditions for optimal performance
            var whereConditions = new List<string>();
            var parameters = new Dictionary<string, object>
            {
                { "From", from.Date },
                { "To", to.Date }
            };

            // ✅ Date filter (always required)
            whereConditions.Add("fpc.DimDateId IN (SELECT Id FROM [dbo].[Dim_DimDates] WHERE FullDate >= @From AND FullDate <= @To)");

            // ✅ Advertiser filter (permission-based)
            if (allowedAdvertiserIds == null)
            {
                // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                whereConditions.Add("1=0"); // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                whereConditions.Add($"fpc.AdvertiserId IN ({advertiserIdList})");
            }

            // ✅ Campaign filter
            if (campaignIds != null && campaignIds.Any())
            {
                var campaignIdList = string.Join(",", campaignIds.Select(id => $"'{id}'"));
                whereConditions.Add($"fpc.CampaignId IN ({campaignIdList})");
            }

            // ✅ Store filter
            if (shopIds != null && shopIds.Any())
            {
                var shopIdList = string.Join(",", shopIds.Select(id => $"'{id}'"));
                whereConditions.Add($"fpc.StoreId IN ({shopIdList})");
            }

            // ✅ Search text filter - Search across all relevant string fields
            if (!string.IsNullOrEmpty(searchText))
            {
                whereConditions.Add(@"
                    (fpc.Title LIKE @SearchText 
                     OR fpc.StoreId LIKE @SearchText 
                     OR fpc.CampaignId LIKE @SearchText 
                     OR fpc.AdvertiserId LIKE @SearchText
                     OR fpc.BcId LIKE @SearchText
                     OR fpc.ItemGroupId LIKE @SearchText
                     OR fpc.ItemId LIKE @SearchText
                     OR fpc.CreativeType LIKE @SearchText
                     OR fpc.ShopContentType LIKE @SearchText
                     OR fpc.CreativeDeliveryStatus LIKE @SearchText
                     OR fpc.Currency LIKE @SearchText)");
                parameters["SearchText"] = $"%{searchText}%";
            }

            // ✅ Creative type filter
            if (!string.IsNullOrEmpty(creativeType))
            {
                whereConditions.Add("fpc.CreativeType = @CreativeType");
                parameters["CreativeType"] = creativeType;
            }

            // ✅ Item group ID filter
            if (!string.IsNullOrEmpty(itemGroupId))
            {
                whereConditions.Add("fpc.ItemGroupId = @ItemGroupId");
                parameters["ItemGroupId"] = itemGroupId;
            }

            // ✅ Item ID filter
            if (!string.IsNullOrEmpty(itemId))
            {
                whereConditions.Add("fpc.ItemId = @ItemId");
                parameters["ItemId"] = itemId;
            }

            // ✅ Build final SQL with dynamic WHERE clause
            var whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";
            
            string sql = $@"
                SELECT fpc.*
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc WITH (NOLOCK)
                {whereClause}
                ORDER BY fpc.Date DESC, fpc.AdvertiserId, fpc.ItemId
                OPTION (RECOMPILE, OPTIMIZE FOR UNKNOWN)";

            return await QueryAsync<FactGmvMaxProductCreativeEntity>(sql, parameters, commandTimeout: 300);
        }

        public async Task<IEnumerable<GmvMaxProductCreativeTrendDto>> GetTrendsAsync(DateTime from, DateTime to)
        {
            return await GetTrendsAsync(from, to, null);
        }

        public async Task<IEnumerable<GmvMaxProductCreativeTrendDto>> GetTrendsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds)
        {
            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fpc.AdvertiserId IN ({advertiserIdList})";
            }

            // ✅ Performance Optimization: Optimized aggregation query
            string sql = $@"
                SELECT fpc.DimDateId AS DateId,
                       SUM(ISNULL(fpc.GrossRevenueUSD, 0)) AS TotalGrossRevenue,
                       AVG(CASE WHEN fpc.ROAS IS NOT NULL THEN fpc.ROAS ELSE NULL END) AS AverageROAS,
                       AVG(CASE WHEN fpc.TACOS IS NOT NULL THEN fpc.TACOS ELSE NULL END) AS AverageTACOS,
                       SUM(fpc.Orders) AS TotalOrders,
                       COUNT(DISTINCT fpc.ItemId) AS CreativeCount,  -- Thay vì ProductCount
                       COUNT(DISTINCT fpc.StoreId) AS StoreCount,
                       COUNT(DISTINCT fpc.TtAccountName) AS AccountCount  -- TikTok Account count
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc WITH (NOLOCK)
                INNER JOIN [dbo].[Dim_DimDates] dd WITH (NOLOCK) ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @From AND dd.FullDate <= @To
                {advertiserFilter}
                GROUP BY fpc.DimDateId
                ORDER BY fpc.DimDateId
                OPTION (RECOMPILE, OPTIMIZE FOR UNKNOWN)";

            return await QueryAsync<GmvMaxProductCreativeTrendDto>(sql, new { From = from.Date, To = to.Date }, commandTimeout: 180);
        }

        public async Task<IEnumerable<GmvMaxProductCreativeTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit)
        {
            return await GetTopSellingAsync(from, to, limit, null);
        }

        public async Task<IEnumerable<GmvMaxProductCreativeTopSellingDto>> GetTopSellingAsync(DateTime from, DateTime to, int limit, List<string>? allowedAdvertiserIds)
        {
            // ✅ Xử lý filter AdvertiserId
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = ""; // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fpc.AdvertiserId IN ({advertiserIdList})";
            }

            string sql = $@"
                SELECT TOP (@Limit)
                       fpc.ItemGroupId,
                       fpc.ItemId,
                       MAX(fpc.Title) AS Title,
                       MAX(fpc.CreativeType) AS CreativeType,
                       MAX(fpc.TtAccountName) AS TtAccountName,
                       SUM(fpc.Orders) AS TotalOrders,
                       SUM(ISNULL(fpc.GrossRevenueUSD, 0)) AS TotalRevenue,
                       AVG(CASE WHEN fpc.ROAS IS NOT NULL THEN fpc.ROAS ELSE NULL END) AS AverageROAS,
                       AVG(ISNULL(fpc.Cost, 0)) AS AverageCost
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @From AND dd.FullDate <= @To
                {advertiserFilter}
                GROUP BY fpc.ItemGroupId, fpc.ItemId
                ORDER BY TotalOrders DESC";

            return await QueryAsync<GmvMaxProductCreativeTopSellingDto>(sql, new { From = from.Date, To = to.Date, Limit = limit });
        }

        // ✅ Dashboard Methods (tương tự Product nhưng cho Creative)

        public async Task<GmvMaxProductCreativeDashboardDto> GetDashboardAsync(string? currency = "USD")
        {
            return await GetDashboardAsync(currency, null);
        }

        public async Task<GmvMaxProductCreativeDashboardDto> GetDashboardAsync(string? currency, List<string>? allowedAdvertiserIds)
        {
            var now = DateTime.Now;
            var currentMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = currentMonth.AddMonths(-1);
            var lastMonthEnd = currentMonth.AddDays(-1);

            // Tính toán tuần đúng theo thứ 2 đến chủ nhật
            var currentWeekStart = GetWeekStart(now);
            var currentWeekEnd = currentWeekStart.AddDays(6);
            var oneWeekAgoStart = currentWeekStart.AddDays(-7);
            var oneWeekAgoEnd = oneWeekAgoStart.AddDays(6);
            var twoWeeksAgoStart = currentWeekStart.AddDays(-14);
            var twoWeeksAgoEnd = twoWeeksAgoStart.AddDays(6);

            // ✅ Tạo advertiser filter clause
            var advertiserFilter = "";
            if (allowedAdvertiserIds == null)
            {
                advertiserFilter = "";
            }
            else if (!allowedAdvertiserIds.Any())
            {
                advertiserFilter = "AND 1=0";
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                advertiserFilter = $"AND fpc.AdvertiserId IN ({advertiserIdList})";
            }

            // SQL cho doanh thu và chi tiêu theo tháng (12 tháng gần nhất) - với currency support
            string monthlySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month], 
                       SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fpc.Cost, 0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @FromDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate)
                ORDER BY [Year], [Month];";

            // SQL cho doanh thu và chi tiêu theo tuần
            string weeklySql = $@"
                SELECT YEAR(dd.FullDate) AS [Year], MONTH(dd.FullDate) AS [Month],
                       DATEDIFF(WEEK, 
                           DATEADD(DAY, -(DATEPART(WEEKDAY, dd.FullDate) + 5) % 7, 
                               DATEADD(DAY, 1-DATEPART(DAY, dd.FullDate), dd.FullDate)), 
                           dd.FullDate) + 1 AS [Week],
                       SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(ISNULL(fpc.Cost, 0)) AS TotalCost
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @FromDate
                {advertiserFilter}
                GROUP BY YEAR(dd.FullDate), MONTH(dd.FullDate),
                         DATEDIFF(WEEK, 
                             DATEADD(DAY, -(DATEPART(WEEKDAY, dd.FullDate) + 5) % 7, 
                                 DATEADD(DAY, 1-DATEPART(DAY, dd.FullDate), dd.FullDate)), 
                             dd.FullDate) + 1
                ORDER BY [Year], [Month], [Week];";

            // SQL cho xếp hạng shop theo tuần (doanh thu) - với currency support
            string shopRankingSql = $@"
                SELECT TOP 10 fpc.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fpc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fpc.ROAS IS NOT NULL THEN fpc.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fpc.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fpc.StoreId
                ORDER BY TotalRevenue DESC;";

            // SQL cho xếp hạng shop theo tuần (chi tiêu) - với currency support
            string shopCostRankingSql = $@"
                SELECT TOP 10 fpc.StoreId, MAX(ds.StoreName) AS StoreName,
                       SUM(ISNULL(fpc.Cost, 0)) AS TotalCost,
                       SUM(fpc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fpc.ROAS IS NOT NULL THEN fpc.ROAS ELSE NULL END) AS AverageROAS
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                LEFT JOIN [dbo].[Dim_DimStores] ds ON ds.Id = fpc.DimStoreId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fpc.StoreId
                ORDER BY TotalCost DESC;";

            // ✅ NEW: SQL cho xếp hạng Creative theo tuần
            string creativeRankingSql = $@"
                SELECT TOP 10 fpc.ItemGroupId, fpc.ItemId, MAX(fpc.Title) AS Title,
                       MAX(fpc.CreativeType) AS CreativeType, MAX(fpc.TtAccountName) AS TtAccountName,
                       SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fpc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fpc.ROAS IS NOT NULL THEN fpc.ROAS ELSE NULL END) AS AverageROAS,
                       AVG(ISNULL(fpc.Cost, 0)) AS AverageCost,
                       MAX(fpc.ShopContentType) AS ShopContentType
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {advertiserFilter}
                GROUP BY fpc.ItemGroupId, fpc.ItemId
                ORDER BY TotalRevenue DESC;";

            // ✅ NEW: SQL cho xếp hạng TikTok Account theo tuần
            string accountRankingSql = $@"
                SELECT TOP 10 fpc.TtAccountName, MAX(fpc.TtAccountAuthorizationType) AS TtAccountAuthorizationType,
                       SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")},0)) AS TotalRevenue,
                       SUM(fpc.Orders) AS TotalOrders,
                       AVG(CASE WHEN fpc.ROAS IS NOT NULL THEN fpc.ROAS ELSE NULL END) AS AverageROAS,
                       AVG(ISNULL(fpc.Cost, 0)) AS AverageCost,
                       COUNT(DISTINCT fpc.ItemId) AS CreativeCount,
                       COUNT(DISTINCT fpc.ItemGroupId) AS ProductCount
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                AND fpc.TtAccountName IS NOT NULL AND fpc.TtAccountName != ''
                {advertiserFilter}
                GROUP BY fpc.TtAccountName
                ORDER BY TotalRevenue DESC;";

            // Lấy dữ liệu 12 tháng gần nhất
            var fromDate = now.AddMonths(-12);
            var monthly = await QueryAsync<DashboardMonthly>(monthlySql, new { FromDate = fromDate.Date });
            var weekly = await QueryAsync<DashboardWeekly>(weeklySql, new { FromDate = fromDate.Date });

            // Lấy xếp hạng shop tuần hiện tại (doanh thu)
            var currentWeekRanking = await QueryAsync<DashboardShopRanking>(shopRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Lấy xếp hạng shop 2 tuần trước (doanh thu)
            var twoWeeksAgoRanking = await QueryAsync<DashboardShopRanking>(shopRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // Lấy xếp hạng shop 1 tuần trước (doanh thu)
            var oneWeekAgoRanking = await QueryAsync<DashboardShopRanking>(shopRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // Lấy xếp hạng shop tuần hiện tại (chi tiêu)
            var currentWeekCostRanking = await QueryAsync<DashboardShopCostRanking>(shopCostRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Lấy xếp hạng shop 2 tuần trước (chi tiêu)
            var twoWeeksAgoCostRanking = await QueryAsync<DashboardShopCostRanking>(shopCostRankingSql,
                new { FromDate = twoWeeksAgoStart.Date, ToDate = twoWeeksAgoEnd.Date });

            // Lấy xếp hạng shop 1 tuần trước (chi tiêu)
            var oneWeekAgoCostRanking = await QueryAsync<DashboardShopCostRanking>(shopCostRankingSql,
                new { FromDate = oneWeekAgoStart.Date, ToDate = oneWeekAgoEnd.Date });

            // ✅ NEW: Lấy xếp hạng Creative và Account
            var currentWeekCreativeRanking = await QueryAsync<DashboardCreativeRanking>(creativeRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });
            var currentWeekAccountRanking = await QueryAsync<DashboardAccountRanking>(accountRankingSql,
                new { FromDate = currentWeekStart.Date, ToDate = currentWeekEnd.Date });

            // Tính toán doanh thu tháng hiện tại và tháng trước
            decimal currentMonthTotalRevenue = 0, lastMonthTotalRevenue = 0;
            decimal currentMonthTotalCost = 0, lastMonthTotalCost = 0;
            foreach (var m in monthly)
            {
                if (m.Year == now.Year && m.Month == now.Month) 
                {
                    currentMonthTotalRevenue = m.TotalRevenue;
                    currentMonthTotalCost = m.TotalCost;
                }
                if (m.Year == lastMonth.Year && m.Month == lastMonth.Month) 
                {
                    lastMonthTotalRevenue = m.TotalRevenue;
                    lastMonthTotalCost = m.TotalCost;
                }
            }

            return new GmvMaxProductCreativeDashboardDto
            {
                CurrentMonth = new DashboardMonthInfo { Year = now.Year, Month = now.Month, TotalRevenue = currentMonthTotalRevenue, TotalCost = currentMonthTotalCost },
                LastMonth = new DashboardMonthInfo { Year = lastMonth.Year, Month = lastMonth.Month, TotalRevenue = lastMonthTotalRevenue, TotalCost = lastMonthTotalCost },
                WeeklyData = new List<DashboardWeekly>(weekly),
                MonthlyData = new List<DashboardMonthly>(monthly),
                CurrentWeekShopRanking = new List<DashboardShopRanking>(currentWeekRanking),
                TwoWeeksAgoShopRanking = new List<DashboardShopRanking>(twoWeeksAgoRanking),
                OneWeekAgoShopRanking = new List<DashboardShopRanking>(oneWeekAgoRanking),
                // Cost rankings
                CurrentWeekShopCostRanking = new List<DashboardShopCostRanking>(currentWeekCostRanking),
                TwoWeeksAgoShopCostRanking = new List<DashboardShopCostRanking>(twoWeeksAgoCostRanking),
                OneWeekAgoShopCostRanking = new List<DashboardShopCostRanking>(oneWeekAgoCostRanking),
                // ✅ NEW: Creative-specific rankings
                CurrentWeekCreativeRanking = new List<DashboardCreativeRanking>(currentWeekCreativeRanking),
                CurrentWeekAccountRanking = new List<DashboardAccountRanking>(currentWeekAccountRanking)
            };
        }

        // Helper methods (copy từ FactGmvMaxProductDapperRepository)
        private DateTime GetWeekStartInMonth(DateTime date)
        {
            var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);
            var dayOfWeek = (int)firstDayOfMonth.DayOfWeek;
            var weekStart = firstDayOfMonth.AddDays(-dayOfWeek + 1);

            while (weekStart.AddDays(6) < date)
            {
                weekStart = weekStart.AddDays(7);
            }

            return weekStart;
        }

        private DateTime GetWeekStart(DateTime date)
        {
            var daysToSubtract = ((int)date.DayOfWeek + 6) % 7;
            return date.AddDays(-daysToSubtract).Date;
        }

        // ✅ Implement remaining methods (sẽ tiếp tục trong phần sau)
        // Note: File này sẽ rất dài, tôi sẽ tạo phần còn lại trong các bước tiếp theo
        
        public Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public async Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD", 
            List<string>? allowedAdvertiserIds = null,
            string? creativeType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null,
            string? itemGroupId = null,
            string? itemId = null)
        {
            // ✅ Use provided date range or default to current month
            var now = DateTime.Now;
            var from = fromDate ?? new DateTime(now.Year, now.Month, 1);
            var to = toDate ?? new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59);

            // ✅ Build dynamic filters
            var filters = new List<string>();
            var parameters = new Dictionary<string, object>
            {
                { "FromDate", from },
                { "ToDate", to }
            };

            // ✅ Advertiser filter
            if (allowedAdvertiserIds == null)
            {
                // Admin - có thể xem tất cả dữ liệu
            }
            else if (!allowedAdvertiserIds.Any())
            {
                filters.Add("1=0"); // User không có quyền gì
            }
            else
            {
                var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
                filters.Add($"fpc.AdvertiserId IN ({advertiserIdList})");
            }

            // ✅ Creative Type filter
            if (!string.IsNullOrEmpty(creativeType))
            {
                filters.Add("fpc.CreativeType = @CreativeType");
                parameters.Add("CreativeType", creativeType);
            }

            // ✅ Search text filter - Search across all relevant string fields
            if (!string.IsNullOrEmpty(searchText))
            {
                filters.Add(@"
                    (fpc.Title LIKE @SearchText 
                     OR fpc.StoreId LIKE @SearchText 
                     OR fpc.CampaignId LIKE @SearchText 
                     OR fpc.AdvertiserId LIKE @SearchText
                     OR fpc.BcId LIKE @SearchText
                     OR fpc.ItemGroupId LIKE @SearchText
                     OR fpc.ItemId LIKE @SearchText
                     OR fpc.CreativeType LIKE @SearchText
                     OR fpc.ShopContentType LIKE @SearchText
                     OR fpc.CreativeDeliveryStatus LIKE @SearchText
                     OR fpc.Currency LIKE @SearchText)");
                parameters.Add("SearchText", $"%{searchText}%");
            }

            // ✅ Shop IDs filter
            if (shopIds != null && shopIds.Any())
            {
                var shopIdList = string.Join(",", shopIds.Select(id => $"'{id}'"));
                filters.Add($"fpc.StoreId IN ({shopIdList})");
            }

            // ✅ Campaign IDs filter
            if (campaignIds != null && campaignIds.Any())
            {
                var campaignIdList = string.Join(",", campaignIds.Select(id => $"'{id}'"));
                filters.Add($"fpc.CampaignId IN ({campaignIdList})");
            }

            // ✅ Item Group ID filter
            if (!string.IsNullOrEmpty(itemGroupId))
            {
                filters.Add("fpc.ItemGroupId = @ItemGroupId");
                parameters.Add("ItemGroupId", itemGroupId);
            }

            // ✅ Item ID filter
            if (!string.IsNullOrEmpty(itemId))
            {
                filters.Add("fpc.ItemId = @ItemId");
                parameters.Add("ItemId", itemId);
            }

            var whereClause = filters.Any() ? "AND " + string.Join(" AND ", filters) : "";

            // ✅ OPTIMIZED: Sử dụng currency field để tăng performance
            string sql = $@"
                SELECT 
                    SUM(ISNULL(fpc.Cost, 0)) AS TotalCost,
                    SUM(ISNULL(fpc.Cost, 0)) AS TotalNetCost,
                    SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) AS TotalGrossRevenue,
                    SUM(ISNULL(fpc.Orders, 0)) AS TotalOrders,
                    COUNT(DISTINCT fpc.ItemId) AS ProductCount,
                    COUNT(DISTINCT fpc.StoreId) AS ActiveStores,
                    COUNT(DISTINCT fpc.AdvertiserId) AS ActiveAdvertisers,
                    CASE 
                        WHEN SUM(ISNULL(fpc.Cost, 0)) > 0 
                        THEN SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) / SUM(ISNULL(fpc.Cost, 0))
                        ELSE 0 
                    END AS AverageROAS,
                    CASE 
                        WHEN SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0)) > 0 
                        THEN SUM(ISNULL(fpc.Cost, 0)) / SUM(ISNULL(fpc.GrossRevenue{(currency?.ToUpper() == "VND" ? "VND" : "USD")}, 0))
                        ELSE 0 
                    END AS AverageTACOS
                FROM [dbo].[Fact_FactGmvMaxProductCreatives] fpc
                INNER JOIN [dbo].[Dim_DimDates] dd ON dd.Id = fpc.DimDateId
                WHERE dd.FullDate >= @FromDate AND dd.FullDate <= @ToDate
                {whereClause}";

            var result = await QueryAsync<dynamic>(sql, parameters.ToDictionary(p => p.Key, p => p.Value));
            var firstResult = result.FirstOrDefault();

            return new SummaryCardsDto
            {
                TotalCost = (decimal)(firstResult?.TotalCost ?? 0),
                TotalNetCost = (decimal)(firstResult?.TotalNetCost ?? 0),
                TotalGrossRevenue = (decimal)(firstResult?.TotalGrossRevenue ?? 0),
                TotalOrders = (int)(firstResult?.TotalOrders ?? 0),
                AverageROAS = (decimal)(firstResult?.AverageROAS ?? 0),
                AverageTACOS = (decimal)(firstResult?.AverageTACOS ?? 0),
                CampaignCount = (int)(firstResult?.ProductCount ?? 0), // Using ProductCount as Creative count
                ActiveStores = (int)(firstResult?.ActiveStores ?? 0),
                ActiveAdvertisers = (int)(firstResult?.ActiveAdvertisers ?? 0),
                Currency = currency ?? "USD",
                GeneratedAt = DateTime.UtcNow
            };
        }

        public Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<DetailedChartsDto> GetDetailedChartsAsync(List<string>? allowedAdvertiserIds = null)
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD", List<string>? allowedAdvertiserIds = null)
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativePerformanceAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<GmvMaxProductCreativeDeliveryDto>> GetCreativeDeliveryAnalysisAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<GmvMaxProductCreativeContentTypeDto>> GetContentTypeAnalysisAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<DashboardCreativeRanking>> GetCreativeRankingAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<DashboardAccountRanking>> GetAccountRankingAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByCreativeTypeAsync(DateTime from, DateTime to, string creativeType, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByContentTypeAsync(DateTime from, DateTime to, string shopContentType, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByDeliveryStatusAsync(DateTime from, DateTime to, string deliveryStatus, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByTikTokAccountAsync(DateTime from, DateTime to, string ttAccountName, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<FactGmvMaxProductCreativeEntity>> GetByProductGroupAsync(DateTime from, DateTime to, string itemGroupId, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetLowPerformanceCreativesAsync(DateTime from, DateTime to, decimal roasThreshold = 1.5m, decimal tacosThreshold = 30.0m, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetHighPerformanceCreativesAsync(DateTime from, DateTime to, decimal roasThreshold = 3.0m, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }

        public Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativeAlertsAsync(DateTime from, DateTime to, List<string>? allowedAdvertiserIds = null, string? currency = "USD")
        {
            throw new NotImplementedException("Sẽ implement trong bước tiếp theo");
        }
    }
}

