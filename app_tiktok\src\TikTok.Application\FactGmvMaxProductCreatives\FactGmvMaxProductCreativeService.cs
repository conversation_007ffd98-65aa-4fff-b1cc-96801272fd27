using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Application.Services;
using TikTok.FactGmvMaxProductCreatives.Dtos;
using TikTok.DimProducts;
using TikTok.DimStores;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Entities.Dim;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using TikTok.DimDates;
using TikTok.DimTTAccounts;
using Microsoft.Extensions.Logging;
using TikTok.Domain.Repositories;
using TikTok.Facts.FactGmvMaxProductCreative;
using TikTok.Permissions;
using Volo.Abp.Authorization;
using Volo.Abp.Authorization.Permissions;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.FactGmvMaxProductCreatives
{
    /// <summary>
    /// Service cho FactGmvMaxProductCreative - tương tự FactGmvMaxProductService nhưng cho Creative level
    /// </summary>
    public class FactGmvMaxProductCreativeService : ApplicationService, IFactGmvMaxProductCreativeService
    {
        private readonly IRepository<FactGmvMaxProductCreativeEntity, Guid> _factGmvMaxProductCreativeRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;
        private readonly IRepository<DimProductEntity, Guid> _dimProductRepository;
        private readonly IRepository<DimTTAccountEntity, Guid> _dimTTAccountRepository;
        private readonly IFactGmvMaxProductCreativeDapperRepository _factGmvMaxProductCreativeDapperRepository;
        private readonly IPermissionChecker _permissionChecker;
        private readonly PermissionFieldHelper _permissionFieldHelper;

        public FactGmvMaxProductCreativeService(
            IRepository<FactGmvMaxProductCreativeEntity, Guid> factGmvMaxProductCreativeRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IRepository<DimStoreEntity, Guid> dimStoreRepository,
            IRepository<DimProductEntity, Guid> dimProductRepository,
            IRepository<DimTTAccountEntity, Guid> dimTTAccountRepository,
            IFactGmvMaxProductCreativeDapperRepository factGmvMaxProductCreativeDapperRepository,
            IPermissionChecker permissionChecker,
            PermissionFieldHelper permissionFieldHelper)
        {
            _factGmvMaxProductCreativeRepository = factGmvMaxProductCreativeRepository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _dimCampaignRepository = dimCampaignRepository;
            _dimStoreRepository = dimStoreRepository;
            _dimProductRepository = dimProductRepository;
            _dimTTAccountRepository = dimTTAccountRepository;
            _factGmvMaxProductCreativeDapperRepository = factGmvMaxProductCreativeDapperRepository;
            _permissionChecker = permissionChecker;
            _permissionFieldHelper = permissionFieldHelper;
        }

        // ✅ Core Data Methods

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetListAsync(DateTime fromDate, DateTime toDate, string? currency = "USD", bool hasViewAllAdvertisers = false)
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                var result = new GetFactGmvMaxProductCreativeDataResponse()
                {
                    From = fromDate,
                    To = toDate,
                    Currency = currency,
                    FactGmvMaxProductCreatives = new List<FactGmvMaxProductCreativeDto>(),
                    DimAdAccounts = new List<DimAdAccountDto>(),
                    DimBusinessCenters = new List<DimBusinessCenterDto>(),
                    DimCampaigns = new List<DimCampaignDto>(),
                    DimStores = new List<DimStoreDto>(),
                    DimProducts = new List<DimProductDto>(),
                    DimTTAccounts = new List<DimTTAccountDto>(),
                    DimDates = new List<DimDateDto>()
                };

                // ✅ Use Dapper repository with permission filtering and currency
                var factGmvMaxProductCreatives = await _factGmvMaxProductCreativeDapperRepository.GetListByDateRangeAsync(fromDate, toDate, allowedAdvertiserIds, currency);
                var factList = factGmvMaxProductCreatives is List<FactGmvMaxProductCreativeEntity> list ? list : new List<FactGmvMaxProductCreativeEntity>(factGmvMaxProductCreatives);

                if (!factList.Any())
                {
                    // Return empty response if no data found
                    Logger.LogWarning($"No FactGmvMaxProductCreative data found for date range {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                    return result;
                }

                // Map fact data
                result.FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(factList);

                // Get all unique DimDateId values from factGmvMaxProductCreatives
                var dimDateIds = factList.Select(f => f.DimDateId).Distinct().ToList();
                var dimDates = await _dimDateRepository.GetListAsync(d => dimDateIds.Contains(d.Id));
                result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDateDto>>(dimDates);

                // Get all unique DimAdAccountId values from factGmvMaxProductCreatives
                var dimAdAccountIds = factList.Select(f => f.DimAdAccountId).Distinct().ToList();
                var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(d => dimAdAccountIds.Contains(d.Id));
                result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccountDto>>(dimAdAccounts);

                // Get all unique DimBusinessCenterId values from factGmvMaxProductCreatives
                var dimBusinessCenterIds = factList.Select(f => f.DimBusinessCenterId).Distinct().ToList();
                var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(d => dimBusinessCenterIds.Contains(d.Id));
                result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenterDto>>(dimBusinessCenters);

                // Get all unique DimCampaignId values from factGmvMaxProductCreatives
                var dimCampaignIds = factList.Select(f => f.DimCampaignId).Distinct().ToList();
                var dimCampaigns = await _dimCampaignRepository.GetListAsync(d => dimCampaignIds.Contains(d.Id));
                result.DimCampaigns = ObjectMapper.Map<List<DimCampaignEntity>, List<DimCampaignDto>>(dimCampaigns);

                // Get all unique DimStoreId values from factGmvMaxProductCreatives
                var dimStoreIds = factList.Select(f => f.DimStoreId).Distinct().ToList();
                var dimStores = await _dimStoreRepository.GetListAsync(d => dimStoreIds.Contains(d.Id));
                result.DimStores = ObjectMapper.Map<List<DimStoreEntity>, List<DimStoreDto>>(dimStores);

                // Get all unique DimProductId values from factGmvMaxProductCreatives
                var dimProductIds = factList.Select(f => f.DimProductId).Distinct().ToList();
                var dimProducts = await _dimProductRepository.GetListAsync(d => dimProductIds.Contains(d.Id));
                result.DimProducts = ObjectMapper.Map<List<DimProductEntity>, List<DimProductDto>>(dimProducts);

                // ✅ NEW: Get all unique DimTTAccountId values from factGmvMaxProductCreatives
                var dimTTAccountIds = factList.Where(f => f.DimTTAccountId.HasValue).Select(f => f.DimTTAccountId.Value).Distinct().ToList();
                if (dimTTAccountIds.Any())
                {
                    var dimTTAccounts = await _dimTTAccountRepository.GetListAsync(d => dimTTAccountIds.Contains(d.Id));
                    result.DimTTAccounts = ObjectMapper.Map<List<DimTTAccountEntity>, List<DimTTAccountDto>>(dimTTAccounts);
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative list for date range {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            // ✅ Check if user has permission to view all advertisers
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);
            return await GetListAsync(fromDate, toDate, currency, hasViewAllAdvertisers);
        }

        // ✅ NEW: Enhanced method with additional filters for better performance
        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetListWithFiltersAsync(
            DateTime fromDate, 
            DateTime toDate, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? creativeType = null,
            string? itemGroupId = null,
            string? itemId = null)
        {
            try
            {
                // Check permissions
                var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // If no permissions, throw authorization exception
                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    throw new AbpAuthorizationException("You don't have permission to view GMV Max ProductCreative data");
                }

                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                var result = new GetFactGmvMaxProductCreativeDataResponse()
                {
                    From = fromDate,
                    To = toDate,
                    Currency = currency,
                    FactGmvMaxProductCreatives = new List<FactGmvMaxProductCreativeDto>(),
                    DimAdAccounts = new List<DimAdAccountDto>(),
                    DimBusinessCenters = new List<DimBusinessCenterDto>(),
                    DimCampaigns = new List<DimCampaignDto>(),
                    DimStores = new List<DimStoreDto>(),
                    DimProducts = new List<DimProductDto>(),
                    DimTTAccounts = new List<DimTTAccountDto>(),
                    DimDates = new List<DimDateDto>()
                };

                // ✅ TỐI ƯU: Get data with filters at database level
                var factGmvMaxProductCreatives = await _factGmvMaxProductCreativeDapperRepository.GetListByDateRangeWithFiltersAsync(
                    fromDate, toDate, allowedAdvertiserIds, currency,
                    campaignIds, shopIds, searchText, creativeType, itemGroupId, itemId);

                if (!factGmvMaxProductCreatives.Any())
                {
                    // Return empty response if no data found
                    Logger.LogWarning($"No FactGmvMaxProductCreative data found for date range {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd} with filters");
                    return result;
                }

                // Map fact data
                var factList = factGmvMaxProductCreatives is List<FactGmvMaxProductCreativeEntity> list ? list : new List<FactGmvMaxProductCreativeEntity>(factGmvMaxProductCreatives);
                result.FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(factList);

                // Get all unique DimDateId values from factGmvMaxProductCreatives
                var uniqueDimDateIds = factList.Select(f => f.DimDateId).Distinct().ToList();

                // Get all dim-dates that are included in factGmvMaxProductCreatives data
                if (uniqueDimDateIds.Any())
                {
                    var dimDates = await _dimDateRepository.GetListAsync(d => uniqueDimDateIds.Contains(d.Id));
                    result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDateDto>>(dimDates);
                }

                // Get all unique DimBusinessCenterId values from factGmvMaxProductCreatives
                var uniqueDimBusinessCenterIds = factList.Select(f => f.DimBusinessCenterId).Distinct().ToList();

                // Get all dim-business-centers that are included in factGmvMaxProductCreatives data
                if (uniqueDimBusinessCenterIds.Any())
                {
                    var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(d => uniqueDimBusinessCenterIds.Contains(d.Id));
                    result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenterDto>>(dimBusinessCenters);
                }

                // Get all unique DimAdAccountId values from factGmvMaxProductCreatives
                var uniqueDimAdAccountIds = factList.Select(f => f.DimAdAccountId).Distinct().ToList();

                // Get all dim-ad-accounts that are included in factGmvMaxProductCreatives data
                if (uniqueDimAdAccountIds.Any())
                {
                    var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(d => uniqueDimAdAccountIds.Contains(d.Id));
                    result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccountDto>>(dimAdAccounts);
                }

                // Get all unique DimCampaignId values from factGmvMaxProductCreatives
                var uniqueDimCampaignIds = factList.Select(f => f.DimCampaignId).Distinct().ToList();

                // Get all dim-campaigns that are included in factGmvMaxProductCreatives data
                if (uniqueDimCampaignIds.Any())
                {
                    var dimCampaigns = await _dimCampaignRepository.GetListAsync(d => uniqueDimCampaignIds.Contains(d.Id));
                    result.DimCampaigns = ObjectMapper.Map<List<DimCampaignEntity>, List<DimCampaignDto>>(dimCampaigns);
                }

                // Get all unique DimStoreId values from factGmvMaxProductCreatives
                var uniqueDimStoreIds = factList.Select(f => f.DimStoreId).Distinct().ToList();

                // Get all dim-stores that are included in factGmvMaxProductCreatives data
                if (uniqueDimStoreIds.Any())
                {
                    var dimStores = await _dimStoreRepository.GetListAsync(d => uniqueDimStoreIds.Contains(d.Id));
                    result.DimStores = ObjectMapper.Map<List<DimStoreEntity>, List<DimStoreDto>>(dimStores);
                }

                // Get all unique DimProductId values from factGmvMaxProductCreatives
                var uniqueDimProductIds = factList.Select(f => f.DimProductId).Distinct().ToList();

                // Get all dim-products that are included in factGmvMaxProductCreatives data
                if (uniqueDimProductIds.Any())
                {
                    var dimProducts = await _dimProductRepository.GetListAsync(d => uniqueDimProductIds.Contains(d.Id));
                    result.DimProducts = ObjectMapper.Map<List<DimProductEntity>, List<DimProductDto>>(dimProducts);
                }

                // Get all unique DimTTAccountId values from factGmvMaxProductCreatives
                var uniqueDimTTAccountIds = factList.Where(f => f.DimTTAccountId.HasValue).Select(f => f.DimTTAccountId.Value).Distinct().ToList();

                // Get all dim-tt-accounts that are included in factGmvMaxProductCreatives data
                if (uniqueDimTTAccountIds.Any())
                {
                    var dimTTAccounts = await _dimTTAccountRepository.GetListAsync(d => uniqueDimTTAccountIds.Contains(d.Id));
                    result.DimTTAccounts = ObjectMapper.Map<List<DimTTAccountEntity>, List<DimTTAccountDto>>(dimTTAccounts);
                }

                // Filter data based on permissions
                if (hasViewAll || hasViewAllAdvertisers)
                {
                    // ViewAll or ViewAllAdvertisers - return all data as is
                    Logger.LogInformation($"User has ViewAll or ViewAllAdvertisers permission - returning {result.FactGmvMaxProductCreatives.Count} records with filters");
                    return result;
                }

                // Filter fields based on permissions
                var allowedFields = PermissionFieldHelper.GetAllowedFields(hasViewSpending, hasViewMetrics, hasViewAll);
                
                // Create filtered DTOs
                var filteredCreatives = new List<FactGmvMaxProductCreativeDto>();
                foreach (var creative in result.FactGmvMaxProductCreatives)
                {
                    var filteredCreative = FilterCreativeDtoByPermissions(creative, allowedFields);
                    filteredCreatives.Add(filteredCreative);
                }

                result.FactGmvMaxProductCreatives = filteredCreatives;

                Logger.LogInformation($"Filtered GMV Max ProductCreative data with filters - returning {filteredCreatives.Count} records with {allowedFields.Count} allowed fields");

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max ProductCreative data with filters from {From} to {To}", fromDate, toDate);
                throw new ValidationException($"Error retrieving GMV Max ProductCreative data with filters: {ex.Message}");
            }
        }

        public async Task<FactGmvMaxProductCreativeSummaryDto> GetSummaryAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                var factGmvMaxProductCreatives = await _factGmvMaxProductCreativeDapperRepository.GetListByDateRangeAsync(fromDate, toDate, allowedAdvertiserIds, "USD");
                var factList = factGmvMaxProductCreatives is List<FactGmvMaxProductCreativeEntity> list ? list : new List<FactGmvMaxProductCreativeEntity>(factGmvMaxProductCreatives);

                if (!factList.Any())
                {
                    return new FactGmvMaxProductCreativeSummaryDto
                    {
                        FromDate = fromDate,
                        ToDate = toDate,
                        TotalRecords = 0
                    };
                }

                // Calculate summary metrics
                var summary = new FactGmvMaxProductCreativeSummaryDto
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalRecords = factList.Count,
                    
                    // Financial Metrics
                    TotalCostUSD = factList.Sum(f => f.Cost),
                    TotalGrossRevenueUSD = factList.Sum(f => f.GrossRevenueUSD ?? 0),
                    TotalAdsRevenueUSD = factList.Where(f => f.CreativeType == "ADS_AND_ORGANIC").Sum(f => f.GrossRevenueUSD ?? 0),
                    TotalOrganicRevenueUSD = factList.Where(f => f.CreativeType == "ORGANIC").Sum(f => f.GrossRevenueUSD ?? 0),
                    AvgCostPerOrderUSD = factList.Where(f => f.Orders > 0).Average(f => f.CostPerOrderUSD ?? 0m),
                    
                    // Performance Metrics
                    AverageROAS = factList.Where(f => f.ROAS.HasValue).Average(f => f.ROAS.Value),
                    AverageTACOS = factList.Where(f => f.TACOS.HasValue).Average(f => f.TACOS.Value),
                    AverageCTR = factList.Where(f => f.AdClickRate.HasValue).Average(f => f.AdClickRate.Value),
                    AverageConversionRate = factList.Where(f => f.AdConversionRate.HasValue).Average(f => f.AdConversionRate.Value),
                    
                    // Volume Metrics
                    TotalOrders = factList.Sum(f => f.Orders),
                    TotalImpressions = factList.Sum(f => f.ProductImpressions ?? 0),
                    TotalClicks = factList.Sum(f => f.ProductClicks ?? 0),
                    
                    // Creative Metrics
                    UniqueCreatives = factList.Select(f => f.ItemId).Distinct().Count(),
                    UniqueCampaigns = factList.Select(f => f.CampaignId).Distinct().Count(),
                    UniqueStores = factList.Select(f => f.StoreId).Distinct().Count(),
                    UniqueAdAccounts = factList.Select(f => f.AdvertiserId).Distinct().Count(),
                    UniqueTikTokAccounts = factList.Where(f => f.DimTTAccountId.HasValue).Select(f => f.DimTTAccountId.Value).Distinct().Count(),
                    UniqueProductGroups = factList.Select(f => f.ItemGroupId).Distinct().Count(),
                    UniqueDates = factList.Select(f => f.DimDateId).Distinct().Count(),
                    
                    // Performance Classification
                    ExcellentROASCreatives = factList.Count(f => f.ROAS > 3.0m),
                    GoodROASCreatives = factList.Count(f => f.ROAS >= 2.0m && f.ROAS <= 3.0m),
                    PoorROASCreatives = factList.Count(f => f.ROAS < 2.0m),
                    HighACOSCreatives = factList.Count(f => f.TACOS.HasValue && f.TACOS.Value > 0.5m),
                    HighProfitCreatives = factList.Count(f => f.TACOS.HasValue && f.TACOS.Value < 0.2m),
                    
                    // ✅ NEW: Creative-specific metrics
                    TopRevenueCreativeId = factList.OrderByDescending(f => f.GrossRevenueUSD ?? 0).FirstOrDefault()?.ItemId ?? string.Empty,
                    TopROASCreativeId = factList.OrderByDescending(f => f.ROAS ?? 0).FirstOrDefault()?.ItemId ?? string.Empty,
                    BestSellingCreativeId = factList.OrderByDescending(f => f.Orders).FirstOrDefault()?.ItemId ?? string.Empty,
                    // Note: TtAccountName không có trong entity, sử dụng DimTTAccountId
                    TopPerformingTikTokAccount = factList.Where(f => f.DimTTAccountId.HasValue)
                        .GroupBy(f => f.DimTTAccountId.Value)
                        .OrderByDescending(g => g.Sum(f => f.GrossRevenueUSD ?? 0))
                        .FirstOrDefault()?.Key.ToString() ?? string.Empty,
                    TopPerformingCreativeType = factList.GroupBy(f => f.CreativeType)
                        .OrderByDescending(g => g.Sum(f => f.GrossRevenueUSD ?? 0))
                        .FirstOrDefault()?.Key ?? string.Empty,
                    TopPerformingContentType = factList.GroupBy(f => f.ShopContentType)
                        .OrderByDescending(g => g.Sum(f => f.GrossRevenueUSD ?? 0))
                        .FirstOrDefault()?.Key ?? string.Empty,
                    
                    // ✅ NEW: Delivery Status distribution
                    ActiveCreatives = factList.Count(f => f.CreativeDeliveryStatus == "ACTIVE"),
                    PausedCreatives = factList.Count(f => f.CreativeDeliveryStatus == "PAUSED"),
                    DisabledCreatives = factList.Count(f => f.CreativeDeliveryStatus == "DISABLED"),
                    
                    // ✅ NEW: Video Performance Metrics
                    AverageVideoViewRate2s = factList.Where(f => f.AdVideoViewRate2s.HasValue).Average(f => f.AdVideoViewRate2s.Value),
                    AverageVideoViewRate6s = factList.Where(f => f.AdVideoViewRate6s.HasValue).Average(f => f.AdVideoViewRate6s.Value),
                    AverageVideoViewRateP25 = factList.Where(f => f.AdVideoViewRateP25.HasValue).Average(f => f.AdVideoViewRateP25.Value),
                    AverageVideoViewRateP50 = factList.Where(f => f.AdVideoViewRateP50.HasValue).Average(f => f.AdVideoViewRateP50.Value),
                    AverageVideoViewRateP75 = factList.Where(f => f.AdVideoViewRateP75.HasValue).Average(f => f.AdVideoViewRateP75.Value),
                    AverageVideoViewRateP100 = factList.Where(f => f.AdVideoViewRateP100.HasValue).Average(f => f.AdVideoViewRateP100.Value),
                    AverageProductClickRate = factList.Where(f => f.ProductClickRate.HasValue).Average(f => f.ProductClickRate.Value)
                };

                return summary;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative summary for date range {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        // ✅ Implement remaining methods (sẽ tiếp tục trong phần sau)
        // Note: File này sẽ rất dài, tôi sẽ implement các methods còn lại trong các bước tiếp theo
        
        public async Task<IEnumerable<GmvMaxProductCreativeTrendDto>> GetTrendsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get trends data from Dapper repository
                var trendsData = await _factGmvMaxProductCreativeDapperRepository.GetTrendsAsync(fromDate, toDate, allowedAdvertiserIds);

                return trendsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative trends from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativeTopSellingDto>> GetTopSellingAsync(DateTime fromDate, DateTime toDate, int limit)
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get top selling data from Dapper repository
                var topSellingData = await _factGmvMaxProductCreativeDapperRepository.GetTopSellingAsync(fromDate, toDate, limit, allowedAdvertiserIds);

                return topSellingData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative top selling from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd} with limit {limit}");
                throw;
            }
        }

        public async Task<GmvMaxProductCreativeDashboardDto> GetDashboardAsync(string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get dashboard data from Dapper repository
                var dashboardData = await _factGmvMaxProductCreativeDapperRepository.GetDashboardAsync(currency, allowedAdvertiserIds);

                return dashboardData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative dashboard with currency {currency}");
                throw;
            }
        }

        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get dashboard summary data from Dapper repository
                var summaryData = await _factGmvMaxProductCreativeDapperRepository.GetDashboardSummaryAsync(currency, allowedAdvertiserIds);

                return summaryData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative dashboard summary with currency {currency}");
                throw;
            }
        }

        public async Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get detailed analysis data from Dapper repository
                var detailedData = await _factGmvMaxProductCreativeDapperRepository.GetDetailedAnalysisDataAsync(currency, allowedAdvertiserIds);

                return detailedData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative detailed analysis with currency {currency}");
                throw;
            }
        }

        public async Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD",
            string? creativeType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null,
            string? itemGroupId = null,
            string? itemId = null)
        {
            // ✅ Check permissions for summary cards access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập summary cards GMV Max ProductCreative");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max ProductCreative summary cards data with currency: {Currency}, creativeType: {CreativeType}", currency, creativeType);
                // ✅ Get summary cards data from Dapper repository
                var summaryCardsData = await _factGmvMaxProductCreativeDapperRepository.GetSummaryCardsAsync(
                    currency, 
                    allowedAdvertiserIds, 
                    creativeType, 
                    fromDate, 
                    toDate, 
                    searchText, 
                    shopIds,
                    campaignIds,
                    itemGroupId,
                    itemId);

                Logger.LogInformation("Successfully loaded GMV Max ProductCreative summary cards data");
                return summaryCardsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative summary cards with currency {currency}");
                throw;
            }
        }

        public async Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get overview section data from Dapper repository
                var overviewData = await _factGmvMaxProductCreativeDapperRepository.GetOverviewSectionAsync(currency, allowedAdvertiserIds);

                return overviewData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative overview section with currency {currency}");
                throw;
            }
        }

        public async Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get charts data from Dapper repository
                var chartsData = await _factGmvMaxProductCreativeDapperRepository.GetChartsDataAsync(currency, allowedAdvertiserIds);

                return chartsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative charts data with currency {currency}");
                throw;
            }
        }

        public async Task<DetailedChartsDto> GetDetailedChartsAsync()
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get detailed charts data from Dapper repository
                var detailedChartsData = await _factGmvMaxProductCreativeDapperRepository.GetDetailedChartsAsync(allowedAdvertiserIds);

                return detailedChartsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting FactGmvMaxProductCreative detailed charts");
                throw;
            }
        }

        public async Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get rankings data from Dapper repository
                var rankingsData = await _factGmvMaxProductCreativeDapperRepository.GetRankingsDataAsync(currency, allowedAdvertiserIds);

                return rankingsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative rankings with currency {currency}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativePerformanceAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get creative performance data from Dapper repository
                var performanceData = await _factGmvMaxProductCreativeDapperRepository.GetCreativePerformanceAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                return performanceData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative performance from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd} with currency {currency}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativeDeliveryDto>> GetCreativeDeliveryAnalysisAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get creative delivery analysis from Dapper repository
                var deliveryAnalysis = await _factGmvMaxProductCreativeDapperRepository.GetCreativeDeliveryAnalysisAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                return deliveryAnalysis;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative delivery analysis from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativeContentTypeDto>> GetContentTypeAnalysisAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get content type analysis from Dapper repository
                var contentTypeAnalysis = await _factGmvMaxProductCreativeDapperRepository.GetContentTypeAnalysisAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                return contentTypeAnalysis;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative content type analysis from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<IEnumerable<DashboardCreativeRanking>> GetCreativeRankingAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get creative ranking data from Dapper repository
                var rankingData = await _factGmvMaxProductCreativeDapperRepository.GetCreativeRankingAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                return rankingData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative ranking from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd} with currency {currency}");
                throw;
            }
        }

        public async Task<IEnumerable<DashboardAccountRanking>> GetAccountRankingAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get account ranking from Dapper repository
                var accountRanking = await _factGmvMaxProductCreativeDapperRepository.GetAccountRankingAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                return accountRanking;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative account ranking from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetByCreativeTypeAsync(DateTime fromDate, DateTime toDate, string creativeType, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get data filtered by creative type from Dapper repository
                var filteredEntities = await _factGmvMaxProductCreativeDapperRepository.GetByCreativeTypeAsync(fromDate, toDate, creativeType, allowedAdvertiserIds, currency);

                // ✅ Convert to response format
                var filteredData = new GetFactGmvMaxProductCreativeDataResponse
                {
                    FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(filteredEntities.ToList())
                };

                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative by creative type {creativeType} from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetByContentTypeAsync(DateTime fromDate, DateTime toDate, string shopContentType, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get data filtered by content type from Dapper repository
                var filteredEntities = await _factGmvMaxProductCreativeDapperRepository.GetByContentTypeAsync(fromDate, toDate, shopContentType, allowedAdvertiserIds, currency);

                // ✅ Convert to response format
                var filteredData = new GetFactGmvMaxProductCreativeDataResponse
                {
                    FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(filteredEntities.ToList())
                };

                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative by content type {shopContentType} from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetByDeliveryStatusAsync(DateTime fromDate, DateTime toDate, string deliveryStatus, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get data filtered by delivery status from Dapper repository
                var filteredEntities = await _factGmvMaxProductCreativeDapperRepository.GetByDeliveryStatusAsync(fromDate, toDate, deliveryStatus, allowedAdvertiserIds, currency);

                // ✅ Convert to response format
                var filteredData = new GetFactGmvMaxProductCreativeDataResponse
                {
                    FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(filteredEntities.ToList())
                };

                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative by delivery status {deliveryStatus} from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetByTikTokAccountAsync(DateTime fromDate, DateTime toDate, string ttAccountName, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get data filtered by TikTok account from Dapper repository
                var filteredEntities = await _factGmvMaxProductCreativeDapperRepository.GetByTikTokAccountAsync(fromDate, toDate, ttAccountName, allowedAdvertiserIds, currency);

                // ✅ Convert to response format
                var filteredData = new GetFactGmvMaxProductCreativeDataResponse
                {
                    FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(filteredEntities.ToList())
                };

                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative by TikTok account {ttAccountName} from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<GetFactGmvMaxProductCreativeDataResponse> GetByProductGroupAsync(DateTime fromDate, DateTime toDate, string itemGroupId, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get data filtered by product group from Dapper repository
                var filteredEntities = await _factGmvMaxProductCreativeDapperRepository.GetByProductGroupAsync(fromDate, toDate, itemGroupId, allowedAdvertiserIds, currency);

                // ✅ Convert to response format
                var filteredData = new GetFactGmvMaxProductCreativeDataResponse
                {
                    FactGmvMaxProductCreatives = ObjectMapper.Map<List<FactGmvMaxProductCreativeEntity>, List<FactGmvMaxProductCreativeDto>>(filteredEntities.ToList())
                };

                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative by product group {itemGroupId} from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetLowPerformanceCreativesAsync(DateTime fromDate, DateTime toDate, decimal roasThreshold = 1.5m, decimal tacosThreshold = 30.0m, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get low performance creatives from Dapper repository
                var lowPerformanceData = await _factGmvMaxProductCreativeDapperRepository.GetLowPerformanceCreativesAsync(fromDate, toDate, roasThreshold, tacosThreshold, allowedAdvertiserIds, currency);

                return lowPerformanceData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative low performance from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd} with ROAS threshold {roasThreshold} and TACOS threshold {tacosThreshold}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetHighPerformanceCreativesAsync(DateTime fromDate, DateTime toDate, decimal roasThreshold = 3.0m, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get high performance creatives from Dapper repository
                var highPerformanceData = await _factGmvMaxProductCreativeDapperRepository.GetHighPerformanceCreativesAsync(fromDate, toDate, roasThreshold, allowedAdvertiserIds, currency);

                return highPerformanceData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative high performance from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd} with ROAS threshold {roasThreshold}");
                throw;
            }
        }

        public async Task<IEnumerable<GmvMaxProductCreativePerformanceDto>> GetCreativeAlertsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(false);

                // ✅ Get creative alerts from Dapper repository
                var creativeAlerts = await _factGmvMaxProductCreativeDapperRepository.GetCreativeAlertsAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                return creativeAlerts;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error getting FactGmvMaxProductCreative alerts from {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                throw;
            }
        }

        private FactGmvMaxProductCreativeDto FilterCreativeDtoByPermissions(FactGmvMaxProductCreativeDto creative, HashSet<string> allowedFields)
        {
            // Clone the original creative DTO
            var filteredCreative = new FactGmvMaxProductCreativeDto();
            
            // Use reflection to copy only allowed fields
            var creativeType = typeof(FactGmvMaxProductCreativeDto);
            var properties = creativeType.GetProperties();
            
            foreach (var property in properties)
            {
                var propertyName = property.Name;
                
                // Copy field if it's allowed
                if (PermissionFieldHelper.IsFieldAllowed(propertyName, allowedFields))
                {
                    var value = property.GetValue(creative);
                    property.SetValue(filteredCreative, value);
                }
            }

            return filteredCreative;
        }
    }
}

