using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Domain.Repositories;
using System.Text.Json;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Implementation của SystemNotificationRuleRepository
    /// Sử dụng Entity Framework Core để truy vấn database
    /// </summary>
    public class SystemNotificationRuleRepository : EfCoreRepository<TikTokDbContext, SystemNotificationRule, Guid>, ISystemNotificationRuleRepository
    {
        public SystemNotificationRuleRepository(IDbContextProvider<TikTokDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// Universal method để lấy rules với các filter options
        /// </summary>
        public async Task<List<SystemNotificationRule>> GetRulesAsync(
            string? entityType = null,
            bool? isActive = true,
            Guid? creatorId = null,
            bool? hasCustomTemplate = null,
            bool? isCrossEntity = null,
            DateTime? createdAfter = null,
            DateTime? createdBefore = null,
            bool? isDefault = null,
            bool? isPublic = null,
            string? searchText = null,
            int skipCount = 0,
            int maxResultCount = 0)
        {
            var criteria = new SystemNotificationRuleSearchCriteria
            {
                EntityType = entityType,
                IsActive = isActive,
                CreatorId = creatorId,
                HasCustomTemplate = hasCustomTemplate,
                IsCrossEntity = isCrossEntity,
                CreatedAfter = createdAfter,
                CreatedBefore = createdBefore,
                IsDefault = isDefault,
                IsPublic = isPublic,
                SearchText = searchText,
                SkipCount = skipCount,
                MaxResultCount = maxResultCount
            };

            return await SearchRulesAsync(criteria);
        }


        /// <summary>
        /// Lấy statistics về rules
        /// </summary>
        public async Task<SystemNotificationRuleStats> GetRuleStatisticsAsync()
        {
            var dbSet = await GetDbSetAsync();
            var allRules = await dbSet.ToListAsync();

            var stats = new SystemNotificationRuleStats
            {
                TotalRules = allRules.Count,
                ActiveRules = allRules.Count(r => r.IsActive),
                InactiveRules = allRules.Count(r => !r.IsActive),
                StandardRules = allRules.Count(r => r.IsActive), 
                // REMOVED: RulesWithCustomTemplates - system auto-generates notification content
                TotalTriggerCount = allRules.Sum(r => r.TriggerCount),
                LastTriggerTime = allRules.Where(r => r.LastTriggeredAt.HasValue).Max(r => r.LastTriggeredAt),
                OldestRuleCreated = allRules.Any() ? allRules.Min(r => r.CreationTime) : null,
                NewestRuleCreated = allRules.Any() ? allRules.Max(r => r.CreationTime) : null
            };

            // Breakdown by entity type
            stats.RulesByEntityTypeBreakdown = allRules
                .Where(r => r.IsActive)
                .GroupBy(r => r.EntityType)
                .ToDictionary(g => g.Key, g => g.Count());

            // Breakdown by recipient type (deprecated - use SystemNotificationRuleConsumerEntity instead)
            stats.RulesByRecipientTypeBreakdown = new Dictionary<string, int>();

            // Breakdown by operator
            stats.RulesByOperatorBreakdown = new Dictionary<string, int>();

            // Breakdown by field
            stats.RulesByFieldBreakdown = new Dictionary<string, int>();

            return stats;
        }

        /// <summary>
        /// Lấy rules cần update cooldown
        /// </summary>
        public Task<List<SystemNotificationRule>> GetRulesNeedingCooldownUpdateAsync()
        {
            // CooldownMinutes field has been removed; return empty list
            return Task.FromResult(new List<SystemNotificationRule>());
        }

        /// <summary>
        /// Lấy rules theo search criteria
        /// </summary>
        public async Task<List<SystemNotificationRule>> SearchRulesAsync(SystemNotificationRuleSearchCriteria criteria)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(criteria.EntityType))
                query = query.Where(r => r.EntityType == criteria.EntityType);

            if (criteria.IsActive.HasValue)
                query = query.Where(r => r.IsActive == criteria.IsActive.Value);

            // REMOVED: HasCustomTemplate filter - system auto-generates notification content

            if (criteria.CreatedAfter.HasValue)
                query = query.Where(r => r.CreationTime >= criteria.CreatedAfter.Value);

            if (criteria.CreatedBefore.HasValue)
                query = query.Where(r => r.CreationTime <= criteria.CreatedBefore.Value);

            if (criteria.CreatorId.HasValue)
                query = query.Where(r => r.CreatorId == criteria.CreatorId.Value);

            // New search criteria
            if (criteria.IsDefault.HasValue)
                query = query.Where(r => r.IsDefault == criteria.IsDefault.Value);

            if (criteria.IsPublic.HasValue)
                query = query.Where(r => r.IsPublic == criteria.IsPublic.Value);

            // TargetEntity and NotificationFrequency have been removed; ignore these filters

            if (!string.IsNullOrEmpty(criteria.SearchText))
            {
                var searchText = criteria.SearchText.ToLower();
                query = query.Where(r => 
                    (r.RuleName != null && r.RuleName.ToLower().Contains(searchText)));
            }

            // Apply ordering and pagination
            query = query.OrderBy(r => r.CreationTime);

            if (criteria.SkipCount > 0)
                query = query.Skip(criteria.SkipCount);

            if (criteria.MaxResultCount > 0)
                query = query.Take(criteria.MaxResultCount);

            return await query.ToListAsync();
        }

        /// <summary>
        /// Đếm số rules theo search criteria
        /// </summary>
        public async Task<long> GetCountAsync(SystemNotificationRuleSearchCriteria criteria)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(criteria.EntityType))
                query = query.Where(r => r.EntityType == criteria.EntityType);

            if (criteria.IsActive.HasValue)
                query = query.Where(r => r.IsActive == criteria.IsActive.Value);

            // REMOVED: HasCustomTemplate filter - system auto-generates notification content

            if (criteria.CreatedAfter.HasValue)
                query = query.Where(r => r.CreationTime >= criteria.CreatedAfter.Value);

            if (criteria.CreatedBefore.HasValue)
                query = query.Where(r => r.CreationTime <= criteria.CreatedBefore.Value);

            if (criteria.CreatorId.HasValue)
                query = query.Where(r => r.CreatorId == criteria.CreatorId.Value);

            // New search criteria
            if (criteria.IsDefault.HasValue)
                query = query.Where(r => r.IsDefault == criteria.IsDefault.Value);

            if (criteria.IsPublic.HasValue)
                query = query.Where(r => r.IsPublic == criteria.IsPublic.Value);

            // TargetEntity and NotificationFrequency have been removed; ignore these filters

            if (!string.IsNullOrEmpty(criteria.SearchText))
            {
                var searchText = criteria.SearchText.ToLower();
                query = query.Where(r => 
                    (r.RuleName != null && r.RuleName.ToLower().Contains(searchText)));
            }

            return await query.LongCountAsync();
        }
    }
}
