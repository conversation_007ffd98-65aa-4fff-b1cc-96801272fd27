/**
 * Ad Account Selector Component for System Notification Rules
 * Handles ad account selection modal, search, and selection management
 */

const AdAccountSelector = {
    // Global state for selected ad accounts
    selectedAdAccountsState: [],

    // Initialize ad account selector
    init() {
        this.setupEventListeners();
    },

    // Load available ad accounts
    loadAvailableAdAccounts() {
        // Reset modal state
        $('#systemNotificationAdAccountSearchInput').val('');
        $('#systemNotificationAdAccountSearchDependClientInput').val('');
        $('#systemNotificationAdAccountsSearchResults').addClass('d-none');
        $('#systemNotificationAdAccountsSearchLoading').removeClass('d-none');
        $('#systemNotificationSelectedAdAccountsList').empty();
        $('#systemNotificationSelectedAdAccountsCount').text('0');

        // Clear any previous search results
        $('#systemNotificationAdAccountsSearchList').empty();

        // Show modal
        $('#selectSystemNotificationAdAccountsModal').modal('show');

        // Reset selectedAdAccountsState
        this.selectedAdAccountsState = [];

        setTimeout(() => {
            this.getListAdAccounts();
        }, 1000);
    },

    // Get list of ad accounts
    getListAdAccounts() {
        // Show loading state
        $('#systemNotificationAdAccountsSearchLoading').removeClass('d-none');
        $('#systemNotificationAdAccountsSearchResults').addClass('d-none');

        // Prepare search parameters
        const searchInput = {
            maxResultCount: 50, // Limit results for better performance
            skipCount: 0,
            sorting: 'name' // Sort by name for better UX
        };
        searchInput.filterText = "";

        // Call the ABP framework client API proxy
        SystemNotificationApiService.getAdAccounts(searchInput)
            .then((response) => {
                if (response && response.items && response.items.length > 0) {
                    this.displaySearchResults(response.items);
                } else {
                    // No results found
                    this.displaySearchResults([]);
                }
            })
            .catch((error) => {
                console.error('Error loading ad accounts:', error);

                // Hide loading and show error
                $('#systemNotificationAdAccountsSearchLoading').addClass('d-none');
                $('#systemNotificationAdAccountsSearchResults').removeClass('d-none');
                $('#systemNotificationAdAccountsSearchList').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Lỗi khi tải danh sách tài khoản quảng cáo: ${error.message || 'Không thể kết nối đến máy chủ'}
                    </div>
                `);
            });
    },

    // Search ad accounts
    searchAdAccounts(searchTerm, searchDependClient) {
        // Show loading state
        $('#systemNotificationAdAccountsSearchLoading').removeClass('d-none');
        $('#systemNotificationAdAccountsSearchResults').addClass('d-none');

        // Prepare search parameters
        const searchInput = {
            maxResultCount: 50, // Limit results for better performance
            skipCount: 0,
            sorting: 'name' // Sort by name for better UX
        };
        searchInput.filterText = searchTerm;
        searchInput.customerFilterText = searchDependClient;

        // Call the ABP framework client API proxy
        SystemNotificationApiService.getAdAccounts(searchInput)
            .then((response) => {
                if (response && response.items && response.items.length > 0) {
                    // Filter results to match search term more precisely
                    const filteredResults = response.items.filter(account => {
                        const nameMatch = account.name && account.name.toLowerCase().includes(searchTerm.toLowerCase());
                        const advertiserIdMatch = account.advertiserId && account.advertiserId.toLowerCase().includes(searchTerm.toLowerCase());
                        const ownerBcIdMatch = account.ownerBcId && account.ownerBcId.toLowerCase().includes(searchTerm.toLowerCase());

                        return nameMatch || advertiserIdMatch || ownerBcIdMatch;
                    });

                    this.displaySearchResults(filteredResults);
                } else {
                    // No results found
                    this.displaySearchResults([]);
                }
            })
            .catch((error) => {
                console.error('Error searching ad accounts:', error);

                // Hide loading and show error
                $('#systemNotificationAdAccountsSearchLoading').addClass('d-none');
                $('#systemNotificationAdAccountsSearchResults').removeClass('d-none');
                $('#systemNotificationAdAccountsSearchList').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Lỗi khi tìm kiếm tài khoản quảng cáo: ${error.message || 'Không thể kết nối đến máy chủ'}
                    </div>
                `);
            });
    },

    // Display search results
    displaySearchResults(results) {
        $('#systemNotificationAdAccountsSearchLoading').addClass('d-none');

        if (results.length === 0) {
            $('#systemNotificationAdAccountsSearchResults').removeClass('d-none');
            $('#systemNotificationAdAccountsSearchList').html(`
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Không tìm thấy tài khoản quảng cáo nào phù hợp với từ khóa tìm kiếm.
                </div>
            `);
            return;
        }

        $('#systemNotificationAdAccountsSearchResults').removeClass('d-none');

        let resultsHtml = '';
        results.forEach(account => {
            const isSelected = this.isAdAccountSelected(account.id);
            const statusBadgeClass = getSystemNotificationStatusBadgeClass(account.status);
            const statusText = getSystemNotificationStatusText(account.status);

            resultsHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${account.name || 'N/A'}</h6>
                        <small class="text-muted">
                            ID: ${account.advertiserId || 'N/A'} | Owner BC: ${account.ownerBcId || 'N/A'} | 
                            Status: <span class="badge ${statusBadgeClass}">${statusText}</span>
                            ${account.company ? `| Company: ${account.company}<br/><span class="text-muted fw-bold">Khách hàng: ${account.customerName ?? '-'} (${account.customerId ?? '-'})</span>` : ''}
                        </small>
                    </div>
                    <button data-account-id="${account.id}" type="button" class="btn btn-sm ${isSelected ? 'btn-danger' : 'btn-primary'}" 
                            onclick="AdAccountSelector.toggleAdAccountSelection(event, '${account.id}', '${escapeHtml(account.name || 'N/A')}', '${account.advertiserId || 'N/A'}', '${account.ownerBcId || 'N/A'}')">
                        <i class="fas fa-${isSelected ? 'times' : 'plus'}"></i>
                        ${isSelected ? 'Bỏ chọn' : 'Chọn'}
                    </button>
                </div>
            `;
        });

        $('#systemNotificationAdAccountsSearchList').html(resultsHtml);
    },

    // Check if ad account is already selected
    isAdAccountSelected(accountId) {
        return $('#systemNotificationSelectedAdAccountsList').find(`[data-account-id="${accountId}"]`).length > 0;
    },

    // Toggle ad account selection
    toggleAdAccountSelection(event, accountId, accountName, advertiserIdParam, ownerBcId) {
        if (this.isAdAccountSelected(accountId)) {
            // Remove from selection
            $(`#systemNotificationSelectedAdAccountsList [data-account-id="${accountId}"]`).remove();
            event.target.innerHTML = '<i class="fas fa-plus"></i> Chọn';
            // remove btn-danger class from button
            let sourceButton = $(`#systemNotificationAdAccountsSearchList button[data-account-id="${accountId}"]`);
            sourceButton.html('<i class="fas fa-plus"></i> Chọn');
            sourceButton.removeClass('btn-danger');
            // remove from selectedAdAccountsState
            this.selectedAdAccountsState = this.selectedAdAccountsState.filter(id => id !== accountId);
        } else {
            // Add to selection 
            const selectedItem = `
                <div class="list-group-item d-flex justify-content-between align-items-center" data-account-id="${accountId}">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${accountName}</h6>
                        <small class="text-muted">
                            ID: ${advertiserIdParam} | Owner BC: ${ownerBcId}
                        </small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="AdAccountSelector.removeSelectedAdAccount('${accountId}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            $('#systemNotificationSelectedAdAccountsList').append(selectedItem);

            let sourceButton = $(`#systemNotificationAdAccountsSearchList button[data-account-id="${accountId}"]`);
            sourceButton.html('<i class="fas fa-times"></i> Bỏ chọn');
            sourceButton.addClass('btn-danger');
            // add to selectedAdAccountsState
            this.selectedAdAccountsState.push(accountId);
        }

        this.updateSelectedAdAccountsCount();
    },

    // Remove selected ad account
    removeSelectedAdAccount(accountId) {
        $(`#systemNotificationSelectedAdAccountsList [data-account-id="${accountId}"]`).remove();
        this.updateSelectedAdAccountsCount();
        
        // Update the source button
        let sourceButton = $(`#systemNotificationAdAccountsSearchList button[data-account-id="${accountId}"]`);
        sourceButton.html('<i class="fas fa-plus"></i> Chọn');
        sourceButton.removeClass('btn-danger');
        
        // Remove from selectedAdAccountsState
        this.selectedAdAccountsState = this.selectedAdAccountsState.filter(id => id !== accountId);
    },

    // Update selected ad accounts count
    updateSelectedAdAccountsCount() {
        const count = this.selectedAdAccountsState ? this.selectedAdAccountsState.length : 0;
        $('#systemNotificationSelectedAdAccountsCount').text(count);
    },

    // Filter ad accounts
    filterAdAccounts(searchTerm) {
        $('#systemNotificationAvailableAdAccountsList .col-md-6').each(function () {
            const cardText = $(this).text().toLowerCase();
            if (cardText.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    },

    // Setup event listeners
    setupEventListeners() {
        // Handle search button click for system notification ad accounts
        $(document).on('click', '#systemNotificationSearchAdAccountsBtn', () => {
            const searchTerm = $('#systemNotificationAdAccountSearchInput').val().trim();
            const searchDependClient = $('#systemNotificationAdAccountSearchDependClientInput').val().trim();
            this.searchAdAccounts(searchTerm, searchDependClient);
        });

        // Handle clear search button click for system notification ad accounts
        $(document).on('click', '#systemNotificationClearSearchBtn', () => {
            $('#systemNotificationAdAccountSearchInput').val('');
            $('#systemNotificationAdAccountsSearchResults').addClass('d-none');
            $('#systemNotificationAdAccountsSearchLoading').addClass('d-none');
            $('#systemNotificationAdAccountsSearchList').empty();
        });

        $(document).on('click', '#systemNotificationClearSearchDependClientBtn', () => {
            $('#systemNotificationAdAccountSearchDependClientInput').val('');
        });

        // Handle search input enter key for system notification ad accounts
        $(document).on('keypress', '#systemNotificationAdAccountSearchInput', (e) => {
            if (e.which === 13) {
                $('#systemNotificationSearchAdAccountsBtn').click();
            }
        });

        // Handle input change for system notification ad accounts
        $(document).on('input', '#systemNotificationAdAccountSearchDependClientInput', () => {
            const searchTerm = $('#systemNotificationAdAccountSearchInput').val().trim();
            const searchDependClient = $('#systemNotificationAdAccountSearchDependClientInput').val().trim();
            this.searchAdAccounts(searchTerm, searchDependClient);
        });

        // Handle confirm ad accounts selection for system notification rules
        $(document).on('click', '#systemNotificationConfirmAdAccountsSelection', () => {
            const selectedAdAccountIds = this.selectedAdAccountsState || [];
            
            if (selectedAdAccountIds.length === 0) {
                abp.notify.warn('Vui lòng chọn ít nhất một tài khoản quảng cáo');
                return;
            }

            // Add selected ad accounts to the rule
            if (!window.SystemNotificationApiService) {
                console.error('SystemNotificationApiService is not available');
                abp.notify.error('Lỗi: API Service chưa được khởi tạo');
                return;
            }

            const ruleId = window.ConsumersManager?.currentRuleId;
            console.log('Current rule ID:', ruleId);
            console.log('ConsumersManager:', window.ConsumersManager);

            if (!ruleId || ruleId === '********-0000-0000-0000-********0000') {
                console.error('Invalid rule ID:', ruleId);
                abp.notify.error('Lỗi: Không tìm thấy ID quy tắc. Vui lòng thử lại.');
                return;
            }

            SystemNotificationApiService.addConsumers(ruleId, {
                adAccountIds: selectedAdAccountIds
            })
            .then(() => {
                abp.notify.success('Đã thêm tài khoản quảng cáo thành công');
                $('#selectSystemNotificationAdAccountsModal').modal('hide');
                // Refresh the consumers modal
                if (window.ConsumersManager?.currentRuleId && window.ConsumersManager?.currentRuleName) {
                    window.ConsumersManager.openConsumersModal(
                        window.ConsumersManager.currentRuleId,
                        window.ConsumersManager.currentRuleName
                    );
                }
            })
            .catch((error) => {
                console.error('Error adding ad accounts:', error);
                abp.notify.error('Lỗi khi thêm tài khoản quảng cáo');
            });
        });

        // Handle ad account search
        $('#systemNotificationAdAccountSearch').on('input', (e) => {
            const searchTerm = $(e.target).val().trim().toLowerCase();
            this.filterAdAccounts(searchTerm);
        });
    }
};

// Export to global scope
window.AdAccountSelector = AdAccountSelector;
