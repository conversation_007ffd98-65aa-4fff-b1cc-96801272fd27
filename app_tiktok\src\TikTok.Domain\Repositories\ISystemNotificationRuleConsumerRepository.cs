using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Domain.Entities.SystemNotification;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Domain.Repositories
{
    public interface ISystemNotificationRuleConsumerRepository : IRepository<SystemNotificationRuleConsumerEntity, Guid>
    {
        /// <summary>
        /// Lấy danh sách consumers của một rule
        /// </summary>
        Task<List<SystemNotificationRuleConsumerEntity>> GetConsumersByRuleIdAsync(Guid ruleId);

        /// <summary>
        /// Lấy danh sách rules áp dụng cho một consumer
        /// </summary>
        Task<List<SystemNotificationRuleConsumerEntity>> GetRulesByConsumerAsync(Guid? bcId, Guid? adAccountId);

        /// <summary>
        /// Xóa tất cả consumers của một rule
        /// </summary>
        Task DeleteConsumersByRuleIdAsync(Guid ruleId);

        /// <summary>
        /// Kiểm tra consumer đã được assign cho rule chưa
        /// </summary>
        Task<bool> IsConsumerAssignedAsync(Guid ruleId, Guid? bcId, Guid? adAccountId);
    }
}
