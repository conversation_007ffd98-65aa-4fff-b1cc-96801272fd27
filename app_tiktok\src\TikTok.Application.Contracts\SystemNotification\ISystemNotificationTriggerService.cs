using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Service interface để trigger system notifications
    /// Provides contract cho notification trigger functionality
    /// </summary>
    public interface ISystemNotificationTriggerService : ITransientDependency
    {
        /// <summary>
        /// Trigger notification rule check cho generic entities
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities">Entities to check against rules</param>
        /// <param name="entityType">String representation of entity type</param>
        /// <returns>Task for async processing</returns>
        Task TriggerRuleCheck<T>(System.Collections.Generic.IEnumerable<T> entities, string entityType);

        /// <summary>
        /// Trigger notification rule check cho Campaign entities
        /// </summary>
        /// <param name="campaigns">Campaign entities to check</param>
        /// <returns>Task for async processing</returns>
        Task TriggerCampaignRuleCheck(System.Collections.Generic.IEnumerable<object> campaigns);

        /// <summary>
        /// Trigger notification rule check cho Product entities
        /// </summary>
        /// <param name="products">Product entities to check</param>
        /// <returns>Task for async processing</returns>
        Task TriggerProductRuleCheck(System.Collections.Generic.IEnumerable<object> products);

        /// <summary>
        /// Trigger notification rule check cho Product Creative entities
        /// </summary>
        /// <param name="productCreatives">Product creative entities to check</param>
        /// <returns>Task for async processing</returns>
        Task TriggerProductCreativeRuleCheck(System.Collections.Generic.IEnumerable<object> productCreatives);

        /// <summary>
        /// Trigger notification rule check cho Product Campaign entities
        /// </summary>
        /// <param name="productCampaigns">Product campaign entities to check</param>
        /// <returns>Task for async processing</returns>
        Task TriggerProductCampaignRuleCheck(System.Collections.Generic.IEnumerable<object> productCampaigns);

        /// <summary>
        /// Trigger notification rule check cho Live Campaign entities
        /// </summary>
        /// <param name="liveCampaigns">Live campaign entities to check</param>
        /// <returns>Task for async processing</returns>
        Task TriggerLiveCampaignRuleCheck(System.Collections.Generic.IEnumerable<object> liveCampaigns);

        /// <summary>
        /// Bulk trigger cho multiple entity types
        /// </summary>
        /// <param name="entitiesByType">Dictionary of entity type -> entities</param>
        /// <returns>Task for async processing</returns>
        Task TriggerBulkRuleCheck(System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<object>> entitiesByType);

        /// <summary>
        /// Helper method để integrate với data sync services
        /// </summary>
        /// <param name="entityType">Type of entity that was synced</param>
        /// <param name="syncedEntities">Entities that were synced</param>
        /// <returns>Task for async processing</returns>
        Task OnDataSyncCompleted(string entityType, System.Collections.Generic.IEnumerable<object> syncedEntities);

        /// <summary>
        /// Helper method để integrate với dashboard updates
        /// </summary>
        /// <param name="entityType">Type of entity that was updated</param>
        /// <param name="updatedEntities">Entities that were updated</param>
        /// <returns>Task for async processing</returns>
        Task OnDashboardDataUpdated(string entityType, System.Collections.Generic.IEnumerable<object> updatedEntities);

        /// <summary>
        /// Helper method để integrate với campaign updates
        /// </summary>
        /// <param name="campaign">Single campaign that was updated</param>
        /// <returns>Task for async processing</returns>
        Task OnCampaignUpdated(object campaign);

        /// <summary>
        /// Helper method để integrate với product updates
        /// </summary>
        /// <param name="product">Single product that was updated</param>
        /// <returns>Task for async processing</returns>
        Task OnProductUpdated(object product);

        /// <summary>
        /// Helper method để integrate với product campaign updates
        /// </summary>
        /// <param name="productCampaign">Single product campaign that was updated</param>
        /// <returns>Task for async processing</returns>
        Task OnProductCampaignUpdated(object productCampaign);

        /// <summary>
        /// Helper method để integrate với live campaign updates
        /// </summary>
        /// <param name="liveCampaign">Single live campaign that was updated</param>
        /// <returns>Task for async processing</returns>
        Task OnLiveCampaignUpdated(object liveCampaign);
    }
}
