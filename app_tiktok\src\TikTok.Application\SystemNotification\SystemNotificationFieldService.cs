using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Application.SystemNotification.FieldConfigurations;

namespace TikTok.Application.SystemNotification
{
    /// <summary>
    /// Service tổng hợp để quản lý field definitions cho System Notification Rules
    /// Gộp chức năng của FieldConfigurationInitializer, FieldDefinitionRegistry và field methods từ SystemNotificationRuleAppService
    /// </summary>
    public class SystemNotificationFieldService : ITransientDependency
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<SystemNotificationFieldService> _logger;
        private readonly Dictionary<string, List<FieldMetadata>> _fieldDefinitions;
        private readonly object _lockObject = new object();
        private static bool _isInitialized = false;

        public SystemNotificationFieldService(IMemoryCache cache, ILogger<SystemNotificationFieldService> logger)
        {
            _cache = cache;
            _logger = logger;
            _fieldDefinitions = new Dictionary<string, List<FieldMetadata>>();
            
            // Tự động khởi tạo field definitions
            InitializeFieldDefinitions();
        }

        /// <summary>
        /// Khởi tạo tất cả field definitions
        /// </summary>
        private void InitializeFieldDefinitions()
        {
            if (!_isInitialized)
            {
                lock (_lockObject)
                {
                    if (!_isInitialized)
                    {
                        try
                        {
                            // Đăng ký field definitions cho RawGmvMaxProductCreativeReportEntity
                            var productFieldConfig = new RawGmvMaxProductCreativeFieldConfiguration();
                            var productFields = productFieldConfig.GetFieldDefinitions();
                            RegisterFields(RawGmvMaxProductCreativeFieldConfiguration.EntityType, productFields);

                            // Đăng ký field definitions cho RawGmvMaxProductCampaignReportEntity
                            var productCampaignFieldConfig = new RawGmvMaxProductCampaignFieldConfiguration();
                            var productCampaignFields = productCampaignFieldConfig.GetFieldDefinitions();
                            RegisterFields(RawGmvMaxProductCampaignFieldConfiguration.EntityType, productCampaignFields);

                            // Đăng ký field definitions cho RawGmvMaxLiveCampaignReportEntity
                            var liveCampaignFieldConfig = new RawGmvMaxLiveCampaignFieldConfiguration();
                            var liveCampaignFields = liveCampaignFieldConfig.GetFieldDefinitions();
                            RegisterFields(RawGmvMaxLiveCampaignFieldConfiguration.EntityType, liveCampaignFields);
                            
                            _isInitialized = true;
                        }
                        catch (Exception ex)
                        {
                            // Log error but don't throw to avoid breaking the application
                            _logger.LogError(ex, "Error initializing SystemNotificationFieldService");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Đăng ký fields cho entity type
        /// </summary>
        private void RegisterFields(string entityType, IEnumerable<FieldMetadata> fieldMetadatas)
        {
            lock (_lockObject)
            {
                var fieldsList = fieldMetadatas.ToList();
                _fieldDefinitions[entityType] = fieldsList;
                
                // Pre-populate cache instead of invalidating
                var cacheKey = $"ManualFieldDefinitions_{entityType}";
                var sortedFields = fieldsList
                    .OrderBy(f => f.Category)
                    .ThenBy(f => f.DisplayName)
                    .ToList();
                
                _cache.Set(cacheKey, sortedFields, TimeSpan.FromHours(24));
            }
        }

        /// <summary>
        /// Lấy tất cả fields cho entity type
        /// </summary>
        public Task<List<FieldMetadata>> GetFieldsForEntityTypeAsync(string entityType)
        {
            var cacheKey = $"ManualFieldDefinitions_{entityType}";
            
            // Try to get from cache first
            if (_cache.TryGetValue(cacheKey, out List<FieldMetadata>? cachedFields))
            {
                return Task.FromResult(cachedFields ?? new List<FieldMetadata>());
            }
            
            // Fallback to _fieldDefinitions
            lock (_lockObject)
            {
                if (_fieldDefinitions.ContainsKey(entityType))
                {
                    var fields = _fieldDefinitions[entityType];
                    
                    // Sắp xếp theo category và display name
                    var sortedFields = fields
                        .OrderBy(f => f.Category)
                        .ThenBy(f => f.DisplayName)
                        .ToList();
                    
                    // Cache the result
                    _cache.Set(cacheKey, sortedFields, TimeSpan.FromHours(24));
                    
                    return Task.FromResult(sortedFields);
                }
                else
                {
                    _logger.LogWarning($"No fields found for entity type: {entityType}");
                }
            }

            return Task.FromResult(new List<FieldMetadata>());
        }


        /// <summary>
        /// Xóa cache cho entity type
        /// </summary>
        private void InvalidateCache(string entityType)
        {
            var cacheKey = $"ManualFieldDefinitions_{entityType}";
            _cache.Remove(cacheKey);
        }
    }
}
