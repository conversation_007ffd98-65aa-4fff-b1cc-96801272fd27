/**
 * Core Helper Functions for System Notification Rules
 * Global utility functions used across the application
 */

// Helper function to get localized text - moved to global scope
function getLocalizedText(key) {
    const localization = window.systemNotificationRulesLocalization;
    if (!localization) {
        // Provide fallback messages for common keys
        const fallbackMessages = {
            'yes': 'Có',
            'no': 'Không',
            'on': 'Bật',
            'off': 'Tắt'
        };
        return fallbackMessages[key] || key;
    }
    return localization[key] || key;
}

// Helper function to escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Helper function to format date strings
function formatDate(s) {
    if (!s) return '';
    try {
        return new Date(s).toLocaleString();
    } catch {
        return s;
    }
}

// Helper function to get localized display name for entity types
// Now using centralized EntityTypesManager
function getEntityTypeDisplayName(entityType) {
    // Use EntityTypesManager if available
    if (window.EntityTypesManager) {
        return window.EntityTypesManager.getLocalizedDisplayName(entityType);
    }
    // Fallback to hardcoded mapping for backward compatibility
    const entityTypeMap = {
        'RawGmvMaxProductCreativeReportEntity': getLocalizedText('gmvMaxProductCreative'),
        'RawGmvMaxProductCampaignReportEntity': getLocalizedText('gmvMaxProductCampaign'),
        'RawGmvMaxLiveCampaignReportEntity': getLocalizedText('gmvMaxLiveCampaign'),
    };
    return entityTypeMap[entityType] || escapeHtml(entityType);
}

// Helper function to get status badge class for system notification rules
function getSystemNotificationStatusBadgeClass(status) {
    switch (status) {
        case 1: // ACTIVE
            return 'bg-success';
        case 2: // INACTIVE
            return 'bg-warning';
        case 3: // SUSPENDED
            return 'bg-danger';
        case 4: // PENDING
            return 'bg-info';
        default:
            return 'bg-secondary';
    }
}

// Helper function to get status text for system notification rules
function getSystemNotificationStatusText(status) {
    switch (status) {
        case 1:
            return 'ACTIVE';
        case 2:
            return 'INACTIVE';
        case 3:
            return 'SUSPENDED';
        case 4:
            return 'PENDING';
        default:
            return 'UNKNOWN';
    }
}

// Helper function to map dataType to Syncfusion format
function mapDataTypeToSyncfusion(dataType) {
    const typeMap = {
        string: 'string',
        number: 'number',
        decimal: 'number',
        boolean: 'boolean',
        datetime: 'date',
        enum: 'string',
    };

    return typeMap[dataType.toLowerCase()] || 'string';
}

// Helper function to get input type for value editor
function getInputType(syncfusionType) {
    const typeMap = {
        string: 'text',
        number: 'number',
        boolean: 'checkbox',
        date: 'datetime-local',
    };

    return typeMap[syncfusionType] || 'text';
}

// Helper function to map operators to Syncfusion format
function mapOperatorsToSyncfusion(operators) {
    const opMap = {
        equal: 'equal',
        equals: 'equal',
        'not equals': 'notequal',
        notequals: 'notequal',
        contains: 'contains',
        startswith: 'startswith',
        endswith: 'endswith',
        greaterthan: 'greaterthan',
        lessthan: 'lessthan',
        greaterthanorequal: 'greaterthanorequal',
        lessthanorequal: 'lessthanorequal',
        between: 'between',
        in: 'in',
        notin: 'notin',
    };
    return (operators || []).map(function (op) {
        if (!op) return 'equal';
        const text = op.toString().trim().toLowerCase();
        if (text === 'not equals') return 'notequal';
        const key = text.replace(/\s+/g, '');
        return opMap[key] || 'equal';
    });
}

// Export functions to global scope for backward compatibility
window.getLocalizedText = getLocalizedText;
window.escapeHtml = escapeHtml;
window.formatDate = formatDate;
window.getEntityTypeDisplayName = getEntityTypeDisplayName;
window.getSystemNotificationStatusBadgeClass = getSystemNotificationStatusBadgeClass;
window.getSystemNotificationStatusText = getSystemNotificationStatusText;
window.mapDataTypeToSyncfusion = mapDataTypeToSyncfusion;
window.getInputType = getInputType;
window.mapOperatorsToSyncfusion = mapOperatorsToSyncfusion;
