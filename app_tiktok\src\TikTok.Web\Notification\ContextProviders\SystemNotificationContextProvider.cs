using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Web.Notification.ContextProviders;
using TikTok.Application.Contracts.SystemNotification;

namespace TikTok.Web.Notification.ContextProviders
{
    /// <summary>
    /// Context provider cho System Notifications
    /// Xử lý việc render system notification với rich data từ SystemNotificationRequest
    /// </summary>
    public class SystemNotificationContextProvider : BaseNotificationProvider
    {
        /// <summary>
        /// Build notification context cho system notifications
        /// </summary>
        public async Task<NotificationContext> BuildNotificationContextAsync(
            string objectId, 
            string context, 
            Dictionary<string, object> metadata)
        {
            var notificationContext = new NotificationContext
            {
                ObjectId = objectId,
                Context = context,
                Title = GetTitle(metadata),
                Content = GetContent(metadata),
                Icon = GetIcon(metadata),
                Color = GetColor(metadata),
                RedirectUrl = GetRedirectUrl(metadata),
                Priority = GetPriority(metadata),
                Metadata = metadata
            };

            return notificationContext;
        }

        /// <summary>
        /// Extract title từ metadata
        /// </summary>
        private string GetTitle(Dictionary<string, object> metadata)
        {
            if (metadata.TryGetValue("Title", out var title) && title is string titleStr)
                return titleStr;

            // Fallback title
            return "System Notification";
        }

        /// <summary>
        /// Extract content từ metadata
        /// </summary>
        private string GetContent(Dictionary<string, object> metadata)
        {
            if (metadata.TryGetValue("Message", out var message) && message is string messageStr)
                return messageStr;

            // Fallback content
            return "A system notification has been triggered.";
        }

        /// <summary>
        /// Extract icon từ metadata
        /// </summary>
        private string? GetIcon(Dictionary<string, object> metadata)
        {
            if (metadata.TryGetValue("Icon", out var icon) && icon is string iconStr)
                return iconStr;

            return "🔔"; // Default icon
        }

        /// <summary>
        /// Extract color từ metadata
        /// </summary>
        private string? GetColor(Dictionary<string, object> metadata)
        {
            if (metadata.TryGetValue("Color", out var color) && color is string colorStr)
                return colorStr;

            return "#666666"; // Default color
        }

        /// <summary>
        /// Extract redirect URL từ metadata
        /// </summary>
        private string? GetRedirectUrl(Dictionary<string, object> metadata)
        {
            if (metadata.TryGetValue("RedirectUrl", out var url) && url is string urlStr)
                return urlStr;

            return null;
        }

        /// <summary>
        /// Extract priority từ metadata
        /// </summary>
        private int GetPriority(Dictionary<string, object> metadata)
        {
            if (metadata.TryGetValue("Priority", out var priority) && priority is int priorityInt)
                return priorityInt;

            return 2; // Default medium priority
        }
    }

    /// <summary>
    /// Notification context data structure
    /// </summary>
    public class NotificationContext
    {
        public string ObjectId { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? RedirectUrl { get; set; }
        public int Priority { get; set; } = 2;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
