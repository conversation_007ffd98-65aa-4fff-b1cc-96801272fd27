using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ GMV Max Identities
    /// </summary>
    public class SyncGmvMaxIdentitiesWorker : WorkerJobBase
    {
        private readonly IGmvMaxIdentitiesSyncService _gmvMaxIdentitiesSyncService;
        public SyncGmvMaxIdentitiesWorker(
            ILogger<SyncGmvMaxIdentitiesWorker> logger,
            IServiceProvider serviceProvider,
            IGmvMaxIdentitiesSyncService gmvMaxIdentitiesSyncService)
            : base(logger, serviceProvider)
        {
            _gmvMaxIdentitiesSyncService = gmvMaxIdentitiesSyncService;
        }

        public override CommandType CommandType => CommandType.SyncGmvMaxIdentities;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync GMV Max Identities cho từng BC
            var responseSync = await _gmvMaxIdentitiesSyncService.SyncManyGmvMaxIdentitiesAsync(parameters.BcId);
            if (responseSync != null)
            {
                if (string.IsNullOrEmpty(responseSync.ErrorMessage))
                    return JobResult.Success(JsonConvert.SerializeObject(responseSync));
                else
                    return JobResult.Error(responseSync.ErrorMessage, JsonConvert.SerializeObject(responseSync));
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ GMV Max Identities");
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private SyncGmvMaxIdentitiesParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<SyncGmvMaxIdentitiesParameters>(parametersJson)
                    ?? new SyncGmvMaxIdentitiesParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new SyncGmvMaxIdentitiesParameters();
            }
        }

        /// <summary>
        /// Parameters cho SyncGmvMaxIdentities
        /// </summary>
        public class SyncGmvMaxIdentitiesParameters : DefaultParameters
        {
        }
    }
}
