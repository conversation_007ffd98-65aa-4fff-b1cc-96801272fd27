/**
 * Permission Helper for TikTok Frontend
 * Handles permission checking and field filtering for GMV Max Campaigns and Products
 * Uses ABP's built-in permission system instead of custom API
 */
class PermissionHelper {
    constructor() {
        this.permissions = {
            // ✅ Unified GMV Max permissions
            campaign: {
                viewSpending: 'TikTok.FactGmvMax.ViewSpending',
                viewMetrics: 'TikTok.FactGmvMax.ViewMetrics',
                viewAll: 'TikTok.FactGmvMax.ViewAll',
                viewVideo: 'TikTok.FactGmvMax.ViewVideo',
                viewAllAdvertisers: 'TikTok.FactGmvMax.ViewAllAdvertisers',
            },
            // ✅ Same unified permissions for product (for backward compatibility)
            product: {
                viewSpending: 'TikTok.FactGmvMax.ViewSpending',
                viewMetrics: 'TikTok.FactGmvMax.ViewMetrics',
                viewAll: 'TikTok.FactGmvMax.ViewAll',
                viewVideo: 'TikTok.FactGmvMax.ViewVideo',
                viewAllAdvertisers: 'TikTok.FactGmvMax.ViewAllAdvertisers',
            },
            // ✅ Add video permissions
            video: {
                viewVideo: 'TikTok.FactGmvMax.ViewVideo',
                viewAll: 'TikTok.FactGmvMax.ViewAll',
                viewAllAdvertisers: 'TikTok.FactGmvMax.ViewAllAdvertisers',
            },
            // ✅ Creative permissions (same as product for creative-level data)
            creative: {
                viewSpending: 'TikTok.FactGmvMax.ViewSpending',
                viewMetrics: 'TikTok.FactGmvMax.ViewMetrics',
                viewAll: 'TikTok.FactGmvMax.ViewAll',
                viewVideo: 'TikTok.FactGmvMax.ViewVideo',
                viewAllAdvertisers: 'TikTok.FactGmvMax.ViewAllAdvertisers',
            },
        };

        // Field definitions for filtering
        this.fieldDefinitions = {
            campaign: {
                spendingFields: [
                    'Cost',
                    'CostVND',
                    'CostUSD',
                    'NetCost',
                    'NetCostVND',
                    'NetCostUSD',
                    'CostPerOrder',
                    'CostPerOrderVND',
                    'CostPerOrderUSD',
                    'TargetRoiBudget',
                    'TargetRoiBudgetVND',
                    'TargetRoiBudgetUSD',
                    'MaxDeliveryBudget',
                    'MaxDeliveryBudgetVND',
                    'MaxDeliveryBudgetUSD',
                    'RoasBid',
                    'RoasBidVND',
                    'RoasBidUSD',
                    'CostPerLiveView',
                    'CostPerLiveViewVND',
                    'CostPerLiveViewUSD',
                    'CostPerTenSecondLiveView',
                    'CostPerTenSecondLiveViewVND',
                    'CostPerTenSecondLiveViewUSD',
                ],
                metricsFields: [
                    'Orders',
                    'GrossRevenue',
                    'GrossRevenueVND',
                    'GrossRevenueUSD',
                    'ROAS',
                    'ConversionRate',
                    'CTR',
                    'CPC',
                    'CPCVND',
                    'CPCUSD',
                    'CPM',
                    'CPMVND',
                    'CPMUSD',
                    'Impressions',
                    'Clicks',
                    'LiveViews',
                    'TenSecondLiveViews',
                    'LiveViewRate',
                    'TenSecondLiveViewRate',
                    'LiveViewConversionRate',
                    'TenSecondLiveViewConversionRate',
                ],
                basicFields: [
                    'CampaignId',
                    'CampaignName',
                    'AdvertiserId',
                    'AdvertiserName',
                    'StoreId',
                    'StoreName',
                    'Date',
                    'CreativeType',
                    'ObjectiveType',
                    'Status',
                ],
            },
            product: {
                spendingFields: [
                    'Cost',
                    'CostVND',
                    'CostUSD',
                    'NetCost',
                    'NetCostVND',
                    'NetCostUSD',
                    'CostPerOrder',
                    'CostPerOrderVND',
                    'CostPerOrderUSD',
                ],
                metricsFields: [
                    'Orders',
                    'GrossRevenue',
                    'GrossRevenueVND',
                    'GrossRevenueUSD',
                    'ROAS',
                    'ConversionRate',
                    'CTR',
                    'CPC',
                    'CPCVND',
                    'CPCUSD',
                    'CPM',
                    'CPMVND',
                    'CPMUSD',
                    'Impressions',
                    'Clicks',
                ],
                basicFields: [
                    'ProductId',
                    'ProductName',
                    'AdvertiserId',
                    'AdvertiserName',
                    'StoreId',
                    'StoreName',
                    'Date',
                    'CreativeType',
                ],
            },
            creative: {
                spendingFields: [
                    'Cost',
                    'CostVND',
                    'CostUSD',
                    'CostPerOrder',
                    'CostPerOrderVND',
                    'CostPerOrderUSD',
                ],
                metricsFields: [
                    'Orders',
                    'GrossRevenue',
                    'GrossRevenueVND',
                    'GrossRevenueUSD',
                    'ROAS',
                    'ProductImpressions',
                    'ProductClicks',
                    'ProductClickRate',
                    'AdClickRate',
                    'AdConversionRate',
                    'AdVideoViewRate2s',
                    'AdVideoViewRate6s',
                    'AdVideoViewRateP25',
                    'AdVideoViewRateP50',
                    'AdVideoViewRateP75',
                    'AdVideoViewRateP100',
                ],
                restrictedFields: [
                    'TACOS', // Chỉ hiển thị với ViewAll hoặc ViewAllAdvertisers
                ],
                basicFields: [
                    'ItemId',
                    'ItemGroupId',
                    'Title',
                    'ProductId',
                    'ProductName',
                    'AdvertiserId',
                    'AdvertiserName',
                    'StoreId',
                    'StoreName',
                    'Date',
                    'CreativeType',
                    'ShopContentType',
                    'CreativeDeliveryStatus',
                ],
            },
        };
    }

    /**
     * ✅ Get permissions using ABP's built-in permission system
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {Object} Permission object with boolean flags
     */
    getPermissions(entityType) {
        const entityPermissions = this.permissions[entityType];
        if (!entityPermissions) {
            return {
                viewSpending: false,
                viewMetrics: false,
                viewAll: false,
                viewAllAdvertisers: false,
            };
        }

        // ✅ Check if ABP is available
        if (typeof abp === 'undefined' || !abp.auth) {
            return {
                viewSpending: false,
                viewMetrics: false,
                viewAll: false,
                viewAllAdvertisers: false,
            };
        }

        // ✅ Use ABP's built-in permission checking
        const result = {
            viewSpending: abp.auth.isGranted(entityPermissions.viewSpending),
            viewMetrics: abp.auth.isGranted(entityPermissions.viewMetrics),
            viewAll: abp.auth.isGranted(entityPermissions.viewAll),
            viewAllAdvertisers: abp.auth.isGranted(
                entityPermissions.viewAllAdvertisers
            ),
        };
        return result;
    }

    /**
     * ✅ Wait for ABP to be available
     * @returns {Promise<void>}
     */
    async waitForABP() {
        return new Promise((resolve) => {
            if (typeof abp !== 'undefined' && abp.auth) {
                resolve();
                return;
            }

            const checkABP = () => {
                if (typeof abp !== 'undefined' && abp.auth) {
                    resolve();
                } else {
                    setTimeout(checkABP, 100);
                }
            };
            checkABP();
        });
    }

    /**
     * ✅ Check if user has any permission for the entity
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {boolean}
     */
    hasAnyPermission(entityType) {
        const permissions = this.getPermissions(entityType);
        return (
            permissions.viewSpending ||
            permissions.viewMetrics ||
            permissions.viewAll ||
            permissions.viewAllAdvertisers
        );
    }

    /**
     * ✅ Get allowed fields based on user permissions
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {Array<string>} Array of allowed field names
     */
    getAllowedFields(entityType) {
        const permissions = this.getPermissions(entityType);
        const fieldDef = this.fieldDefinitions[entityType];

        if (!fieldDef) {
            console.error(`Unknown entity type: ${entityType}`);
            return [];
        }

        let allowedFields = [...fieldDef.basicFields]; // Always include basic fields

        if (
            permissions.viewSpending ||
            permissions.viewAll ||
            permissions.viewAllAdvertisers
        ) {
            allowedFields = allowedFields.concat(fieldDef.spendingFields);
        }

        if (
            permissions.viewMetrics ||
            permissions.viewAll ||
            permissions.viewAllAdvertisers
        ) {
            allowedFields = allowedFields.concat(fieldDef.metricsFields);
        }

        return allowedFields;
    }

    /**
     * ✅ Check if a specific field is allowed based on permissions
     * @param {string} fieldName - Name of the field to check
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {boolean}
     */
    isFieldAllowed(fieldName, entityType) {
        const allowedFields = this.getAllowedFields(entityType);
        return allowedFields.includes(fieldName);
    }

    /**
     * ✅ Filter data object based on permissions
     * @param {Object} data - Data object to filter
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {Object} Filtered data object
     */
    filterDataByPermissions(data, entityType) {
        const permissions = this.getPermissions(entityType);

        if (permissions.viewAll || permissions.viewAllAdvertisers) {
            return data; // Return all data if user has view all permission
        }

        const allowedFields = this.getAllowedFields(entityType);
        const filteredData = {};

        // Copy only allowed fields
        allowedFields.forEach((field) => {
            if (data.hasOwnProperty(field)) {
                filteredData[field] = data[field];
            }
        });

        return filteredData;
    }

    /**
     * ✅ Filter array of data objects based on permissions
     * @param {Array<Object>} dataArray - Array of data objects to filter
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {Array<Object>} Array of filtered data objects
     */
    filterDataArrayByPermissions(dataArray, entityType) {
        if (!Array.isArray(dataArray)) {
            return [];
        }

        return dataArray.map((item) =>
            this.filterDataByPermissions(item, entityType)
        );
    }

    /**
     * ✅ Show permission denied message
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     */
    showPermissionDeniedMessage(entityType) {
        const entityNames = {
            campaign: 'Campaign',
            product: 'Product',
            creative: 'Product Creative',
            video: 'Video',
        };
        const entityName = entityNames[entityType] || entityType;
        const message = `Bạn không có quyền truy cập dữ liệu GMV Max ${entityName}. Vui lòng liên hệ quản trị viên để được cấp quyền.`;

        // Use ABP's notification system if available, otherwise use alert
        if (typeof abp !== 'undefined' && abp.notify) {
            abp.notify.error(message);
        } else {
            alert(message);
        }
    }

    /**
     * ✅ Get permission summary for debugging
     * @param {string} entityType - 'campaign', 'product', 'creative', or 'video'
     * @returns {Object} Permission summary
     */
    getPermissionSummary(entityType) {
        const permissions = this.getPermissions(entityType);
        const allowedFields = this.getAllowedFields(entityType);

        return {
            entityType,
            permissions,
            allowedFieldsCount: allowedFields.length,
            hasAnyPermission: this.hasAnyPermission(entityType),
            allowedFields: allowedFields,
        };
    }
}

// ✅ Create global instance
window.PermissionHelper = new PermissionHelper();

// ✅ Log permission status for debugging (only in development)
if (
    typeof abp !== 'undefined' &&
    abp.setting &&
    abp.setting.get('App.Environment') === 'Development'
) {
    console.log('🔐 Permission Helper initialized with ABP integration');
    console.log(
        '📊 Campaign permissions:',
        window.PermissionHelper.getPermissionSummary('campaign')
    );
    console.log(
        '📦 Product permissions:',
        window.PermissionHelper.getPermissionSummary('product')
    );
}
