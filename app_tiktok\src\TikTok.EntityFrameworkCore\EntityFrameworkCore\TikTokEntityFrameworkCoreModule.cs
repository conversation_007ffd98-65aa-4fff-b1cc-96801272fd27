﻿using Microsoft.Extensions.DependencyInjection;
using System;
using TikTok.AdAccounts;
using TikTok.Dapper;
using TikTok.Entities;
using TikTok.EntityFrameworkCore.Repositories;
using TikTok.Repositories;
using Tsp.Zalo.EntityFrameworkCore;
using Module.Notifications.EntityFrameworkCore;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using Volo.Abp.Uow;
using TikTok.Domain.Repositories;

namespace TikTok.EntityFrameworkCore;

[DependsOn(
    typeof(TikTokDomainModule),
    typeof(AbpIdentityEntityFrameworkCoreModule),
    typeof(AbpOpenIddictEntityFrameworkCoreModule),
    typeof(AbpPermissionManagementEntityFrameworkCoreModule),
    typeof(AbpSettingManagementEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpBackgroundJobsEntityFrameworkCoreModule),
    typeof(AbpAuditLoggingEntityFrameworkCoreModule),
    typeof(AbpTenantManagementEntityFrameworkCoreModule),
    typeof(AbpFeatureManagementEntityFrameworkCoreModule),
    typeof(ZaloEntityFrameworkCoreModule),
    typeof(NotificationsEntityFrameworkCoreModule)
    )]
public class TikTokEntityFrameworkCoreModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        TikTokEfCoreEntityExtensionMappings.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<TikTokDbContext>(options =>
        {
            /* Remove "includeAllEntities: true" to create
             * default repositories only for aggregate roots */
            options.AddDefaultRepositories(includeAllEntities: true);
        });

        // Register Dapper repository
        context.Services.AddTransient(typeof(IDapperRepository<>), typeof(DapperRepository<>));

        // Register specific Dapper repositories
        context.Services.AddTransient<IAdAccountDapperRepository, AdAccountDapperRepository>();

        Configure<AbpDbContextOptions>(options =>
        {
            /* The main point to change your DBMS.
             * See also TikTokMigrationsDbContextFactory for EF Core tooling. */
            options.UseSqlServer();
        });

        // Register the AdAccountSupporterFilter
        context.Services.AddScoped<IAdAccountSupporterFilter, AdAccountSupporterFilter>();
    }
}
