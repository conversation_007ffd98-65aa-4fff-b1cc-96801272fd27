using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Entities;
using TikTok.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository implementation cho RawGmvMaxCampaignPostsEntity
    /// </summary>
    public class RawGmvMaxCampaignPostsRepository : EfCoreRepository<TikTokDbContext, RawGmvMaxCampaignPostsEntity, Guid>, IRawGmvMaxCampaignPostsRepository
    {
        public RawGmvMaxCampaignPostsRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> danh s<PERSON>ch posts theo Campaign ID
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByCampaignIdAsync(string campaignId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.CampaignId == campaignId)
                .OrderBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts theo Advertiser ID
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByAdvertiserIdAsync(string advertiserId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts theo BC ID
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByBcIdAsync(string bcId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.BcId == bcId)
                .OrderBy(x => x.AdvertiserId)
                .ThenBy(x => x.CampaignId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts theo Identity ID
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByIdentityIdAsync(string identityId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.IdentityId == identityId)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts theo Identity Type
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByIdentityTypeAsync(string identityType, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.IdentityType == identityType)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts theo Video Definition
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoDefinitionAsync(string videoDefinition, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.VideoDefinition == videoDefinition)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts theo Video Format
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoFormatAsync(string videoFormat, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.VideoFormat == videoFormat)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts có video duration trong khoảng
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoDurationRangeAsync(double minDuration, double maxDuration, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.VideoDuration.HasValue && x.VideoDuration >= minDuration && x.VideoDuration <= maxDuration)
                .OrderBy(x => x.VideoDuration)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách posts có video size trong khoảng
        /// </summary>
        public async Task<List<RawGmvMaxCampaignPostsEntity>> GetByVideoSizeRangeAsync(long minSize, long maxSize, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.VideoSize.HasValue && x.VideoSize >= minSize && x.VideoSize <= maxSize)
                .OrderBy(x => x.VideoSize)
                .ThenBy(x => x.ItemId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy post theo Item ID
        /// </summary>
        public async Task<RawGmvMaxCampaignPostsEntity?> GetByItemIdAsync(string itemId, string campaignId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.ItemId == itemId && x.CampaignId == campaignId)
                .FirstOrDefaultAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy post theo Video ID
        /// </summary>
        public async Task<RawGmvMaxCampaignPostsEntity?> GetByVideoIdAsync(string videoId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.VideoId == videoId)
                .FirstOrDefaultAsync(cancellationToken);
        }

        /// <summary>
        /// Kiểm tra post có tồn tại không
        /// </summary>
        public async Task<bool> ExistsAsync(string itemId, string campaignId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.ItemId == itemId && x.CampaignId == campaignId)
                .AnyAsync(cancellationToken);
        }

        /// <summary>
        /// Xóa tất cả posts của một Campaign
        /// </summary>
        public async Task<int> DeleteByCampaignIdAsync(string campaignId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var entities = await dbSet
                .Where(x => x.CampaignId == campaignId)
                .ToListAsync(cancellationToken);
            
            if (entities.Any())
            {
                dbSet.RemoveRange(entities);
                await (await GetDbContextAsync()).SaveChangesAsync(cancellationToken);
            }
            
            return entities.Count;
        }

        /// <summary>
        /// Xóa tất cả posts của một Advertiser
        /// </summary>
        public async Task<int> DeleteByAdvertiserIdAsync(string advertiserId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var entities = await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .ToListAsync(cancellationToken);
            
            if (entities.Any())
            {
                dbSet.RemoveRange(entities);
                await (await GetDbContextAsync()).SaveChangesAsync(cancellationToken);
            }
            
            return entities.Count;
        }
    }
}
