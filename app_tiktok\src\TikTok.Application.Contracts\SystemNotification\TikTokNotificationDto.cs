using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.Application.Contracts.SystemNotification
{
    public class TikTokNotificationDto
    {
        public string UserId { get; set; }
        public TikTokUserType UserType { get; set; }
        public string ObjectId { get; set; }
        public string Context { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string Payload { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public string PhoneNumber { get; set; }
        public string AdAccountId { get; set; }
        public string BcId { get; set; }
    }

    public class TikTokNotificationUserDto
    {
        public string UserId { get; set; }
        public TikTokUserType UserType { get; set; }
        public string PhoneNumber { get; set; }
        public string AdAccountId { get; set; }
        public string BcId { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class NotificationDto
    {
        public string ObjectId { get; set; }
        public string Context { get; set; }
        public string Payload { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}


