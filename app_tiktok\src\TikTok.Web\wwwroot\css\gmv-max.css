/* ===================================
   GMV Max Analysis Styles
   =================================== */

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Tab Navigation */
.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    color: #495057;
    font-weight: 600;
}

.nav-tabs .nav-link i {
    margin-right: 8px;
}

/* ROI Cards */
.roi-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #ffffff;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.roi-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #007bff;
}

.roi-card.good-roi {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.roi-card.good-roi:hover {
    border-color: #1e7e34;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
}

.roi-card.poor-roi {
    border-color: #dc3545;
    background: linear-gradient(135deg, #fff8f8 0%, #f8e8e8 100%);
}

.roi-card.poor-roi:hover {
    border-color: #c82333;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.15);
}

.roi-card .card-body {
    padding: 2rem;
    text-align: center;
}

.roi-card .roi-count {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.roi-card.good-roi .roi-count {
    color: #28a745;
}

.roi-card.poor-roi .roi-count {
    color: #dc3545;
}

.roi-card .roi-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.roi-card .roi-threshold {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.roi-card .roi-percentage {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.roi-card .btn-details {
    background: #ffffff;
    border: 2px solid #007bff;
    color: #007bff;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.roi-card .btn-details:hover {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
}

.roi-card.good-roi .btn-details {
    border-color: #28a745;
    color: #28a745;
}

.roi-card.good-roi .btn-details:hover {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.roi-card.poor-roi .btn-details {
    border-color: #dc3545;
    color: #dc3545;
}

.roi-card.poor-roi .btn-details:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

/* ========================================
   🎨 ENHANCED: Video Status Cards with Modern Effects
   ======================================== */
.video-status-card {
    border-radius: 20px;
    border: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.video-status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-status-card:hover::before {
    opacity: 1;
}

.video-status-card:hover {
    transform: translateY(-8px) scale(1.02);
}

.video-status-card .card-body {
    padding: 1rem;
    text-align: center;
}

.video-status-card .status-icon {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.video-status-card:hover .status-icon {
    transform: scale(1.1);
}

.video-status-card .status-count {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
}

.video-status-card:hover .status-count {
    transform: scale(1.05);
}

.video-status-card .status-name {
    font-size: 0.9rem;
    font-weight: 600;
}

/* ========================================
   🎨 ENHANCED: Video Status Colors - Modern & Vibrant
   ======================================== */

/* Đang chờ - Vàng cam nhẹ nhàng */
.status-in-queue {
    background: linear-gradient(135deg, #ffd93d 0%, #ff8c42 100%);
    color: #2d3436;
    border-left: 5px solid #ff8c42;
    box-shadow: 0 4px 15px rgba(255, 140, 66, 0.3);
}

.status-in-queue .status-icon {
    color: #d63031 !important;
}

.status-in-queue .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-in-queue .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-in-queue:hover {
    box-shadow: 0 8px 25px rgba(255, 140, 66, 0.4);
    transform: translateY(-2px);
}

/* Đang học - Xanh dương thông minh */
.status-learning {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-left: 5px solid #764ba2;
    box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);
}

.status-learning .status-icon {
    color: #74b9ff !important;
}

.status-learning .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-learning .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-learning:hover {
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.4);
    transform: translateY(-2px);
}

/* Đang chạy - Xanh lá thành công */
.status-delivering {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border-left: 5px solid #11998e;
    box-shadow: 0 4px 15px rgba(17, 153, 142, 0.3);
}

.status-delivering .status-icon {
    color: #00ff88 !important;
}

.status-delivering .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-delivering .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-delivering:hover {
    box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
    transform: translateY(-2px);
}

/* Not Delivery - Xanh dương nhạt (trung lập, chưa hoàn thành) */
.status-not-delivery {
    background: linear-gradient(135deg, #90caf9 0%, #64b5f6 100%);
    color: white;
    border-left: 5px solid #64b5f6;
    box-shadow: 0 4px 15px rgba(100, 181, 246, 0.3);
}

.status-not-delivery .status-icon {
    color: #42a5f5 !important;
}

.status-not-delivery .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-not-delivery .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-not-delivery:hover {
    box-shadow: 0 8px 25px rgba(100, 181, 246, 0.4);
    transform: translateY(-2px);
}

/* Excluded - Cam (bị loại khỏi quy trình) */
.status-excluded {
    background: linear-gradient(135deg, #ff9800 0%, #fb8c00 100%);
    color: white;
    border-left: 5px solid #fb8c00;
    box-shadow: 0 4px 15px rgba(251, 140, 0, 0.3);
}

.status-excluded .status-icon {
    color: #ffb74d !important;
}

.status-excluded .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-excluded .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-excluded:hover {
    box-shadow: 0 8px 25px rgba(251, 140, 0, 0.4);
    transform: translateY(-2px);
}

/* Unavailable - Xám (không hoạt động, vô hiệu) */
.status-unavailable {
    background: linear-gradient(135deg, #bdbdbd 0%, #a0a0a0 100%);
    color: white;
    border-left: 5px solid #a0a0a0;
    box-shadow: 0 4px 15px rgba(160, 160, 160, 0.3);
}

.status-unavailable .status-icon {
    color: #e0e0e0 !important;
}

.status-unavailable .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-unavailable .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-unavailable:hover {
    box-shadow: 0 8px 25px rgba(160, 160, 160, 0.4);
    transform: translateY(-2px);
}

/* Rejected - Đỏ (cảnh báo mạnh, bị chặn) */
.status-rejected {
    background: linear-gradient(135deg, #f44336 0%, #e53935 100%);
    color: white;
    border-left: 5px solid #e53935;
    box-shadow: 0 4px 15px rgba(229, 57, 53, 0.3);
}

.status-rejected .status-icon {
    color: #ef5350 !important;
}

.status-rejected .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-rejected .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-rejected:hover {
    box-shadow: 0 8px 25px rgba(229, 57, 53, 0.4);
    transform: translateY(-2px);
}

/* Cần ủy quyền - Hồng cam cảnh báo */
.status-auth-needed {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-left: 5px solid #f5576c;
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.status-auth-needed .status-icon {
    color: #fb7185 !important;
}

.status-auth-needed .status-count {
    color: #2d3436 !important;
    font-weight: 800;
}

.status-auth-needed .status-name {
    color: #2d3436 !important;
    font-weight: 600;
}

.status-auth-needed:hover {
    box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
    transform: translateY(-2px);
}

/* ========================================
   🌟 SPECIAL EFFECTS: Animated Status Indicators
   ======================================== */

/* Đang chạy - Hiệu ứng pulse xanh với glow */
.status-delivering .status-icon {
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}

/* Đang học - Hiệu ứng glow xanh dương với brain pulse */
.status-learning .status-icon {
    animation: glow-blue 3s ease-in-out infinite alternate;
}

@keyframes glow-blue {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.08);
    }
}

/* Không chạy - Hiệu ứng shake nhẹ */
.status-not-delivery:hover .status-icon {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

/* Đang chờ - Hiệu ứng bounce nhẹ với glow vàng */
.status-in-queue .status-icon {
    animation: bounce-gentle 2s infinite;
}

@keyframes bounce-gentle {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0) scale(1);
    }
    40% {
        transform: translateY(-4px) scale(1.05);
    }
    60% {
        transform: translateY(-2px) scale(1.02);
    }
}

/* Từ chối - Hiệu ứng flash đỏ */
.status-rejected:hover .status-count {
    animation: flash-red 0.6s ease-in-out;
}

@keyframes flash-red {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* ========================================
   💫 ENHANCED: Number Counter Effects
   ======================================== */

/* Hiệu ứng cho số đếm khi hover */
.video-status-card:hover .status-count {
    animation: number-pop 0.3s ease-out;
}

@keyframes number-pop {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1.05);
    }
}

/* Hiệu ứng đặc biệt cho số lớn */
.video-status-card .status-count[data-count='high'] {
    animation: big-number-glow 2s ease-in-out infinite alternate;
}

@keyframes big-number-glow {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.05);
    }
}

/* ========================================
   🎯 ENHANCED: Video Table Styling
   ======================================== */

/* Improved video table styling */
#problematic-videos-table .table {
    margin-bottom: 0;
}

#problematic-videos-table .table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

#problematic-videos-table .table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

#problematic-videos-table .table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

#problematic-videos-table .table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
}

/* Enhanced status badges in table */
#problematic-videos-table .badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    min-width: 120px;
    text-align: center;
}

/* Action buttons in table */
#problematic-videos-table .btn-group .btn {
    border-radius: 6px;
    margin-right: 0.25rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

#problematic-videos-table .btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

#problematic-videos-table .btn-group .btn:last-child {
    margin-right: 0;
}

/* Card header improvements */
.card-header .d-flex .gap-3 {
    gap: 1rem !important;
}

.card-header .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.card-header .btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.card-header .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

/* ========================================
   📄 PAGINATION: Video Table Styling
   ======================================== */

/* Pagination container */
#problematic-videos-pagination {
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
}

/* Pagination info text */
#problematic-videos-pagination .pagination-info {
    display: flex;
    align-items: center;
}

#problematic-videos-pagination .pagination-info small {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

/* Pagination controls */
#problematic-videos-pagination .pagination {
    margin-bottom: 0;
}

#problematic-videos-pagination .page-link {
    color: #495057;
    background-color: #fff;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    margin: 0 0.125rem;
    transition: all 0.3s ease;
}

#problematic-videos-pagination .page-link:hover {
    color: #007bff;
    background-color: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

#problematic-videos-pagination .page-item.active .page-link {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25);
}

#problematic-videos-pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.6;
}

#problematic-videos-pagination .page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Pagination responsive */
@media (max-width: 768px) {
    #problematic-videos-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    #problematic-videos-pagination .pagination-info {
        justify-content: center;
    }

    #problematic-videos-pagination nav {
        display: flex;
        justify-content: center;
    }
}

/* ========================================
   📱 RESPONSIVE: Mobile Optimizations for Status Cards
   ======================================== */
@media (max-width: 768px) {
    .video-status-card {
        border-radius: 15px;
        margin-bottom: 0.75rem;
    }

    .video-status-card .card-body {
        padding: 0.75rem;
    }

    .video-status-card .status-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }

    .video-status-card .status-count {
        font-size: 1.5rem;
    }

    .video-status-card .status-name {
        font-size: 0.8rem;
    }

    /* Giảm hiệu ứng animation trên mobile */
    .status-delivering .status-icon,
    .status-learning .status-icon,
    .status-in-queue .status-icon {
        animation: none;
    }
}

/* ========================================
   🌙 DARK MODE: Enhanced contrast for status cards
   ======================================== */
@media (prefers-color-scheme: dark) {
    .video-status-card {
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .video-status-card::before {
        background: rgba(255, 255, 255, 0.05);
    }

    .video-status-card:hover::before {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* Collapse Sections */
.card-header[data-bs-toggle='collapse'] {
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.card-header[data-bs-toggle='collapse']:hover {
    background-color: #f8f9fa;
}

.card-header .fa-chevron-down {
    transition: transform 0.3s ease;
}

.card-header[aria-expanded='false'] .fa-chevron-down {
    transform: rotate(-90deg);
}

/* Filter Sections */
.filter-section {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.filter-section:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
}

.filter-section .card-body {
    padding: 1.5rem;
}

.filter-section .card-title {
    color: #495057;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f8f9fa;
}

.filter-section .card-title i {
    color: #007bff;
    margin-right: 8px;
}

/* Form Controls */
.form-label.fw-bold {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-right: 8px;
    color: #007bff;
    font-size: 0.9rem;
}

/* Buttons */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-success {
    background: #28a745;
    border: 2px solid #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-outline-secondary {
    background: #ffffff;
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Filter Action Buttons */
.filter-actions {
    padding-top: 1rem;
    border-top: 1px solid #f8f9fa;
    margin-top: 1rem;
}

.filter-actions .btn {
    min-width: 120px;
}

/* Pivot Tables */
#productCampaignPivotTable,
#liveCampaignPivotTable,
#problematic-videos-table {
    min-height: 400px;
}

/* Loading States */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

.text-muted {
    font-size: 0.9rem;
}

/* Footer */
.footer-info .card {
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.footer-info small {
    color: #6c757d;
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%) !important;
}

/* Toast Notifications */
.toast {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-header {
    border-radius: 10px 10px 0 0;
    border-bottom: none;
}

.toast-body {
    border-radius: 0 0 10px 10px;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-title i {
    margin-right: 10px;
}

/* ========================================
   🆕 VALUE SELECTION MODAL STYLING - SYNCED WITH FactGmvMaxCampaign
   ======================================== */

/* Override general valueSelectionModal styling for GmvMax modals */
#productValueSelectionModal .modal-dialog,
#liveValueSelectionModal .modal-dialog {
    max-width: min(1500px, 80vw) !important;
}

/* Quick Selection Buttons Container */
.btn-group.flex-wrap {
    gap: 0.25rem;
}

/* Quick Selection Button Styling */
.modal-body .btn-outline-primary,
.modal-body .btn-outline-info,
.modal-body .btn-outline-success,
.modal-body .btn-outline-warning,
.modal-body .btn-outline-danger,
.modal-body .btn-outline-secondary {
    border-width: 2px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.modal-body .btn-outline-primary:hover,
.modal-body .btn-outline-info:hover,
.modal-body .btn-outline-success:hover,
.modal-body .btn-outline-warning:hover,
.modal-body .btn-outline-danger:hover,
.modal-body .btn-outline-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Selected Count Badge */
.modal-body .badge.bg-primary {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 10px;
    font-weight: 600;
}

/* Value Checkbox List Styling */
#product-value-checkbox-list .card,
#live-value-checkbox-list .card {
    transition: all 0.3s ease;
}

/* Ensure modal content has proper spacing */
#productValueSelectionModal .modal-body,
#liveValueSelectionModal .modal-body {
    padding: 1.5rem 2rem;
}

#productValueSelectionModal .modal-header,
#liveValueSelectionModal .modal-header {
    padding: 1rem 2rem;
}

#productValueSelectionModal .modal-footer,
#liveValueSelectionModal .modal-footer {
    padding: 1rem 2rem;
}

/* Responsive Design for Modal */
@media (max-width: 1400px) {
    #productValueSelectionModal .modal-dialog,
    #liveValueSelectionModal .modal-dialog {
        max-width: 95%;
    }
}

@media (max-width: 992px) {
    #productValueSelectionModal .modal-dialog,
    #liveValueSelectionModal .modal-dialog {
        max-width: 90%;
        margin: 1.5rem auto;
    }

    #productValueSelectionModal .modal-body,
    #liveValueSelectionModal .modal-body {
        padding: 1.25rem 1.5rem;
    }
}

@media (max-width: 768px) {
    #productValueSelectionModal .modal-dialog,
    #liveValueSelectionModal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }

    #productValueSelectionModal .modal-body,
    #liveValueSelectionModal .modal-body {
        padding: 1rem;
    }

    #productValueSelectionModal .modal-header,
    #liveValueSelectionModal .modal-header {
        padding: 0.75rem 1rem;
    }

    .modal-body .btn-group.flex-wrap {
        justify-content: center;
    }

    .modal-body .btn-sm {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    #product-value-checkbox-list .col-6,
    #live-value-checkbox-list .col-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
        text-align: center;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }

    .roi-card .card-body {
        padding: 1.5rem;
    }

    .roi-card .roi-count {
        font-size: 2.5rem;
    }

    .video-status-card .card-body {
        padding: 0.75rem;
    }

    .video-status-card .status-count {
        font-size: 1.3rem;
    }

    .nav-tabs .nav-link {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

/* ROI Card Specific Animations */
.roi-card.good-roi {
    animation: slideInLeft 0.8s ease-out;
    animation-delay: 0.2s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.roi-card.poor-roi {
    animation: slideInRight 0.8s ease-out;
    animation-delay: 0.4s;
    opacity: 0;
    animation-fill-mode: forwards;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ========================================
   🔧 Scoped thinner scrollbars for Pivot areas only
   ======================================== */
/* Firefox */
#productCampaignPivotTable,
#liveCampaignPivotTable {
    scrollbar-width: thin;
    scrollbar-color: auto; /* use system/default colors */
}

/* WebKit-based browsers (Chrome, Edge, Safari) */
#productCampaignPivotTable *::-webkit-scrollbar,
#liveCampaignPivotTable *::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

#productCampaignPivotTable *::-webkit-scrollbar-track,
#liveCampaignPivotTable *::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

#productCampaignPivotTable *::-webkit-scrollbar-thumb,
#liveCampaignPivotTable *::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 6px;
}

#productCampaignPivotTable *::-webkit-scrollbar-thumb:hover,
#liveCampaignPivotTable *::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ========================================
   🆕 NEW: Summary Cards Grid Layout (Like FactGmvMaxCampaign)
   ======================================== */
#summary-cards-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Responsive: 3 cards on tablet, 2 on mobile */
@media (max-width: 992px) {
    #summary-cards-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    #summary-cards-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    #summary-cards-container {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}

/* Use existing dashboard-summary-card styles from dashboard-common.css */
/* ROI-specific dashboard-summary-card styling */
.dashboard-summary-card.roi-good {
    border-left-color: #28a745 !important;
}

.dashboard-summary-card.roi-good .card-icon {
    color: #28a745;
}

.dashboard-summary-card.roi-poor {
    border-left-color: #dc3545 !important;
}

.dashboard-summary-card.roi-poor .card-icon {
    color: #dc3545;
}

/* Clickable card hover effects */
.dashboard-summary-card.clickable-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.dashboard-summary-card.clickable-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* ROI detail icon styling */
.roi-detail-icon {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.9rem !important;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.dashboard-summary-card.clickable-card:hover .roi-detail-icon {
    opacity: 1;
}

/* Adjust card-icon for ROI cards to accommodate both icons */
.dashboard-summary-card.roi-good .card-icon,
.dashboard-summary-card.roi-poor .card-icon {
    position: relative;
}

/* ========================================
   🔄 DEPRECATED: Old Metric Cards (keeping for fallback)
   ======================================== */
.metric-card {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    background: #ffffff;
    display: none; /* Hide old cards */
}

/* ========================================
   🔄 UPDATED: ROI Cards (Smaller, Second Row)
   ======================================== */
.roi-card-small {
    min-height: 120px;
}

.roi-card-small .card-body {
    padding: 1rem;
}

.roi-card-small .roi-count {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.roi-card-small .roi-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.roi-card-small .roi-icon {
    font-size: 1.2rem;
}

.roi-card-small .roi-percentage .badge {
    font-size: 0.75rem;
}

/* ROI Icon styling */
.roi-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.05);
}

/* Small detail buttons for ROI cards */
.btn-xs {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 0.25rem;
}

.roi-card-small .btn-xs {
    margin-top: 0.5rem;
    width: auto;
    min-width: 70px;
}

/* ========================================
   🆕 NEW: ROI Details Modal Styling
   ======================================== */
#roiDetailsModal .modal-dialog {
    max-width: 900px;
}

#roiDetailsModal .modal-body {
    padding: 0;
    max-height: 60vh;
    overflow: hidden;
}

#roiDetailsModal .table-responsive {
    border-radius: 0;
    border: none;
}

#roiDetailsModal .table thead th {
    background: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

#roiDetailsModal .table tbody tr:hover {
    background-color: #f8f9fa;
}

#roiDetailsModal .modal-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

#roiDetailsModal .modal-header {
    padding: 1rem 1.5rem;
    background: #ffffff;
    border-bottom: 1px solid #dee2e6;
}

/* Fix modal overlay issues */
#roiDetailsModal {
    z-index: 1055 !important;
}

#roiDetailsModal .modal-backdrop {
    z-index: 1050 !important;
}

/* Ensure proper modal behavior */
.modal-open {
    overflow: hidden !important;
}

/* Fix any potential scrollbar issues */
#roiDetailsModal .modal-dialog {
    margin: 1.75rem auto;
    pointer-events: none;
}

#roiDetailsModal .modal-content {
    pointer-events: auto;
}

/* ===================================
   Video Table Cell Styles
   =================================== */

/* Video Title Cell - Regular weight with multi-line support */
.video-title-cell {
    font-weight: normal !important;
    line-height: 1.4;
    max-height: 5.6em; /* 4 lines * 1.4 line-height */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break: break-word;
    text-wrap: wrap;
    white-space: normal;
    overflow-wrap: break-word;
}

/* Channel Cell - Regular weight with multi-line support */
.video-channel-cell {
    font-weight: normal !important;
    line-height: 1.4;
    max-height: 5.6em; /* 4 lines * 1.4 line-height */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break: break-word;
    text-wrap: wrap;
    white-space: normal;
    overflow-wrap: break-word;
}

/* Campaign Cell - Regular weight with multi-line support */
.video-campaign-cell {
    font-weight: normal !important;
    line-height: 1.4;
    max-height: 5.6em; /* 4 lines * 1.4 line-height */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break: break-word;
    text-wrap: wrap;
    white-space: normal;
    overflow-wrap: break-word;
}

/* Ensure Syncfusion Grid cells have proper height */
.e-grid .e-gridcontent .e-table .e-row .e-rowcell {
    vertical-align: top;
    padding: 8px !important;
    min-height: 60px;
}

/* Hover effect for video table rows */
.e-grid .e-gridcontent .e-table .e-row:hover .video-title-cell,
.e-grid .e-gridcontent .e-table .e-row:hover .video-channel-cell,
.e-grid .e-gridcontent .e-table .e-row:hover .video-campaign-cell {
    background-color: rgba(0, 123, 255, 0.05);
    transition: background-color 0.2s ease;
}
