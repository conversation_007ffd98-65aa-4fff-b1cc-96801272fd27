using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Controllers;
using TikTok.Permissions;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.HttpApi.Controllers.SystemNotification
{
    [RemoteService(Name = "TikTok")]
    [Route("api/system-notification-rules")]
    [Authorize(TikTokPermissions.NotificationRules.Default)]
    public class SystemNotificationRuleController : TikTokController
    {
        private readonly ISystemNotificationRuleAppService _appService;

        public SystemNotificationRuleController(ISystemNotificationRuleAppService appService)
        {
            _appService = appService;
        }

        [HttpGet]
        public async Task<ActionResult<SystemNotificationRuleListDto>> GetListAsync([FromQuery] SystemNotificationRuleSearchDto input)
        {
            var result = await _appService.GetListAsync(input);
            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SystemNotificationRuleDto>> GetAsync(Guid id)
        {
            var result = await _appService.GetAsync(id);
            return Ok(result);
        }

        [HttpPost]
        [Authorize(TikTokPermissions.SystemNotificationRules.Create)]
        public async Task<ActionResult<SystemNotificationRuleDto>> CreateAsync([FromBody] CreateSystemNotificationRuleDto input)
        {
            var result = await _appService.CreateAsync(input);
            return CreatedAtAction(nameof(GetAsync), new { id = result.Id }, result);
        }

        [HttpPut("{id}")]
        [Authorize(TikTokPermissions.NotificationRules.Edit)]
        public async Task<ActionResult<SystemNotificationRuleDto>> UpdateAsync(Guid id, [FromBody] UpdateSystemNotificationRuleDto input)
        {
            var result = await _appService.UpdateAsync(id, input);
            return Ok(result);
        }

        [HttpDelete("{id}")]
        [Authorize(TikTokPermissions.SystemNotificationRules.Delete)]
        public async Task<ActionResult> DeleteAsync(Guid id)
        {
            await _appService.DeleteAsync(id);
            return NoContent();
        }

        [HttpPatch("{id}/toggle-active")]
        [Authorize(TikTokPermissions.SystemNotificationRules.Edit)]
        public async Task<ActionResult<SystemNotificationRuleDto>> ToggleActiveAsync(Guid id)
        {
            var result = await _appService.ToggleActiveAsync(id);
            return Ok(result);
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<SystemNotificationRuleStatsDto>> GetStatisticsAsync()
        {
            var result = await _appService.GetStatisticsAsync();
            return Ok(result);
        }

        [HttpPost("validate")]
        [Authorize(TikTokPermissions.SystemNotificationRules.ManageRule)]
        public async Task<ActionResult<SystemNotificationRuleValidationDto>> ValidateRuleAsync([FromBody] object input)
        {
            var result = await _appService.ValidateRuleAsync(input);
            return Ok(result);
        }

        [HttpGet("fields/{entityType}")]
        public async Task<ActionResult<List<FieldMetadata>>> GetFieldsForEntityTypeAsync(string entityType)
        {
            var result = await _appService.GetFieldsForEntityTypeAsync(entityType);
            return Ok(result);
        }


        [HttpPost("{id}/trigger")]
        public async Task<ActionResult> TriggerRuleCheckAsync(Guid id)
        {
            await _appService.TriggerRuleCheckAsync(id);
            return Ok(new { message = "Rule check triggered successfully" });
        }


        [HttpGet("entity-types")]
        public ActionResult<Dictionary<string, string>> GetSupportedEntityTypes()
        {
            var entityTypes = new Dictionary<string, string>
            {
                ["RawGmvMaxProductCreativeReportEntity"] = "GMV Max Product Creative",
                ["RawGmvMaxProductCampaignReportEntity"] = "GMV Max Product Campaign",
                ["RawGmvMaxLiveCampaignReportEntity"] = "GMV Max Live Campaign"
            };

            return Ok(entityTypes);
        }


        [HttpGet("operators")]
        public ActionResult<Dictionary<string, string>> GetSupportedOperators()
        {
            var operators = new Dictionary<string, string>
            {
                ["Equals"] = "Equals",
                ["Contains"] = "Contains",
                ["StartsWith"] = "Starts With",
                ["EndsWith"] = "Ends With",
                ["IsEmpty"] = "Is Empty",
                ["IsNotEmpty"] = "Is Not Empty",
                ["GreaterThan"] = "Greater Than",
                ["LessThan"] = "Less Than",
                ["GreaterThanOrEqual"] = "Greater Than or Equal",
                ["LessThanOrEqual"] = "Less Than or Equal",
                ["Between"] = "Between",
                ["In"] = "In List",
                ["IsToday"] = "Is Today",
                ["IsYesterday"] = "Is Yesterday",
                ["IsThisWeek"] = "Is This Week",
                ["IsThisMonth"] = "Is This Month"
            };

            return Ok(operators);
        }

        [HttpGet("recipient-types")]
        public ActionResult<Dictionary<string, string>> GetSupportedRecipientTypes()
        {
            var recipientTypes = new Dictionary<string, string>
            {
                ["RuleCreator"] = "Rule Creator",
                ["SpecificUsers"] = "Specific Users",
                ["EntityOwners"] = "Entity Owners",
                ["PermissionBased"] = "Permission Based",
                ["AllUsers"] = "All Users"
            };

            return Ok(recipientTypes);
        }

        [HttpGet("field-categories")]
        public ActionResult<Dictionary<string, string>> GetFieldCategories()
        {
            var categories = new Dictionary<string, string>
            {
                ["performance"] = "Performance",
                ["cost"] = "Cost",
                ["orders"] = "Orders",
                ["status"] = "Status",
                ["creative"] = "Creative",
                ["campaign"] = "Campaign",
                ["product"] = "Product"
            };

            return Ok(categories);
        }
    }
}
