using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu GMV Max Identities
    /// </summary>
    public class GmvMaxIdentitiesSyncApplicationAppService : ApplicationService, IGmvMaxIdentitiesSyncApplicationAppService
    {
        private readonly IGmvMaxIdentitiesSyncService _gmvMaxIdentitiesSyncService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="gmvMaxIdentitiesSyncService">GMV Max Identities sync service</param>
        public GmvMaxIdentitiesSyncApplicationAppService(IGmvMaxIdentitiesSyncService gmvMaxIdentitiesSyncService)
        {
            _gmvMaxIdentitiesSyncService = gmvMaxIdentitiesSyncService;
        }

        /// <summary>
        /// Đồng bộ GMV Max Identities theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxIdentitiesSyncResult> SyncGmvMaxIdentitiesAsync(string advertiserId, string bcId)
        {
            return await _gmvMaxIdentitiesSyncService.SyncGmvMaxIdentitiesAsync(advertiserId, bcId);
        }

        /// <summary>
        /// Đồng bộ nhiều GMV Max Identities cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxIdentitiesSyncResult> SyncManyGmvMaxIdentitiesAsync(string bcId, List<string>? advertiserIds = null)
        {
            return await _gmvMaxIdentitiesSyncService.SyncManyGmvMaxIdentitiesAsync(bcId, advertiserIds);
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Identities cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxIdentitiesSyncResult> SyncAllGmvMaxIdentitiesAsync()
        {
            return await _gmvMaxIdentitiesSyncService.SyncAllGmvMaxIdentitiesAsync();
        }
    }
}
