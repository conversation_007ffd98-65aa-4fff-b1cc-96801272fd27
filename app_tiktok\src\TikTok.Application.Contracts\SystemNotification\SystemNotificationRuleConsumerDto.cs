using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TikTok.Application.Contracts.SystemNotification
{
    public class SystemNotificationRuleConsumerDto
    {
        public Guid Id { get; set; }
        public Guid SystemNotificationRuleId { get; set; }
        public Guid? BcId { get; set; }
        public Guid? AdAccountId { get; set; }
        public string ConsumerType { get; set; } = "AdAccount";
        public string? ConsumerName { get; set; }
        public string? ConsumerId { get; set; }
        public string? BcName { get; set; }
        public string? AdAccountName { get; set; }
    }

    public class SystemNotificationRuleConsumersResponse
    {
        public Guid RuleId { get; set; }
        public List<SystemNotificationRuleConsumerAdAccountDto> AdAccounts { get; set; } = new();
        public List<SystemNotificationRuleConsumerBusinessCenterDto> BusinessCenters { get; set; } = new();
    }

    public class SystemNotificationRuleConsumerAdAccountDto
    {
        public Guid Id { get; set; }
        public string AdvertiserId { get; set; } = string.Empty;
        public string AdAccountName { get; set; } = string.Empty;
        public string BcName { get; set; } = string.Empty;
        public bool IsAssigned { get; set; }
    }

    public class SystemNotificationRuleConsumerBusinessCenterDto
    {
        public Guid Id { get; set; }
        public string BcId { get; set; } = string.Empty;
        public string BcName { get; set; } = string.Empty;
        public bool IsAssigned { get; set; }
    }

    public class AddSystemNotificationRuleConsumersDto
    {
        [Required]
        public Guid SystemNotificationRuleId { get; set; }

        [Required]
        public List<Guid> AdAccountIds { get; set; } = new();
    }

    public class RemoveSystemNotificationRuleConsumersDto
    {
        [Required]
        public Guid SystemNotificationRuleId { get; set; }

        [Required]
        public List<Guid> ConsumerIds { get; set; } = new();
    }
}
