using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi.Models;
using TikTokBusinessApi;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu GMV Max Campaign Posts
    /// </summary>
    public class GmvMaxCampaignPostsSyncService : BaseSyncService, IGmvMaxCampaignPostsSyncService
    {
        private const string SERVICE_NAME = "GmvMaxCampaignPostsSync";

        private readonly IRawGmvMaxCampaignPostsRepository _gmvMaxCampaignPostsRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IBusinessCenterRepository _businessCenterRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        public GmvMaxCampaignPostsSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxCampaignPostsRepository gmvMaxCampaignPostsRepository,
            IAssetRepository assetRepository,
            IBusinessCenterRepository businessCenterRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ILogger<GmvMaxCampaignPostsSyncService> logger
            ) : base(serviceProvider, logger)
        {
            _gmvMaxCampaignPostsRepository = gmvMaxCampaignPostsRepository;
            _assetRepository = assetRepository;
            _businessCenterRepository = businessCenterRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo Business Center ID
        /// Lấy tất cả campaignId và advertiserId từ bảng RawGmvMaxCampaignsEntity
        /// </summary>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsAsync(string bcId)
        {
            var result = new GmvMaxCampaignPostsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max Campaign Posts cho BC: {BcId}", bcId);

                // Lấy danh sách campaigns từ GmvMax
                var campaigns = await GetGmvMaxCampaignsAsync(bcId);

                if (!campaigns.Any())
                {
                    _logger.LogDebug("Không tìm thấy campaign nào cho BC: {BcId}", bcId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug("Tìm thấy {Count} campaign cho BC: {BcId}", campaigns.Count, bcId);

                // Nhóm campaigns theo AdvertiserId, StoreId và StoreAuthorizedBcId
                var advertiserStoreGroups = campaigns
                    .Where(c => !string.IsNullOrEmpty(c.AdvertiserId) &&
                               !string.IsNullOrEmpty(c.StoreId) &&
                               !string.IsNullOrEmpty(c.StoreAuthorizedBcId))
                    .GroupBy(c => new { c.AdvertiserId, c.StoreId, c.StoreAuthorizedBcId })
                    .ToList();

                _logger.LogDebug("Tìm thấy {Count} nhóm AdvertiserId-StoreId-StoreAuthorizedBcId cho BC: {BcId}", advertiserStoreGroups.Count, bcId);

                var unauthorizedAdvertiserIds = new List<string>();

                // Đồng bộ cho từng nhóm AdvertiserId-StoreId-StoreAuthorizedBcId
                foreach (var group in advertiserStoreGroups)
                {
                    try
                    {
                        var advertiserId = group.Key.AdvertiserId;
                        var storeId = group.Key.StoreId;
                        var storeAuthorizedBcId = group.Key.StoreAuthorizedBcId;
                        var groupCampaigns = group.ToList();

                        _logger.LogDebug("Đồng bộ GMV Max Campaign Posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, StoreAuthorizedBcId: {StoreAuthorizedBcId}, Campaigns: {CampaignCount}",
                            advertiserId, storeId, storeAuthorizedBcId, groupCampaigns.Count);

                        var groupResult = await SyncGmvMaxCampaignPostsByAdvertiserStoreAsync(
                            advertiserId,
                            storeId,
                            bcId,
                            groupCampaigns,
                            unauthorizedAdvertiserIds);

                        // Merge basic results only
                        result.NewRecords += groupResult.NewRecords;
                        result.UpdatedRecords += groupResult.UpdatedRecords;
                        result.ErrorRecords += groupResult.ErrorRecords;
                        result.TotalPagesProcessed += groupResult.TotalPagesProcessed;
                        result.TotalPostsFromApi += groupResult.TotalPostsFromApi;
                        result.CampaignCount += groupResult.CampaignCount;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, StoreAuthorizedBcId: {StoreAuthorizedBcId}",
                            group.Key.AdvertiserId, group.Key.StoreId, group.Key.StoreAuthorizedBcId);
                        result.ErrorRecords++;
                    }
                }

                result.AdvertiserCount = advertiserStoreGroups.Select(g => g.Key.AdvertiserId).Distinct().Count();
                result.UnauthorizedAdvertiserIds = unauthorizedAdvertiserIds;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ GMV Max Campaign Posts cho BC: {BcId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ GMV Max Campaign Posts cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max Campaign Posts: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo AdvertiserId và StoreId
        /// </summary>
        private async Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsByAdvertiserStoreAsync(
            string advertiserId,
            string storeId,
            string bcId,
            List<RawGmvMaxCampaignsEntity> campaigns,
            List<string>? unauthorizedAdvertiserIds = null)
        {
            var result = new GmvMaxCampaignPostsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max Campaign Posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Campaigns: {CampaignCount}",
                    advertiserId, storeId, campaigns.Count);

                // Tạo TikTok client
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Đồng bộ posts cho tất cả campaigns của advertiser-store này
                await ProcessPostsByAdvertiserStoreAsync(tikTokClient, advertiserId, storeId, bcId, campaigns, result, unauthorizedAdvertiserIds);

                result.CampaignCount = campaigns.Count;
                result.AdvertiserCount = 1;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ GMV Max Campaign Posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    advertiserId, storeId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ GMV Max Campaign Posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}",
                    advertiserId, storeId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max Campaign Posts: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}",
                    advertiserId, storeId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts theo Campaign ID (method gốc được giữ lại cho tương thích)
        /// </summary>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncGmvMaxCampaignPostsByCampaignAsync(string campaignId, string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null)
        {
            var result = new GmvMaxCampaignPostsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max Campaign Posts cho Campaign: {CampaignId}, Advertiser: {AdvertiserId}", campaignId, advertiserId);

                // Tạo TikTok client
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Xử lý với pagination trực tiếp thay vì lấy tất cả trước
                await ProcessPostsWithPagination(tikTokClient, campaignId, advertiserId, bcId, result, unauthorizedAdvertiserIds);

                result.CampaignCount = 1;
                result.AdvertiserCount = 1;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ GMV Max Campaign Posts cho Campaign: {CampaignId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    campaignId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ GMV Max Campaign Posts cho Campaign: {CampaignId}", campaignId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max Campaign Posts: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho Campaign: {CampaignId}", campaignId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign Posts cho nhiều Campaign của một Advertiser
        /// </summary>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncManyGmvMaxCampaignPostsAsync(string advertiserId, string bcId, List<string>? campaignIds = null)
        {
            var result = new GmvMaxCampaignPostsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Nếu campaignIds là null, lấy tất cả campaign IDs từ asset repository
                if (campaignIds == null)
                {
                    var campaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(advertiserId);
                    campaignIds = campaigns.Select(x => x.CampaignId).ToList();
                    _logger.LogDebug("Lấy danh sách Campaign IDs từ Asset Repository cho Advertiser: {AdvertiserId}, Số Campaign: {Count}", advertiserId, campaignIds.Count);
                }

                if (!campaignIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Campaign nào cho Advertiser: {AdvertiserId}", advertiserId);
                    result.ErrorMessage = $"Không tìm thấy Campaign nào cho Advertiser: {advertiserId}";
                    return result;
                }

                _logger.LogDebug("Bắt đầu đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}, Số Campaign: {Count}", advertiserId, campaignIds.Count);

                var unauthorizedAdvertiserIds = new List<string>();
                foreach (var campaignId in campaignIds)
                {
                    try
                    {
                        var singleResult = await SyncGmvMaxCampaignPostsByCampaignAsync(campaignId, advertiserId, bcId, unauthorizedAdvertiserIds);

                        // Merge basic results only
                        result.NewRecords += singleResult.NewRecords;
                        result.UpdatedRecords += singleResult.UpdatedRecords;
                        result.ErrorRecords += singleResult.ErrorRecords;
                        result.TotalPagesProcessed += singleResult.TotalPagesProcessed;
                        result.TotalPostsFromApi += singleResult.TotalPostsFromApi;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho Campaign: {CampaignId}", campaignId);
                        result.ErrorRecords++;
                    }
                }

                result.CampaignCount = campaignIds.Count;
                result.AdvertiserCount = 1;
                result.UnauthorizedAdvertiserIds = unauthorizedAdvertiserIds;

                _logger.LogDebug("Hoàn thành đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}, Tổng: {Total}", advertiserId, result.TotalSynced);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}", advertiserId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ nhiều GMV Max Campaign Posts: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều GMV Max Campaign Posts cho Advertiser: {AdvertiserId}", advertiserId);
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign Posts cho tất cả Business Centers
        /// </summary>
        public async Task<GmvMaxCampaignPostsSyncResult> SyncAllGmvMaxCampaignPostsAsync()
        {
            var result = new GmvMaxCampaignPostsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả GMV Max Campaign Posts cho tất cả BC");

                // Lấy tất cả Business Centers
                var businessCenters = await _businessCenterRepository.GetListAsync();

                if (!businessCenters.Any())
                {
                    _logger.LogWarning("Không tìm thấy Business Center nào");
                    result.ErrorMessage = "Không tìm thấy Business Center nào";
                    return result;
                }

                foreach (var bc in businessCenters)
                {
                    try
                    {
                        // Lấy tất cả advertisers cho BC này
                        var advertisers = await _assetRepository.GetByBcIdAsync(bc.BcId, assetType: Enums.AssetType.ADVERTISER);

                        foreach (var advertiser in advertisers)
                        {
                            var bcResult = await SyncManyGmvMaxCampaignPostsAsync(advertiser.AssetId, bc.BcId);

                            // Merge basic results only
                            result.NewRecords += bcResult.NewRecords;
                            result.UpdatedRecords += bcResult.UpdatedRecords;
                            result.ErrorRecords += bcResult.ErrorRecords;
                            result.CampaignCount += bcResult.CampaignCount;
                            result.TotalPagesProcessed += bcResult.TotalPagesProcessed;
                            result.TotalPostsFromApi += bcResult.TotalPostsFromApi;

                            result.UnauthorizedAdvertiserIds.AddRange(bcResult.UnauthorizedAdvertiserIds);
                        }

                        result.AdvertiserCount += advertisers.Count;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign Posts cho BC: {BcId}", bc.BcId);
                        result.ErrorRecords++;
                    }
                }

                result.BcCount = businessCenters.Count;
                _logger.LogDebug("Hoàn thành đồng bộ tất cả GMV Max Campaign Posts cho tất cả BC, Tổng: {Total}", result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả GMV Max Campaign Posts: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả GMV Max Campaign Posts cho tất cả BC");
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Lấy danh sách GmvMax campaigns từ database
        /// </summary>
        private async Task<List<RawGmvMaxCampaignsEntity>> GetGmvMaxCampaignsAsync(string bcId)
        {
            var allCampaigns = await _gmvMaxCampaignsRepository.GetByBcIdAsync(bcId);

            _logger.LogDebug("Tìm thấy {Count} campaign cho BC: {BcId}", allCampaigns.Count, bcId);

            return allCampaigns;
        }

        /// <summary>
        /// Xử lý posts cho một advertiser-store cụ thể
        /// </summary>
        private async Task ProcessPostsByAdvertiserStoreAsync(
            TikTokBusinessApiClient tikTokClient,
            string advertiserId,
            string storeId,
            string bcId,
            List<RawGmvMaxCampaignsEntity> campaigns,
            GmvMaxCampaignPostsSyncResult result,
            List<string>? unauthorizedAdvertiserIds)
        {
            var page = 1;
            const int pageSize = 50; // Max page size for GMV Max API

            try
            {
                // Lấy StoreAuthorizedBcId từ campaign đầu tiên (vì cùng store thì BC ID giống nhau)
                var storeAuthorizedBcId = campaigns.FirstOrDefault()?.StoreAuthorizedBcId;

                if (string.IsNullOrEmpty(storeAuthorizedBcId))
                {
                    _logger.LogWarning("Không tìm thấy StoreAuthorizedBcId cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}. Bỏ qua nhóm này.",
                        advertiserId, storeId);
                    return;
                }

                _logger.LogDebug("Sử dụng StoreAuthorizedBcId: {StoreAuthorizedBcId} cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}",
                    storeAuthorizedBcId, advertiserId, storeId);

                // Lấy danh sách existing posts cho tất cả campaigns của advertiser-store này để tối ưu performance
                var campaignIds = campaigns.Select(c => c.CampaignId).ToList();
                var existingPosts = new List<RawGmvMaxCampaignPostsEntity>();

                foreach (var campaignId in campaignIds)
                {
                    var campaignPosts = await _gmvMaxCampaignPostsRepository.GetByCampaignIdAsync(campaignId);
                    existingPosts.AddRange(campaignPosts);
                }

                var existingPostDict = existingPosts.ToDictionary(x => $"{x.CampaignId}_{x.ItemId}", x => x);
                var processedItemIds = new HashSet<string>();

                // Process each page for this advertiser-store
                while (true)
                {
                    var request = new GMVMaxCampaignPostsRequest
                    {
                        AdvertiserId = advertiserId,
                        StoreId = storeId,
                        StoreAuthorizedBcId = storeAuthorizedBcId,
                        NeedAuthCodeVideo = true,
                        Page = page,
                        PageSize = pageSize
                    };

                    _logger.LogDebug("Xử lý GMV Max Campaign Posts - AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Page: {Page}",
                        advertiserId, storeId, page);

                    var response = await tikTokClient.GMVMax.GetGmvMaxCampaignPostsAsync(request);

                    if (!TikTokApiCodes.IsSuccess(response.Code))
                    {
                        // Xử lý lỗi quyền truy cập
                        if (AdAccountSyncHelper.IsPermissionError(response) && unauthorizedAdvertiserIds != null)
                        {
                            var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(response.Message);
                            if (batchUnauthorizedIds?.Count > 0)
                            {
                                unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                            }
                            else
                            {
                                unauthorizedAdvertiserIds.Add(advertiserId);
                            }
                            _logger.LogWarning("Không có quyền truy cập Campaign Posts: AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Code: {Code}",
                                advertiserId, storeId, response.Code);
                            break;
                        }

                        throw new BusinessException(response.Code.ToString(),
                            $"Không thể lấy danh sách campaign posts cho advertiser {advertiserId}, store {storeId}: {response.Message}");
                    }

                    if (response?.Data?.ItemList == null || !response.Data.ItemList.Any())
                    {
                        _logger.LogDebug("Không có thêm campaign posts nào cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Page: {Page}",
                            advertiserId, storeId, page);
                        break;
                    }

                    // Process this page for all campaigns of this advertiser-store
                    await ProcessAndSavePostsPageForAdvertiserStore(response.Data.ItemList, campaigns, advertiserId, bcId,
                        result, existingPostDict, processedItemIds, page);

                    result.TotalPostsFromApi += response.Data.ItemList.Count;
                    result.TotalPagesProcessed++;

                    _logger.LogDebug("Đã xử lý {Count} posts từ trang {Page} cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Total: {Total}",
                        response.Data.ItemList.Count, page, advertiserId, storeId, response.Data.PageInfo?.TotalNumber ?? 0);

                    // Check if there are more pages
                    if (response.Data.PageInfo?.TotalPage <= page)
                    {
                        break;
                    }

                    page++;
                }

                _logger.LogDebug("Hoàn thành xử lý campaign posts cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}, Tổng pages: {Pages}, Tổng posts: {Posts}",
                    advertiserId, storeId, result.TotalPagesProcessed, result.TotalPostsFromApi);
            }
            catch (BusinessException)
            {
                throw; // Re-throw business exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xử lý pagination cho AdvertiserId: {AdvertiserId}, StoreId: {StoreId}", advertiserId, storeId);
                throw;
            }
        }

        /// <summary>
        /// Xử lý posts với pagination tương tự AssetSyncService (method cũ cho tương thích)
        /// </summary>
        private async Task ProcessPostsWithPagination(
            TikTokBusinessApiClient tikTokClient,
            string campaignId,
            string advertiserId,
            string bcId,
            GmvMaxCampaignPostsSyncResult result,
            List<string>? unauthorizedAdvertiserIds)
        {
            var page = 1;
            const int pageSize = 50; // Max page size for GMV Max API

            try
            {
                // First, get campaign info to get store details
                var campaignInfoResponse = await tikTokClient.GMVMax.GetCampaignInfoAsync(
                    advertiserId,
                    campaignId);

                if (!TikTokApiCodes.IsSuccess(campaignInfoResponse.Code))
                {
                    // Xử lý lỗi quyền truy cập
                    if (AdAccountSyncHelper.IsPermissionError(campaignInfoResponse) && unauthorizedAdvertiserIds != null)
                    {
                        var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(campaignInfoResponse.Message);
                        if (batchUnauthorizedIds?.Count > 0)
                        {
                            unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                        }
                        else
                        {
                            unauthorizedAdvertiserIds.Add(advertiserId);
                        }
                        _logger.LogWarning("Không có quyền truy cập Campaign: {CampaignId}, Advertiser: {AdvertiserId}, Code: {Code}",
                            campaignId, advertiserId, campaignInfoResponse.Code);
                        return;
                    }

                    throw new BusinessException(campaignInfoResponse.Code.ToString(),
                        $"Không thể lấy thông tin campaign {campaignId}: {campaignInfoResponse.Message}");
                }

                var campaignInfo = campaignInfoResponse.Data;
                if (campaignInfo == null || string.IsNullOrEmpty(campaignInfo.StoreId) || string.IsNullOrEmpty(campaignInfo.StoreAuthorizedBcId))
                {
                    _logger.LogWarning("Campaign {CampaignId} không có thông tin store hoặc BC ID cần thiết", campaignId);
                    return;
                }

                // Lấy danh sách existing items để tối ưu performance
                var existingPosts = await _gmvMaxCampaignPostsRepository.GetByCampaignIdAsync(campaignId);
                var existingPostDict = existingPosts.ToDictionary(x => x.ItemId, x => x);
                var processedItemIds = new HashSet<string>();

                // Process each page
                while (true)
                {
                    var request = new GMVMaxCampaignPostsRequest
                    {
                        AdvertiserId = advertiserId,
                        StoreId = campaignInfo.StoreId,
                        StoreAuthorizedBcId = campaignInfo.StoreAuthorizedBcId,
                        NeedAuthCodeVideo = true,
                        Page = page,
                        PageSize = pageSize
                    };

                    // Get identities for this campaign if available
                    if (campaignInfo.IdentityList != null && campaignInfo.IdentityList.Any())
                    {
                        request.IdentityList.AddRange(campaignInfo.IdentityList);
                    }

                    _logger.LogDebug("Xử lý GMV Max Campaign Posts - Campaign: {CampaignId}, Page: {Page}", campaignId, page);

                    var response = await tikTokClient.GMVMax.GetGmvMaxCampaignPostsAsync(request);

                    if (!TikTokApiCodes.IsSuccess(response.Code))
                    {
                        // Xử lý lỗi quyền truy cập
                        if (AdAccountSyncHelper.IsPermissionError(response) && unauthorizedAdvertiserIds != null)
                        {
                            var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(response.Message);
                            if (batchUnauthorizedIds?.Count > 0)
                            {
                                unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                            }
                            else
                            {
                                unauthorizedAdvertiserIds.Add(advertiserId);
                            }
                            _logger.LogWarning("Không có quyền truy cập Campaign Posts: {CampaignId}, Advertiser: {AdvertiserId}, Code: {Code}",
                                campaignId, advertiserId, response.Code);
                            break;
                        }

                        throw new BusinessException(response.Code.ToString(),
                            $"Không thể lấy danh sách campaign posts cho {campaignId}: {response.Message}");
                    }

                    if (response?.Data?.ItemList == null || !response.Data.ItemList.Any())
                    {
                        _logger.LogDebug("Không có thêm campaign posts nào cho Campaign: {CampaignId}, Page: {Page}", campaignId, page);
                        break;
                    }

                    // Process this page
                    await ProcessAndSavePostsPage(response.Data.ItemList, campaignId, advertiserId, bcId,
                        result, existingPostDict, processedItemIds, page);

                    result.TotalPostsFromApi += response.Data.ItemList.Count;
                    result.TotalPagesProcessed++;

                    _logger.LogDebug("Đã xử lý {Count} posts từ trang {Page} cho Campaign: {CampaignId}, Total: {Total}",
                        response.Data.ItemList.Count, page, campaignId, response.Data.PageInfo?.TotalNumber ?? 0);

                    // Check if there are more pages
                    if (response.Data.PageInfo?.TotalPage <= page)
                    {
                        break;
                    }

                    page++;
                }

                _logger.LogDebug("Hoàn thành xử lý campaign posts cho Campaign: {CampaignId}, Tổng pages: {Pages}, Tổng posts: {Posts}",
                    campaignId, result.TotalPagesProcessed, result.TotalPostsFromApi);
            }
            catch (BusinessException)
            {
                throw; // Re-throw business exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xử lý pagination cho Campaign: {CampaignId}", campaignId);
                throw;
            }
        }

        /// <summary>
        /// Xử lý và lưu posts cho tất cả campaigns của một advertiser-store
        /// </summary>
        private async Task ProcessAndSavePostsPageForAdvertiserStore(
            List<GMVMaxItem> postList,
            List<RawGmvMaxCampaignsEntity> campaigns,
            string advertiserId,
            string bcId,
            GmvMaxCampaignPostsSyncResult result,
            Dictionary<string, RawGmvMaxCampaignPostsEntity> existingPostDict,
            HashSet<string> processedItemIds,
            int page)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            try
            {
                var newEntities = new List<RawGmvMaxCampaignPostsEntity>();
                var updatedEntities = new List<RawGmvMaxCampaignPostsEntity>();

                // Lặp qua từng campaign để tạo record cho mỗi post
                foreach (var campaign in campaigns)
                {
                    // Convert dữ liệu từ API thành danh sách entity cho campaign này
                    var mappedEntities = MapListApiDataToEntities(postList, campaign.CampaignId, advertiserId, bcId);

                    // Duyệt từng post của campaign này
                    foreach (var mappedEntity in mappedEntities)
                    {
                        try
                        {
                            var uniqueKey = $"{campaign.CampaignId}_{mappedEntity.ItemId}";

                            // Skip if already processed in this sync session
                            if (processedItemIds.Contains(uniqueKey))
                            {
                                continue;
                            }

                            processedItemIds.Add(uniqueKey);

                            if (!existingPostDict.TryGetValue(uniqueKey, out var existingEntity))
                            {
                                // Thêm mới
                                mappedEntity.SyncedAt = DateTime.UtcNow;
                                newEntities.Add(mappedEntity);
                                result.NewRecords++;
                            }
                            else
                            {
                                // Cập nhật nếu có thay đổi
                                if (existingEntity.HasChanged(mappedEntity))
                                {
                                    existingEntity.UpdateFromNewData(mappedEntity);
                                    existingEntity.SyncedAt = DateTime.UtcNow;
                                    updatedEntities.Add(existingEntity);
                                    result.UpdatedRecords++;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Lỗi khi xử lý GMV Max Campaign Post: {ItemId} cho Campaign: {CampaignId} tại trang {Page}",
                                mappedEntity.ItemId, campaign.CampaignId, page);
                            result.ErrorRecords++;
                        }
                    }
                }

                // Lưu các bản ghi mới (bulk insert)
                if (newEntities.Any())
                {
                    await _gmvMaxCampaignPostsRepository.InsertManyAsync(newEntities);
                    _logger.LogDebug("Bulk insert {Count} GMV Max Campaign Post cho trang {Page}, AdvertiserId: {AdvertiserId}",
                        newEntities.Count, page, advertiserId);
                }

                // Cập nhật các bản ghi đã tồn tại (bulk update)
                if (updatedEntities.Any())
                {
                    await _gmvMaxCampaignPostsRepository.UpdateManyAsync(updatedEntities);
                    _logger.LogDebug("Bulk update {Count} GMV Max Campaign Post cho trang {Page}, AdvertiserId: {AdvertiserId}",
                        updatedEntities.Count, page, advertiserId);
                }

                await uow.CompleteAsync();
                _logger.LogDebug("Hoàn thành xử lý trang {Page} cho AdvertiserId {AdvertiserId}: {NewCount} mới, {UpdateCount} cập nhật GMV Max Campaign Post",
                    page, advertiserId, newEntities.Count, updatedEntities.Count);
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                _logger.LogError(ex, "Lỗi khi xử lý và lưu GMV Max Campaign Posts trang {Page} cho AdvertiserId: {AdvertiserId}", page, advertiserId);
                throw;
            }
        }

        /// <summary>
        /// Xử lý và lưu danh sách posts cho một trang theo pattern của AssetSyncService
        /// </summary>
        private async Task ProcessAndSavePostsPage(
            List<GMVMaxItem> postList,
            string campaignId,
            string advertiserId,
            string bcId,
            GmvMaxCampaignPostsSyncResult result,
            Dictionary<string, RawGmvMaxCampaignPostsEntity> existingPostDict,
            HashSet<string> processedItemIds,
            int page)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            try
            {
                var newEntities = new List<RawGmvMaxCampaignPostsEntity>();
                var updatedEntities = new List<RawGmvMaxCampaignPostsEntity>();

                // Convert dữ liệu từ API thành danh sách entity
                var mappedEntities = MapListApiDataToEntities(postList, campaignId, advertiserId, bcId);

                // Duyệt từng bản ghi xử lý thêm mới/cập nhật
                foreach (var mappedEntity in mappedEntities)
                {
                    try
                    {
                        // Skip if already processed in this sync session
                        if (processedItemIds.Contains(mappedEntity.ItemId))
                        {
                            continue;
                        }

                        processedItemIds.Add(mappedEntity.ItemId);

                        if (!existingPostDict.TryGetValue(mappedEntity.ItemId, out var existingEntity))
                        {
                            // Thêm mới
                            mappedEntity.SyncedAt = DateTime.UtcNow;
                            newEntities.Add(mappedEntity);
                            result.NewRecords++;
                        }
                        else
                        {
                            // Cập nhật nếu có thay đổi
                            if (existingEntity.HasChanged(mappedEntity))
                            {
                                existingEntity.UpdateFromNewData(mappedEntity);
                                existingEntity.SyncedAt = DateTime.UtcNow;
                                updatedEntities.Add(existingEntity);
                                result.UpdatedRecords++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý GMV Max Campaign Post: {ItemId} tại trang {Page}", mappedEntity.ItemId, page);
                        result.ErrorRecords++;
                    }
                }

                // Lưu các bản ghi mới (bulk insert)
                if (newEntities.Any())
                {
                    await _gmvMaxCampaignPostsRepository.InsertManyAsync(newEntities);
                    _logger.LogDebug("Bulk insert {Count} GMV Max Campaign Post cho trang {Page}", newEntities.Count, page);
                }

                // Cập nhật các bản ghi đã tồn tại (bulk update)
                if (updatedEntities.Any())
                {
                    await _gmvMaxCampaignPostsRepository.UpdateManyAsync(updatedEntities);
                    _logger.LogDebug("Bulk update {Count} GMV Max Campaign Post cho trang {Page}", updatedEntities.Count, page);
                }

                await uow.CompleteAsync();
                _logger.LogDebug("Hoàn thành xử lý trang {Page}: {NewCount} mới, {UpdateCount} cập nhật GMV Max Campaign Post",
                    page, newEntities.Count, updatedEntities.Count);
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                _logger.LogError(ex, "Lỗi khi xử lý và lưu GMV Max Campaign Posts trang {Page} cho Campaign: {CampaignId}", page, campaignId);
                throw;
            }
        }

        /// <summary>
        /// Xử lý và lưu danh sách posts theo pattern cũ (để tương thích)
        /// </summary>
        private async Task ProcessAndSavePosts(
            List<GMVMaxItem> postList,
            string campaignId,
            string advertiserId,
            string bcId,
            GmvMaxCampaignPostsSyncResult result)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            try
            {
                var newEntities = new List<RawGmvMaxCampaignPostsEntity>();
                var updatedEntities = new List<RawGmvMaxCampaignPostsEntity>();

                // Convert dữ liệu từ API thành danh sách entity
                var mappedEntities = MapListApiDataToEntities(postList, campaignId, advertiserId, bcId);

                // Lấy danh sách item IDs để query DB
                var itemIds = mappedEntities.Select(x => x.ItemId).ToList();

                // Lấy danh sách đã có trong DB
                var existingEntities = await GetExistingEntitiesByCampaignAndItemIds(campaignId, itemIds);
                var existingDict = existingEntities.ToDictionary(x => x.ItemId, x => x);

                // Duyệt từng bản ghi xử lý thêm mới/cập nhật
                foreach (var mappedEntity in mappedEntities)
                {
                    try
                    {
                        // Chỉ đếm cơ bản, không tính thống kê phức tạp

                        if (!existingDict.TryGetValue(mappedEntity.ItemId, out var existingEntity))
                        {
                            // Thêm mới
                            mappedEntity.SyncedAt = DateTime.UtcNow;
                            newEntities.Add(mappedEntity);
                            result.NewRecords++;
                        }
                        else
                        {
                            // Cập nhật nếu có thay đổi
                            if (existingEntity.HasChanged(mappedEntity))
                            {
                                existingEntity.UpdateFromNewData(mappedEntity);
                                existingEntity.SyncedAt = DateTime.UtcNow;
                                updatedEntities.Add(existingEntity);
                                result.UpdatedRecords++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý GMV Max Campaign Post: {ItemId}", mappedEntity.ItemId);
                        result.ErrorRecords++;
                    }
                }

                // Lưu các bản ghi mới
                if (newEntities.Any())
                {
                    await _gmvMaxCampaignPostsRepository.InsertManyAsync(newEntities);
                    _logger.LogDebug("Thêm mới {Count} GMV Max Campaign Post", newEntities.Count);
                }

                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxCampaignPostsRepository.UpdateManyAsync(updatedEntities);
                    _logger.LogDebug("Cập nhật {Count} GMV Max Campaign Post", updatedEntities.Count);
                }

                await uow.CompleteAsync();
                _logger.LogDebug("Hoàn thành xử lý và lưu {NewCount} mới, {UpdateCount} cập nhật GMV Max Campaign Post",
                    newEntities.Count, updatedEntities.Count);
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                _logger.LogError(ex, "Lỗi khi xử lý và lưu GMV Max Campaign Posts cho Campaign: {CampaignId}", campaignId);
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách entity đã tồn tại theo campaign và item IDs
        /// </summary>
        private async Task<List<RawGmvMaxCampaignPostsEntity>> GetExistingEntitiesByCampaignAndItemIds(string campaignId, List<string> itemIds)
        {
            var allEntities = await _gmvMaxCampaignPostsRepository.GetByCampaignIdAsync(campaignId);
            return allEntities.Where(x => itemIds.Contains(x.ItemId)).ToList();
        }

        /// <summary>
        /// Convert danh sách dữ liệu API thành danh sách entity
        /// </summary>
        private List<RawGmvMaxCampaignPostsEntity> MapListApiDataToEntities(List<GMVMaxItem> postList, string campaignId, string advertiserId, string bcId)
        {
            var entities = new List<RawGmvMaxCampaignPostsEntity>();

            foreach (var post in postList)
            {
                try
                {
                    var entity = MapApiDataToEntity(post, campaignId, advertiserId, bcId);
                    entities.Add(entity);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi map dữ liệu API sang Entity cho Post: {ItemId}",
                        post.ItemId);
                }
            }

            return entities;
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity
        /// </summary>
        private RawGmvMaxCampaignPostsEntity MapApiDataToEntity(GMVMaxItem post, string campaignId, string advertiserId, string bcId)
        {
            var entity = new RawGmvMaxCampaignPostsEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                CampaignId = campaignId,
                ItemId = post.ItemId ?? string.Empty,
                Text = post.Text
            };

            // Map SPU ID List (JSON array)
            if (post.SpuIdList != null && post.SpuIdList.Any())
            {
                try
                {
                    entity.SpuIdList = System.Text.Json.JsonSerializer.Serialize(post.SpuIdList);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Lỗi khi serialize SpuIdList cho Post: {ItemId}", post.ItemId);
                    entity.SpuIdList = string.Join(",", post.SpuIdList);
                }
            }

            // Map Identity Info
            if (post.IdentityInfo != null)
            {
                entity.IdentityDisplayName = post.IdentityInfo.DisplayName;
                entity.IdentityId = post.IdentityInfo.IdentityId;
                entity.IdentityType = post.IdentityInfo.IdentityType;
                entity.IdentityProfileImage = post.IdentityInfo.ProfileImage;
                entity.IdentityAuthorizedBcId = post.IdentityInfo.IdentityAuthorizedBcId;
            }

            // Map Video Info
            if (post.VideoInfo != null)
            {
                entity.VideoBitRate = post.VideoInfo.BitRate;
                entity.VideoDefinition = post.VideoInfo.Definition;
                entity.VideoDuration = post.VideoInfo.Duration;
                entity.VideoFormat = post.VideoInfo.Format;
                entity.VideoFps = post.VideoInfo.Fps;
                entity.VideoHeight = post.VideoInfo.Height;
                entity.VideoWidth = post.VideoInfo.Width;
                entity.VideoPreviewUrl = post.VideoInfo.PreviewUrl;
                entity.VideoSignature = post.VideoInfo.Signature;
                entity.VideoSize = post.VideoInfo.Size;
                entity.VideoCoverUrl = post.VideoInfo.VideoCoverUrl;
                entity.VideoId = post.VideoInfo.VideoId;
            }

            return entity;
        }





    }
}
