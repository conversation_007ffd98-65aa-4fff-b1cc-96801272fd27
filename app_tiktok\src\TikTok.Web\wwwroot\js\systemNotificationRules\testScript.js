/**
 * System Notification Rules Tester JavaScript
 * Handles all testing functionality for System Notification Rules
 */

window.SystemNotificationRulesTester = {
    // Configuration
    config: {
        apiBaseUrl: '/notification-rules-test',
        defaultTimeout: 30000,
    },

    // Initialize the tester
    init: function () {
        this.bindEvents();
        this.updateBadge();
    },

    // Bind all event handlers
    bindEvents: function () {
        // Product Rules Test
        document
            .getElementById('productTestForm')
            ?.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testProductRules();
            });

        // Campaign Rules Test
        document
            .getElementById('campaignTestForm')
            ?.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testCampaignRules();
            });

        // Validate Rules
        document
            .getElementById('validateAllRulesBtn')
            ?.addEventListener('click', () => {
                this.validateAllRules();
            });

        // Full Flow Test
        document
            .getElementById('fullFlowTestForm')
            ?.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testFullFlow();
            });

        // Campaign Grouping Test
        document
            .getElementById('campaignGroupingForm')
            ?.addEventListener('submit', (e) => {
                e.preventDefault();
                this.testCampaignGrouping();
            });

        // Error Scenarios Test
        document
            .getElementById('testErrorScenariosBtn')
            ?.addEventListener('click', () => {
                this.testErrorScenarios();
            });

        // View Rules Button
        document
            .getElementById('viewRulesBtn')
            ?.addEventListener('click', () => {
                this.viewActiveRules();
            });
    },

    // Update badge to show enhanced features
    updateBadge: function () {
        const badge = document.querySelector('.badge.bg-light.text-dark');
        if (badge) {
            badge.textContent = 'ENHANCED';
            badge.className = 'badge bg-success text-white';
        }
    },

    // Test Product Rules
    testProductRules: function () {
        const formData = new FormData(
            document.getElementById('productTestForm')
        );
        const data = {
            entityType: formData.get('entityType'),
            ruleId: formData.get('ruleId') || null,
            entityLimit: parseInt(formData.get('entityLimit')) || 5,
        };

        this.showLoading('Testing Product Rules...');
        this.makeRequest('test-product-rules', data)
            .then((response) =>
                this.displayResults('Product Rules Test', response)
            )
            .catch((error) => this.displayError('Product Rules Test', error));
    },

    // Test Campaign Rules
    testCampaignRules: function () {
        const formData = new FormData(
            document.getElementById('campaignTestForm')
        );
        const data = {
            entityType: formData.get('entityType'),
            ruleId: formData.get('ruleId') || null,
            entityLimit: parseInt(formData.get('entityLimit')) || 3,
        };

        this.showLoading('Testing Campaign Rules...');
        this.makeRequest('test-campaign-rules', data)
            .then((response) =>
                this.displayResults('Campaign Rules Test', response)
            )
            .catch((error) => this.displayError('Campaign Rules Test', error));
    },

    // Validate All Rules
    validateAllRules: function () {
        const entityType = document.getElementById('validateEntityType').value;
        const data = entityType ? { entityType } : {};

        this.showLoading('Validating Rules...');
        this.makeRequest('validate-rules', data, 'GET')
            .then((response) =>
                this.displayResults('Rules Validation', response)
            )
            .catch((error) => this.displayError('Rules Validation', error));
    },

    // Test Full Flow (Production-like)
    testFullFlow: function () {
        const formData = new FormData(
            document.getElementById('fullFlowTestForm')
        );
        const sendReal = document.getElementById(
            'sendRealNotifications'
        ).checked;

        const data = {
            entityType: formData.get('entityType'),
            ruleId: formData.get('ruleId') || null,
            entityLimit: parseInt(formData.get('entityLimit')) || 5,
            sendRealNotifications: sendReal,
        };

        const loadingText = sendReal
            ? 'Testing Full Flow with Real Notifications...'
            : 'Testing Full Flow (Simulation)...';

        this.showLoading(loadingText);
        this.makeRequest('test-full-notification-flow', data)
            .then((response) => this.displayResults('Full Flow Test', response))
            .catch((error) => this.displayError('Full Flow Test', error));
    },

    // Test Campaign Grouping
    testCampaignGrouping: function () {
        const formData = new FormData(
            document.getElementById('campaignGroupingForm')
        );
        const data = {
            entityType: formData.get('entityType'),
            entityLimit: parseInt(formData.get('entityLimit')) || 10,
        };

        this.showLoading('Testing Campaign Grouping...');
        this.makeRequest('test-campaign-grouping', data)
            .then((response) =>
                this.displayResults('Campaign Grouping Test', response)
            )
            .catch((error) =>
                this.displayError('Campaign Grouping Test', error)
            );
    },

    // Test Error Scenarios
    testErrorScenarios: function () {
        this.showLoading('Testing Error Scenarios...');
        this.makeRequest('test-error-scenarios', {})
            .then((response) =>
                this.displayResults('Error Scenarios Test', response)
            )
            .catch((error) => this.displayError('Error Scenarios Test', error));
    },

    // View Active Rules
    viewActiveRules: function () {
        this.showLoading('Loading Active Rules...');
        this.makeRequest('validate-rules', {}, 'GET')
            .then((response) => this.displayActiveRules(response))
            .catch((error) => this.displayError('Active Rules', error));
    },

    // Make HTTP request
    makeRequest: function (endpoint, data, method = 'POST') {
        const url = `${this.config.apiBaseUrl}/${endpoint}`;
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        };

        if (method === 'POST') {
            const formData = new URLSearchParams();
            Object.keys(data).forEach((key) => {
                if (data[key] !== null && data[key] !== undefined) {
                    formData.append(key, data[key]);
                }
            });
            options.body = formData;
        } else if (method === 'GET' && Object.keys(data).length > 0) {
            const params = new URLSearchParams(data);
            options.url = `${url}?${params}`;
        }

        return fetch(url, options)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(
                        `HTTP ${response.status}: ${response.statusText}`
                    );
                }
                return response.json();
            })
            .then((data) => {
                if (!data.success) {
                    throw new Error(data.message || 'Request failed');
                }
                return data;
            });
    },

    // Show loading state
    showLoading: function (message) {
        const resultArea = document.getElementById('testResultArea');
        resultArea.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-muted">${message}</p>
            </div>
        `;
    },

    // Display test results
    displayResults: function (testName, response) {
        const resultArea = document.getElementById('testResultArea');
        const timestamp = new Date().toLocaleString();

        let html = `
            <div class="alert alert-success">
                <h6><i class="fa-solid fa-check-circle me-2"></i>${testName} - ${timestamp}</h6>
                <p class="mb-0">${
                    response.message || 'Test completed successfully'
                }</p>
            </div>
        `;

        if (response.result) {
            html += this.formatTestResult(response.result);
        }

        if (response.errorTests) {
            html += this.formatErrorTests(response.errorTests);
        }

        if (response.rules) {
            html += this.formatRulesList(response.rules);
        }

        resultArea.innerHTML = html;
        resultArea.scrollTop = 0;
    },

    // Display error
    displayError: function (testName, error) {
        const resultArea = document.getElementById('testResultArea');
        const timestamp = new Date().toLocaleString();

        resultArea.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>${testName} - ${timestamp}</h6>
                <p class="mb-0"><strong>Error:</strong> ${
                    error.message || error
                }</p>
            </div>
        `;
        resultArea.scrollTop = 0;
    },

    // Format test result
    formatTestResult: function (result) {
        let html = '<div class="card mt-3"><div class="card-body">';

        // Basic info
        html += '<h6 class="card-title">Test Results</h6>';
        html += '<div class="row">';

        if (result.entitiesProcessed !== undefined) {
            html += `<div class="col-md-3"><strong>Entities Processed:</strong> ${result.entitiesProcessed}</div>`;
        }
        if (result.rulesTested !== undefined) {
            html += `<div class="col-md-3"><strong>Rules Tested:</strong> ${result.rulesTested}</div>`;
        }
        if (result.notificationsGenerated !== undefined) {
            html += `<div class="col-md-3"><strong>Notifications Generated:</strong> ${result.notificationsGenerated}</div>`;
        }
        if (result.notificationsSent !== undefined) {
            html += `<div class="col-md-3"><strong>Notifications Sent:</strong> ${result.notificationsSent}</div>`;
        }

        html += '</div>';

        // Campaign grouping details
        if (result.campaignGroups !== undefined) {
            html += `<div class="mt-2"><strong>Campaign Groups:</strong> ${result.campaignGroups}</div>`;
        }

        if (result.campaignGroupDetails) {
            html +=
                '<div class="mt-2"><strong>Campaign Group Details:</strong></div>';
            html += '<ul class="list-unstyled">';
            result.campaignGroupDetails.forEach((group) => {
                html += `<li>• Campaign ${group.campaignId}: ${group.entityCount} entities</li>`;
            });
            html += '</ul>';
        }

        // Processing time
        if (result.processingTimeMs !== undefined) {
            html += `<div class="mt-2"><strong>Processing Time:</strong> ${result.processingTimeMs.toFixed(
                2
            )}ms</div>`;
        }

        // Send real notifications flag
        if (result.sendRealNotifications !== undefined) {
            const badge = result.sendRealNotifications
                ? '<span class="badge bg-warning">Real Notifications Sent</span>'
                : '<span class="badge bg-info">Simulation Mode</span>';
            html += `<div class="mt-2"><strong>Mode:</strong> ${badge}</div>`;
        }

        html += '</div></div>';
        return html;
    },

    // Format error tests
    formatErrorTests: function (errorTests) {
        let html = '<div class="card mt-3"><div class="card-body">';
        html += '<h6 class="card-title">Error Scenario Tests</h6>';

        errorTests.forEach((test) => {
            const badge = test.success
                ? '<span class="badge bg-success">PASS</span>'
                : '<span class="badge bg-danger">FAIL</span>';

            html += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>${test.scenario}</strong>
                        ${badge}
                    </div>
                    <div class="small text-muted">
                        Result: ${test.result} | Error: ${test.error || 'None'}
                    </div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    },

    // Format rules list
    formatRulesList: function (rules) {
        let html = '<div class="card mt-3"><div class="card-body">';
        html += '<h6 class="card-title">Active Rules</h6>';

        if (rules.length === 0) {
            html += '<p class="text-muted">No active rules found.</p>';
        } else {
            html +=
                '<div class="table-responsive"><table class="table table-sm">';
            html +=
                '<thead><tr><th>Rule Name</th><th>Entity Type</th><th>Status</th><th>Valid</th></tr></thead>';
            html += '<tbody>';

            rules.forEach((rule) => {
                const statusBadge = rule.isActive
                    ? '<span class="badge bg-success">Active</span>'
                    : '<span class="badge bg-secondary">Inactive</span>';

                const validBadge = rule.isValid
                    ? '<span class="badge bg-success">Valid</span>'
                    : '<span class="badge bg-danger">Invalid</span>';

                html += `
                    <tr>
                        <td>${rule.ruleName || 'N/A'}</td>
                        <td>${rule.entityType || 'N/A'}</td>
                        <td>${statusBadge}</td>
                        <td>${validBadge}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
        }

        html += '</div></div>';
        return html;
    },

    // Display active rules
    displayActiveRules: function (response) {
        const resultArea = document.getElementById('testResultArea');
        const timestamp = new Date().toLocaleString();

        let html = `
            <div class="alert alert-info">
                <h6><i class="fa-solid fa-list me-2"></i>Active Rules - ${timestamp}</h6>
                <p class="mb-0">Found ${response.validRulesCount || 0} rules</p>
            </div>
        `;

        if (response.rules) {
            html += this.formatRulesList(response.rules);
        }

        resultArea.innerHTML = html;
        resultArea.scrollTop = 0;
    },

    // Clear results
    clearResults: function () {
        document.getElementById('testResultArea').innerHTML =
            '<div class="text-center text-muted"><i class="fa-solid fa-clipboard-question fa-2x mb-2"></i><p>Click "Test" buttons above to run enhanced System Notification Rules tests</p></div>';
    },
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    SystemNotificationRulesTester.init();
});
