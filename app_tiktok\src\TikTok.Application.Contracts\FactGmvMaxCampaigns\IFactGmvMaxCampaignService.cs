using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.FactGmvMaxCampaigns.Dtos;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.FactGmvMaxCampaigns
{
    public interface IFactGmvMaxCampaignService
    {
        // Existing methods
        Task<GetFactGmvMaxCampaignDataResponse> GetListAsync(DateTime fromDate, DateTime toDate, string? currency = "USD", bool hasViewAllAdvertisers = false);
        Task<GetFactGmvMaxCampaignDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        
        // ✅ NEW: Enhanced method with additional filters for better performance
        Task<GetFactGmvMaxCampaignDataResponse> GetListWithFiltersAsync(
            DateTime fromDate, 
            DateTime toDate, 
            string? currency = "USD",
            List<string>? campaignIds = null,
            List<string>? shopIds = null,
            string? searchText = null,
            string? shoppingAdsType = null,
            string? operationStatus = null);
        Task<GmvMaxCampaignDashboardDto> GetDashboardAsync(string? currency = "USD");
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD");
        Task<object> GetDetailedAnalysisDataAsync();

        // ✅ NEW: Section-specific methods for independent loading
        /// <summary>
        /// Lấy dữ liệu cho Summary Cards section - Critical priority
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <param name="shoppingAdsType">Loại quảng cáo shopping (PRODUCT/LIVE)</param>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="searchText">Tìm kiếm theo tên</param>
        /// <param name="shopIds">Danh sách shop IDs</param>
        /// <returns>Summary cards data</returns>
        Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD",
            string? shoppingAdsType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<string>? shopIds = null,
            List<string>? campaignIds = null);

        /// <summary>
        /// Lấy dữ liệu cho Overview Section - High priority
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Overview section data</returns>
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD");

        /// <summary>
        /// Lấy dữ liệu cho Charts Section - Medium priority (lazy load)
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Charts data</returns>
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD");

        /// <summary>
        /// Lấy dữ liệu cho Detailed Charts Section - Medium priority (lazy load)
        /// </summary>
        /// <returns>Detailed charts data</returns>
        Task<DetailedChartsDto> GetDetailedChartsAsync();

        /// <summary>
        /// Lấy dữ liệu cho Rankings Section - Low priority (background load)
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Rankings data</returns>
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD");

        /// <summary>
        /// Lấy snapshot mới nhất cho mỗi campaign trong khoảng thời gian (đã áp quyền)
        /// </summary>
        Task<List<FactGmvMaxCampaignDto>> GetLatestPerCampaignWithPermissionsAsync(
            DateTime fromDate,
            DateTime toDate,
            string? currency = "USD");

    }
}
