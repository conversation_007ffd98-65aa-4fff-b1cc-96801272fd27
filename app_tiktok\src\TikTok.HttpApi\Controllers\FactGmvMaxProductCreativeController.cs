using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.FactGmvMaxProductCreatives;
using TikTok.FactGmvMaxProductCreatives.Dtos;
using TikTok.Facts.FactGmvMaxProductCreative;
using TikTok.Permissions;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller cho FactGmvMaxProductCreative - tương tự FactGmvMaxProductController nhưng cho Creative level
    /// </summary>
    [Route("api/fact-gmv-max-product-creative")]
    public class FactGmvMaxProductCreativeController : AbpControllerBase
    {
        private readonly IFactGmvMaxProductCreativeService _factGmvMaxProductCreativeService;

        public FactGmvMaxProductCreativeController(IFactGmvMaxProductCreativeService factGmvMaxProductCreativeService)
        {
            _factGmvMaxProductCreativeService = factGmvMaxProductCreativeService;
        }

        // Helper: pick currency value from DTO (USD/VND)
        private static decimal? SelectByCurrency(decimal? usd, decimal? vnd, string? currency)
        {
            if (string.Equals(currency, "VND", StringComparison.OrdinalIgnoreCase))
            {
                return vnd ?? usd;
            }
            return usd ?? vnd;
        }

        /// <summary>
        /// Lấy dữ liệu GMV Max ProductCreative cho pivot table (với phân quyền)
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="creativeType">Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)</param>
        /// <param name="itemGroupId">ID Product Group cụ thể</param>
        /// <param name="itemId">ID Creative cụ thể</param>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <param name="campaignIds">Danh sách Campaign IDs để filter</param>
        /// <param name="shopIds">Danh sách Shop IDs để filter</param>
        /// <param name="searchText">Tìm kiếm theo title, product name, store, campaign, advertiser</param>
        /// <returns>Dữ liệu GMV Max ProductCreative đã được filter theo quyền</returns>
        [HttpGet("data")]
        public async Task<ActionResult<GetFactGmvMaxProductCreativeDataResponse>> GetGmvMaxProductCreativeData(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? creativeType = null,
            [FromQuery] string? itemGroupId = null,
            [FromQuery] string? itemId = null,
            [FromQuery] string? currency = "USD",
            // ✅ NEW FILTERS - Backward compatible (all optional)
            [FromQuery] List<string>? campaignIds = null,
            [FromQuery] List<string>? shopIds = null,
            [FromQuery] string? searchText = null)
        {
            try
            {
                // ✅ Check permissions before proceeding - sử dụng quyền FactGmvMax hiện có
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập dữ liệu GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ TỐI ƯU: Get data with filters at database level for better performance
                var result = await _factGmvMaxProductCreativeService.GetListWithFiltersAsync(
                    from, to, currency,
                    campaignIds, shopIds, searchText, creativeType, itemGroupId, itemId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy thống kê tổng hợp GMV Max ProductCreative
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Thống kê tổng hợp</returns>
        [HttpGet("summary")]
        public async Task<IActionResult> GetGmvMaxProductCreativeSummary(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] List<string>? campaignIds = null,
            [FromQuery] List<string>? shopIds = null,
            [FromQuery] string? searchText = null,
            [FromQuery] string? creativeType = null,
            [FromQuery] string? itemGroupId = null,
            [FromQuery] string? itemId = null)
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem thống kê GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ TỐI ƯU: Get summary data with filters at database level
                var result = await _factGmvMaxProductCreativeService.GetListWithFiltersAsync(
                    from, to, "USD", campaignIds, shopIds, searchText, creativeType, itemGroupId, itemId);
                
                // Calculate summary from filtered data
                var creatives = result.FactGmvMaxProductCreatives;
                var summary = new FactGmvMaxProductCreativeSummaryDto
                {
                    FromDate = from,
                    ToDate = to,
                    TotalRecords = creatives.Count,
                    TotalCostUSD = creatives.Sum(f => f.Cost),
                    TotalGrossRevenueUSD = creatives.Sum(f => f.GrossRevenueUSD ?? 0),
                    TotalOrders = creatives.Sum(f => f.Orders),
                    AverageROAS = creatives.Where(f => f.ROAS.HasValue).Any() ? creatives.Where(f => f.ROAS.HasValue).Average(f => f.ROAS ?? 0) : 0,
                    AverageTACOS = creatives.Where(f => f.TACOS.HasValue).Any() ? creatives.Where(f => f.TACOS.HasValue).Average(f => f.TACOS ?? 0) : 0,
                    UniqueCreatives = creatives.Select(f => f.ItemId).Distinct().Count(),
                    UniqueCampaigns = creatives.Select(f => f.CampaignId).Distinct().Count(),
                    UniqueStores = creatives.Select(f => f.StoreId).Distinct().Count(),
                    UniqueAdAccounts = creatives.Select(f => f.AdvertiserId).Distinct().Count(),
                    UniqueTikTokAccounts = creatives.Where(f => !string.IsNullOrEmpty(f.TtAccountName)).Select(f => f.TtAccountName).Distinct().Count(),
                    UniqueProductGroups = creatives.Select(f => f.ItemGroupId).Distinct().Count(),
                    UniqueDates = creatives.Select(f => f.Date.Date).Distinct().Count(),
                    TotalImpressions = creatives.Sum(f => f.ProductImpressions ?? 0),
                    TotalClicks = creatives.Sum(f => f.ProductClicks ?? 0),
                    AvgCostPerOrderUSD = creatives.Where(f => f.CostPerOrderUSD.HasValue).Any() ? creatives.Where(f => f.CostPerOrderUSD.HasValue).Average(f => f.CostPerOrderUSD ?? 0) : 0,
                    AverageCTR = creatives.Where(f => f.ProductClickRate.HasValue).Any() ? creatives.Where(f => f.ProductClickRate.HasValue).Average(f => f.ProductClickRate ?? 0) : 0,
                    AverageConversionRate = creatives.Where(f => f.AdConversionRate.HasValue).Any() ? creatives.Where(f => f.AdConversionRate.HasValue).Average(f => f.AdConversionRate ?? 0) : 0,
                    AverageVideoViewRate2s = creatives.Where(f => f.AdVideoViewRate2s.HasValue).Any() ? creatives.Where(f => f.AdVideoViewRate2s.HasValue).Average(f => f.AdVideoViewRate2s ?? 0) : 0,
                    AverageVideoViewRate6s = creatives.Where(f => f.AdVideoViewRate6s.HasValue).Any() ? creatives.Where(f => f.AdVideoViewRate6s.HasValue).Average(f => f.AdVideoViewRate6s ?? 0) : 0,
                    AverageProductClickRate = creatives.Where(f => f.ProductClickRate.HasValue).Any() ? creatives.Where(f => f.ProductClickRate.HasValue).Average(f => f.ProductClickRate ?? 0) : 0
                };
                return Ok(summary);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ API cho Summary Cards - Dữ liệu tổng hợp cho summary cards
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <param name="creativeType">Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)</param>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="searchText">Tìm kiếm theo tên</param>
        /// <param name="shopIds">Danh sách shop IDs</param>
        /// <param name="campaignIds">Danh sách Campaign IDs để filter</param>
        /// <param name="itemGroupId">ID Product Group cụ thể</param>
        /// <param name="itemId">ID Creative cụ thể</param>
        /// <returns>Summary cards data</returns>
        [HttpGet("summary-cards")]
        public async Task<IActionResult> GetSummaryCards(
            [FromQuery] string? currency = "USD",
            [FromQuery] string? creativeType = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? searchText = null,
            [FromQuery] List<string>? shopIds = null,
            [FromQuery] List<string>? campaignIds = null,
            [FromQuery] string? itemGroupId = null,
            [FromQuery] string? itemId = null)
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập summary cards GMV Max ProductCreative");
                }

                var summaryData = await _factGmvMaxProductCreativeService.GetSummaryCardsAsync(
                    currency, creativeType, fromDate, toDate, searchText, shopIds, campaignIds, itemGroupId, itemId);
                return Ok(summaryData);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách creative có hiệu suất thấp
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="roasThreshold">Ngưỡng ROAS thấp (mặc định: 1.5)</param>
        /// <param name="tacosThreshold">Ngưỡng TACOS cao (mặc định: 30%)</param>
        /// <returns>Danh sách creative có hiệu suất thấp</returns>
        [HttpGet("low-performance")]
        public async Task<IActionResult> GetLowPerformanceCreatives(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] decimal roasThreshold = 1.5m,
            [FromQuery] decimal tacosThreshold = 30.0m,
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem chỉ số hiệu suất GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductCreativeService.GetLowPerformanceCreativesAsync(from, to, roasThreshold, tacosThreshold, currency);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách creative có chi phí cao
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="costThreshold">Ngưỡng chi phí cao (USD)</param>
        /// <returns>Danh sách creative có chi phí cao</returns>
        [HttpGet("high-cost")]
        public async Task<IActionResult> GetHighCostCreatives(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] decimal costThreshold = 1000m,
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions before proceeding
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem dữ liệu chi phí GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductCreativeService.GetListWithPermissionsAsync(from, to, currency);
                
                // Filter high cost creatives
                var highCostCreatives = result.FactGmvMaxProductCreatives
                    .Where(fpc => SelectByCurrency(fpc.CostPerOrderUSD, fpc.CostPerOrderVND, currency) >= costThreshold)
                    .OrderByDescending(fpc => SelectByCurrency(fpc.CostPerOrderUSD, fpc.CostPerOrderVND, currency))
                    .ToList();

                return Ok(highCostCreatives);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy cảnh báo cho creative
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách cảnh báo</returns>
        [HttpGet("alerts")]
        public async Task<IActionResult> GetCreativeAlerts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem cảnh báo GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductCreativeService.GetCreativeAlertsAsync(from, to, currency);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy xu hướng creative theo thời gian
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Xu hướng creative</returns>
        [HttpGet("trends")]
        public async Task<IActionResult> GetCreativeTrends(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem xu hướng GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductCreativeService.GetTrendsAsync(from, to);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách creative bán chạy nhất
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="limit">Số lượng tối đa (mặc định: 10)</param>
        /// <returns>Danh sách creative bán chạy</returns>
        [HttpGet("top-selling")]
        public async Task<IActionResult> GetTopSellingCreatives(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int limit = 10)
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem danh sách creative bán chạy GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductCreativeService.GetTopSellingAsync(from, to, limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy dữ liệu dashboard cho ProductCreative
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Dữ liệu dashboard</returns>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetCreativeDashboard(
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem dashboard GMV Max ProductCreative");
                }

                // ✅ Get REAL data from database service
                var result = await _factGmvMaxProductCreativeService.GetDashboardAsync(currency);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Lấy dữ liệu ranking cho ProductCreative
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Dữ liệu ranking</returns>
        [HttpGet("rankings")]
        public async Task<IActionResult> GetCreativeRankings(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem ranking GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var creativeRankings = await _factGmvMaxProductCreativeService.GetCreativeRankingAsync(from, to, currency);
                var accountRankings = await _factGmvMaxProductCreativeService.GetAccountRankingAsync(from, to, currency);

                var result = new
                {
                    CreativeRankings = creativeRankings,
                    AccountRankings = accountRankings
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// ✅ NEW: Lấy phân tích hiệu suất creative
        /// </summary>
        [HttpGet("performance-analysis")]
        public async Task<IActionResult> GetCreativePerformanceAnalysis(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? currency = "USD")
        {
            try
            {
                // ✅ Check permissions
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền xem phân tích hiệu suất GMV Max ProductCreative");
                }

                // Set default date range if not provided
                var from = fromDate ?? DateTime.Now.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                // ✅ Get REAL data from database service
                var performance = await _factGmvMaxProductCreativeService.GetCreativePerformanceAsync(from, to, currency);
                var deliveryAnalysis = await _factGmvMaxProductCreativeService.GetCreativeDeliveryAnalysisAsync(from, to, currency);
                var contentTypeAnalysis = await _factGmvMaxProductCreativeService.GetContentTypeAnalysisAsync(from, to, currency);

                var result = new
                {
                    Performance = performance,
                    DeliveryAnalysis = deliveryAnalysis,
                    ContentTypeAnalysis = contentTypeAnalysis
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
