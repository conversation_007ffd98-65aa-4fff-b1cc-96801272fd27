using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Application.Contracts.SystemNotification;
using TikTok.Domain.Entities.SystemNotification;
using TikTok.Domain.Repositories;
using TikTok.Permissions;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Application.SystemNotification
{
    [Authorize(TikTokPermissions.SystemNotificationRules.ManageRule)]
    public class SystemNotificationRuleConsumerAppService : ApplicationService, ISystemNotificationRuleConsumerAppService
    {
        private readonly ISystemNotificationRuleConsumerRepository _consumerRepository;
        private readonly IRepository<SystemNotificationRule, Guid> _ruleRepository;

        public SystemNotificationRuleConsumerAppService(
            ISystemNotificationRuleConsumerRepository consumerRepository,
            IRepository<SystemNotificationRule, Guid> ruleRepository)
        {
            _consumerRepository = consumerRepository;
            _ruleRepository = ruleRepository;
        }

        /// <summary>
        /// Lấy danh sách consumers của một rule
        /// </summary>
        public async Task<SystemNotificationRuleConsumersResponse> GetRuleConsumersAsync(Guid ruleId)
        {
            // Kiểm tra rule có tồn tại không
            var rule = await _ruleRepository.GetAsync(ruleId);
            if (rule == null)
            {
                throw new ArgumentException($"SystemNotificationRule with id {ruleId} not found");
            }

            var consumers = await _consumerRepository.GetConsumersByRuleIdAsync(ruleId);
            
            var response = new SystemNotificationRuleConsumersResponse
            {
                RuleId = ruleId,
                AdAccounts = new List<SystemNotificationRuleConsumerAdAccountDto>(),
                BusinessCenters = new List<SystemNotificationRuleConsumerBusinessCenterDto>()
            };

            // Map consumers to response
            foreach (var consumer in consumers)
            {
                if (consumer.ConsumerType == "AdAccount" && consumer.AdAccountId.HasValue)
                {
                    response.AdAccounts.Add(new SystemNotificationRuleConsumerAdAccountDto
                    {
                        Id = consumer.Id,
                        AdvertiserId = consumer.ConsumerId ?? "",
                        AdAccountName = consumer.ConsumerName ?? "",
                        BcName = "", // TODO: Get BC name from BC service if needed
                        IsAssigned = true
                    });
                }
                else if (consumer.ConsumerType == "BusinessCenter" && consumer.BcId.HasValue)
                {
                    response.BusinessCenters.Add(new SystemNotificationRuleConsumerBusinessCenterDto
                    {
                        Id = consumer.Id,
                        BcId = consumer.ConsumerId ?? "",
                        BcName = consumer.ConsumerName ?? "",
                        IsAssigned = true
                    });
                }
            }

            return response;
        }

        /// <summary>
        /// Thêm consumers vào rule
        /// </summary>
        public async Task<List<SystemNotificationRuleConsumerDto>> AddConsumersToRuleAsync(AddSystemNotificationRuleConsumersDto input)
        {
            // Kiểm tra rule có tồn tại không
            var rule = await _ruleRepository.GetAsync(input.SystemNotificationRuleId);
            if (rule == null)
            {
                throw new ArgumentException($"SystemNotificationRule with id {input.SystemNotificationRuleId} not found");
            }

            var consumers = new List<SystemNotificationRuleConsumerEntity>();

            foreach (var adAccountId in input.AdAccountIds)
            {
                // Kiểm tra consumer đã được assign chưa
                var isAssigned = await _consumerRepository.IsConsumerAssignedAsync(input.SystemNotificationRuleId, null, adAccountId);
                if (isAssigned)
                {
                    continue; // Skip nếu đã được assign
                }

                var consumer = new SystemNotificationRuleConsumerEntity
                {
                    SystemNotificationRuleId = input.SystemNotificationRuleId,
                    AdAccountId = adAccountId,
                    ConsumerType = "AdAccount",
                    ConsumerId = adAccountId.ToString()
                };

                consumers.Add(consumer);
            }

            await _consumerRepository.InsertManyAsync(consumers);

            return consumers.Select(c => new SystemNotificationRuleConsumerDto
            {
                Id = c.Id,
                SystemNotificationRuleId = c.SystemNotificationRuleId,
                AdAccountId = c.AdAccountId,
                ConsumerType = c.ConsumerType,
                ConsumerId = c.ConsumerId
            }).ToList();
        }

        /// <summary>
        /// Xóa consumers khỏi rule
        /// </summary>
        public async Task RemoveConsumersFromRuleAsync(Guid ruleId, List<Guid> consumerIds)
        {
            var consumers = await _consumerRepository.GetListAsync(x => 
                x.SystemNotificationRuleId == ruleId && 
                consumerIds.Contains(x.Id));

            await _consumerRepository.DeleteManyAsync(consumers);
        }
    }
}
