/**
 * Modal Management Component for System Notification Rules
 * Handles modal operations, CRUD operations, and form management
 */

const ModalManager = {
    // Global variables for entity change warning
    pendingEntityChange: null,
    pendingSelectElement: null,
    
    // Global variables for delete confirmation
    pendingDeleteRuleId: null,

    // Initialize modal manager
    init() {
        this.setupEventListeners();
    },

    // Open create modal
    openCreateModal() {
        // Clear the rule ID to indicate this is a create operation
        document.getElementById('editRuleId').value = '';
        document.getElementById('editRuleName').value = '';
        document.getElementById('editEntityType').value = '';
        document.getElementById('editIsDefault').checked = false;
        document.getElementById('editIsPublic').checked = false;
        
        // Clear any validation errors
        clearAllValidationErrors();
        
        // Update modal title for create
        document.getElementById('SystemNotificationRuleModalLabel').innerHTML = getLocalizedText('createRule');
        
        $('#SystemNotificationRuleModal').modal('show');
        
        // Load entity types for create - hard-coded as requested
        const select = document.getElementById('editEntityType');
        
        // Use centralized EntityTypesManager for entity types
        if (window.EntityTypesManager) {
            select.innerHTML = window.EntityTypesManager.generateDropdownOptions({
                includeAll: false,
                activeOnly: true,
                selectText: 'select'
            });
        } else {
            // Fallback to hardcoded options for backward compatibility
            select.innerHTML =
                '<option value="">-- ' +
                getLocalizedText('select') +
                ' --</option>' +
                '<option value="RawGmvMaxProductCreativeReportEntity">' +
                getLocalizedText('gmvMaxProductCreative') +
                '</option>' +
                '<option value="RawGmvMaxProductCampaignReportEntity">' +
                getLocalizedText('D') +
                '</option>' +
                '<option value="RawGmvMaxLiveCampaignReportEntity">' +
                getLocalizedText('gmvMaxLiveCampaign') +
                '</option>';
        }
        
        window.QueryBuilderManager?.initCreateQueryBuilder();
    },

    // Open edit modal
    async openEditModal(id) {
        try {
            const data = await SystemNotificationApiService.getRuleDetail(id);
            
            // Clear any validation errors
            clearAllValidationErrors();
            
            // Update modal title for edit
            document.getElementById('SystemNotificationRuleModalLabel').innerHTML = getLocalizedText('editRule');
            
            $('#SystemNotificationRuleModal').modal('show');
            document.getElementById('editRuleId').value = data.id;
            document.getElementById('editRuleName').value = data.ruleName || '';
            
            // entity types - hard-coded as requested
            const select = document.getElementById('editEntityType');
            select.innerHTML =
                '<option value="">-- ' +
                getLocalizedText('select') +
                ' --</option>' +
                '<option value="RawGmvMaxProductCreativeReportEntity">' +
                getLocalizedText('gmvMaxProductCreative') +
                '</option>' +
                '<option value="RawGmvMaxProductCampaignReportEntity">' +
                getLocalizedText('gmvMaxProductCampaign') +
                '</option>' +
                '<option value="RawGmvMaxLiveCampaignReportEntity">' +
                getLocalizedText('gmvMaxLiveCampaign') +
                '</option>';
            select.value = data.entityType;
            
            // Set other fields
            document.getElementById('editIsDefault').checked = data.isDefault || false;
            document.getElementById('editIsPublic').checked = data.isPublic || false;
            
            let qbRule = null;
            try {
                qbRule = data.conditionsJson ? JSON.parse(data.conditionsJson) : null;
            } catch {
                qbRule = null;
            }
            
            window.QueryBuilderManager?.initEditQueryBuilder(data.entityType, qbRule ? qbRule.rules || [] : []);
        } catch (error) {
            console.error('Error opening edit modal:', error);
            abp.notify.error('Lỗi khi tải thông tin quy tắc');
        }
    },

    // Copy rule
    async copyRule(id) {
        try {
            const data = await SystemNotificationApiService.getRuleDetail(id);
            
            // Clear the rule ID to indicate this is a create operation (but copy existing data)
            document.getElementById('editRuleId').value = '';
            
            // Clear any validation errors
            clearAllValidationErrors();
            
            // Update modal title for copy (which is creating a new rule)
            document.getElementById('SystemNotificationRuleModalLabel').innerHTML = getLocalizedText('createRule');
            
            // Open create modal and prefill values from existing rule
            $('#SystemNotificationRuleModal').modal('show');
            document.getElementById('editRuleName').value = `${data.ruleName || ''} ${getLocalizedText('copy')}`;
            
            // entity types - hard-coded as requested
            const select = document.getElementById('editEntityType');
            select.innerHTML =
                '<option value="">-- ' +
                getLocalizedText('select') +
                ' --</option>' +
                '<option value="RawGmvMaxProductCreativeReportEntity">' +
                getLocalizedText('gmvMaxProductCreative') +
                '</option>' +
                '<option value="RawGmvMaxProductCampaignReportEntity">' +
                getLocalizedText('gmvMaxProductCampaign') +
                '</option>' +
                '<option value="RawGmvMaxLiveCampaignReportEntity">' +
                getLocalizedText('gmvMaxLiveCampaign') +
                '</option>';
            select.value = data.entityType;
            
            // Set other fields
            document.getElementById('editIsDefault').checked = data.isDefault || false;
            document.getElementById('editIsPublic').checked = data.isPublic || false;
            
            // initialize QB with existing conditions
            const qbEl = document.getElementById('editQuerybuilder');
            qbEl.innerHTML = '';
            const fields = await SystemNotificationApiService.getFields(data.entityType);
            let qbRule = null;
            try {
                qbRule = data.conditionsJson ? JSON.parse(data.conditionsJson) : null;
            } catch {
                qbRule = null;
            }
            
            window.QueryBuilderManager?.renderQueryBuilder(qbEl, fields, qbRule ? qbRule.rules || [] : []);
            
            // ensure subsequent entity changes reload fields in the Copy/Create modal
            select.onchange = async function () {
                const entity = this.value;
                if (!entity) {
                    qbEl.innerHTML = '';
                    return;
                }

            // Kiểm tra nếu có conditions đã tạo (chỉ hiện cảnh báo khi có điều kiện thực sự)
            const qb = qbEl.__qb;
            if (qb && qb.rule && qb.rule.rules && qb.rule.rules.length > 0) {
                console.log('🔍 Checking conditions:', qb.rule.rules);
                
                // Kiểm tra xem có điều kiện thực sự hay chỉ là rule mặc định
                const hasRealConditions = qb.rule.rules.some(rule => {
                    console.log('🔍 Checking rule:', rule);
                    
                    // Bỏ qua rule mặc định (chỉ có field và operator, không có value thực sự)
                    // Kiểm tra value có ý nghĩa thực sự
                    if (rule.value === '' || rule.value === null || rule.value === undefined) {
                        console.log('❌ Rule has empty value, skipping');
                        return false;
                    }
                    
                    // Kiểm tra nếu là array (cho operator 'between', 'in', 'notin')
                    if (Array.isArray(rule.value)) {
                        const hasValidArrayValue = rule.value.some(v => v !== '' && v !== null && v !== undefined);
                        console.log('🔍 Array value check:', rule.value, 'hasValid:', hasValidArrayValue);
                        return hasValidArrayValue;
                    }
                    
                    // Kiểm tra string có nội dung thực sự
                    if (typeof rule.value === 'string') {
                        const hasValidStringValue = rule.value.trim() !== '';
                        console.log('🔍 String value check:', rule.value, 'hasValid:', hasValidStringValue);
                        return hasValidStringValue;
                    }
                    
                    // Các kiểu khác (number, boolean) đều coi là có giá trị
                    console.log('✅ Rule has valid value:', rule.value);
                    return true;
                });
                
                console.log('🔍 Has real conditions:', hasRealConditions);
                
                if (hasRealConditions) {
                    console.log('⚠️ Showing warning modal');
                    // Lưu thông tin để xử lý sau khi user xác nhận
                    ModalManager.pendingEntityChange = entity;
                    ModalManager.pendingSelectElement = this;

                    // Hiển thị modal cảnh báo
                    $('#entityChangeWarningModal').modal('show');
                    return;
                } else {
                    console.log('✅ No real conditions, proceeding with entity change');
                }
            }

                // Nếu không có conditions, tiếp tục đổi entity
                await window.QueryBuilderManager?.changeEntityType(this, entity, qbEl);
            };
        } catch (error) {
            console.error('Error copying rule:', error);
            abp.notify.error('Lỗi khi sao chép quy tắc');
        }
    },

    // Delete rule - show confirmation modal
    deleteRule(id) {
        // Store the rule ID for later use
        this.pendingDeleteRuleId = id;
        
        // Show the confirmation modal
        $('#deleteRuleConfirmModal').modal('show');
    },

    // Actually delete the rule after confirmation
    async confirmDeleteRule() {
        if (!this.pendingDeleteRuleId) return;
        
        try {
            await SystemNotificationApiService.deleteRule(this.pendingDeleteRuleId);
            abp.notify.success('Đã xóa quy tắc thành công');
            window.TableManager?.refresh();
            
            // Reset pending ID
            this.pendingDeleteRuleId = null;
            
            // Close modal
            $('#deleteRuleConfirmModal').modal('hide');
        } catch (error) {
            console.error('Error deleting rule:', error);
            abp.notify.error('Lỗi khi xóa quy tắc');
        }
    },

    // Save rule (create or update)
    async saveRule() {
        // Validate required fields using real-time validation
        const isRuleNameValid = validateRuleName();
        const isEntityTypeValid = validateEntityType();
        
        if (!isRuleNameValid || !isEntityTypeValid) {
            // Focus on first invalid field
            if (!isRuleNameValid) {
                document.getElementById('editRuleName').focus();
            } else if (!isEntityTypeValid) {
                document.getElementById('editEntityType').focus();
            }
            return;
        }
        
        // FE validation for query builder
        const validationError = validateRulesBeforeSubmit('editQuerybuilder');
        if (validationError) {
            abp.notify.error(validationError);
            return;
        }
        
        const ruleName = document.getElementById('editRuleName').value?.trim();
        const entityType = document.getElementById('editEntityType').value;
        const id = document.getElementById('editRuleId').value;
        
        const body = {
            ruleName: ruleName,
            entityType: entityType,
            isDefault: document.getElementById('editIsDefault').checked,
            isPublic: document.getElementById('editIsPublic').checked,
            conditionsJson: window.QueryBuilderManager?.collectConditionsJson('editQuerybuilder'),
        };

        try {
            // Check if this is create or update operation
            if (id && id.trim() !== '') {
                // Update existing rule
                await SystemNotificationApiService.updateRule(id, body);
                abp.notify.success('Đã cập nhật quy tắc thành công');
            } else {
                // Create new rule
                await SystemNotificationApiService.createRule(body);
                abp.notify.success('Đã tạo quy tắc thành công');
            }
            
            $('#SystemNotificationRuleModal').modal('hide');
            window.TableManager?.refresh();
        } catch (error) {
            console.error('Error saving rule:', error);
            abp.notify.error('Lỗi khi lưu quy tắc');
        }
    },

    // Handle entity type change
    async changeEntityType(selectElement, entity, qbEl) {
        // Lưu giá trị hiện tại để có thể khôi phục
        selectElement.dataset.previousValue = selectElement.value;

        const fields = await SystemNotificationApiService.getFields(entity);
        window.QueryBuilderManager?.renderQueryBuilder(qbEl, fields, []);
    },

    // Setup event listeners
    setupEventListeners() {
        // Save rule button
        const saveRuleButton = document.getElementById('saveRuleButton');
        if (saveRuleButton) {
            saveRuleButton.addEventListener('click', () => this.saveRule());
        }

        // New rule button
        const newRuleButton = document.getElementById('NewRuleButton');
        if (newRuleButton) {
            newRuleButton.addEventListener('click', () => this.openCreateModal());
        }

        // Event handlers for entity change warning modal
        const entityChangeCancelBtn = document.getElementById('entityChangeCancelBtn');
        if (entityChangeCancelBtn) {
            entityChangeCancelBtn.addEventListener('click', () => {
                // Khôi phục giá trị cũ
                if (this.pendingSelectElement && this.pendingSelectElement.dataset.previousValue) {
                    this.pendingSelectElement.value = this.pendingSelectElement.dataset.previousValue;
                }

                // Reset pending values
                this.pendingEntityChange = null;
                this.pendingSelectElement = null;

                // Đóng modal
                $('#entityChangeWarningModal').modal('hide');
            });
        }

        const entityChangeConfirmBtn = document.getElementById('entityChangeConfirmBtn');
        if (entityChangeConfirmBtn) {
            entityChangeConfirmBtn.addEventListener('click', async () => {
                if (this.pendingSelectElement && this.pendingEntityChange) {
                    // Tìm QueryBuilder element
                    const qbEl = document.getElementById('editQuerybuilder');
                    if (qbEl) {
                        // Thực hiện đổi entity
                        await this.changeEntityType(this.pendingSelectElement, this.pendingEntityChange, qbEl);
                    }
                }

                // Reset pending values
                this.pendingEntityChange = null;
                this.pendingSelectElement = null;

                // Đóng modal
                $('#entityChangeWarningModal').modal('hide');
            });
        }

        // Reset pending values when modal is hidden
        $('#entityChangeWarningModal').on('hidden.bs.modal', () => {
            this.pendingEntityChange = null;
            this.pendingSelectElement = null;
        });

        // Event handlers for delete confirmation modal
        const deleteRuleCancelBtn = document.getElementById('deleteRuleCancelBtn');
        if (deleteRuleCancelBtn) {
            deleteRuleCancelBtn.addEventListener('click', () => {
                // Reset pending ID
                this.pendingDeleteRuleId = null;
                
                // Close modal
                $('#deleteRuleConfirmModal').modal('hide');
            });
        }

        const deleteRuleConfirmBtn = document.getElementById('deleteRuleConfirmBtn');
        if (deleteRuleConfirmBtn) {
            deleteRuleConfirmBtn.addEventListener('click', () => {
                this.confirmDeleteRule();
            });
        }

        // Reset pending values when delete modal is hidden
        $('#deleteRuleConfirmModal').on('hidden.bs.modal', () => {
            this.pendingDeleteRuleId = null;
        });
    }
};

// Export to global scope
window.ModalManager = ModalManager;
