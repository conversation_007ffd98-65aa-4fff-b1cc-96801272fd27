using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TikTok.Application.Contracts.DataSync.TikTokVideoSync
{
    /// <summary>
    /// DTO cho request lấy thông tin nhiều video TikTok
    /// </summary>
    public class GetVideoInfoListRequestDto
    {
        /// <summary>
        /// Danh sách Video ID cần lấy thông tin (tối đa 500 bản ghi)
        /// </summary>
        [Required]
        [MaxLength(500, ErrorMessage = "Danh sách video ID không được vượt quá 500 bản ghi")]
        public IList<string> VideoIds { get; set; } = new List<string>();
    }
}
