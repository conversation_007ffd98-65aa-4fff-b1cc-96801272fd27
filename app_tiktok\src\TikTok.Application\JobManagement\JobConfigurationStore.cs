using System;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.Extensions.Logging;
using TikTok.BackgroundJobs.Workers;
using TikTok.Entities;
using TikTok.Repositories;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Guids;
using System.Linq;
using TikTok.Enums;
using static TikTok.BackgroundJobs.Workers.ManagerJob;
using static TikTok.BackgroundJobs.Workers.RegisterJob;
using TikTok.BackgroundJobs;
using System.Collections.Generic;
using TikTok.BusinessApplications;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service để quản lý cấu hình và khởi động recurring jobs
    /// </summary>
    public class JobConfigurationStore : IJobConfigurationStore
    {
        private readonly ILogger<JobConfigurationStore> _logger;
        private readonly IJobConfigurationRepository _jobConfigurationRepository;
        private readonly IWorkerInfoRepository _workerInfoRepository;
        private readonly IGuidGenerator _guidGenerator;
        private readonly IRecurringJobManager _recurringJobManager;
        private readonly IJobConfigurationCacheService _configurationCacheService;
        private readonly IJobTypeConfigurationCache _jobTypeConfigurationCache;
        private readonly IJobTypeConfigurationRepository _jobTypeConfigurationRepository;

        public JobConfigurationStore(
            ILogger<JobConfigurationStore> logger,
            IJobConfigurationRepository jobConfigurationRepository,
            IWorkerInfoRepository workerInfoRepository,
            IGuidGenerator guidGenerator,
            IRecurringJobManager recurringJobManager,
            IJobConfigurationCacheService configurationCacheService,
            IJobTypeConfigurationCache jobTypeConfigurationCache,
            IJobTypeConfigurationRepository jobTypeConfigurationRepository)
        {
            _logger = logger;
            _jobConfigurationRepository = jobConfigurationRepository;
            _workerInfoRepository = workerInfoRepository;
            _guidGenerator = guidGenerator;
            _recurringJobManager = recurringJobManager;
            _configurationCacheService = configurationCacheService;
            _jobTypeConfigurationCache = jobTypeConfigurationCache;
            _jobTypeConfigurationRepository = jobTypeConfigurationRepository;
        }

        /// <summary>
        /// Khởi động hệ thống job
        /// </summary>
        public async Task StartJobSystemAsync()
        {
            _logger.LogDebug("Starting job system...");

            try
            {
                // Lấy cấu hình hiện tại từ cache hoặc database
                var configuration = await _configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                if (configuration == null)
                {
                    _logger.LogDebug("No configuration found, creating default configuration");
                    await CreateDefaultConfigurationAsync();
                    configuration = await _configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                }

                // Khởi tạo workers nếu chưa có
                await InitializeWorkersAsync(configuration.MaxWorkers);

                // Khởi tạo cấu hình mặc định cho các loại công việc
                await InitializeDefaultJobTypeConfigurationsAsync();

                // Khởi động recurring jobs nếu hệ thống đang active
                if (configuration.IsActive)
                {
                    await StartRecurringJobsAsync(configuration);
                }

                _logger.LogDebug("Job system started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start job system");
            }
        }

        /// <summary>
        /// Dừng hệ thống job
        /// </summary>
        public async Task StopJobSystemAsync()
        {
            _logger.LogDebug("Stopping job system...");

            try
            {
                // Dừng recurring jobs
                _recurringJobManager.RemoveIfExists(TikTokConsts.JobSystem.ManagerJobId);
                _recurringJobManager.RemoveIfExists(TikTokConsts.JobSystem.RegisterJobId);

                _logger.LogDebug("Job system stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to stop job system");
                throw;
            }
        }

        /// <summary>
        /// Cấu hình lại hệ thống job dựa trên ID cấu hình
        /// </summary>
        /// <param name="id">ID của cấu hình</param>
        public async Task ReConfigurationAsync(Guid id)
        {
            _logger.LogDebug("Reconfiguring job system with configuration ID: {Id}", id);

            try
            {
                // Lấy cấu hình từ cache hoặc database
                var configuration = await _configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                if (configuration == null)
                {
                    throw new InvalidOperationException($"Configuration with ID {id} not found");
                }

                // Dừng recurring jobs hiện tại
                _recurringJobManager.RemoveIfExists(TikTokConsts.JobSystem.ManagerJobId);
                _recurringJobManager.RemoveIfExists(TikTokConsts.JobSystem.RegisterJobId);

                // Cập nhật số lượng workers nếu cần
                await UpdateWorkersAsync(configuration.MaxWorkers);

                // Khởi động lại recurring jobs nếu hệ thống đang active
                if (configuration.IsActive)
                {
                    await StartRecurringJobsAsync(configuration);
                }

                _logger.LogDebug("Job system reconfigured successfully with configuration ID: {Id}", id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reconfigure job system with configuration ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Tạo cấu hình mặc định
        /// </summary>
        private async Task CreateDefaultConfigurationAsync()
        {
            var configuration = new JobConfigurationEntity(_guidGenerator.Create())
            {
                Name = TikTokConsts.JobSystem.DefaultConfigurationName,
                Description = TikTokConsts.JobSystem.DefaultConfigurationDescription,
                IsActive = false,
                ManagerJobCron = TikTokConsts.JobSystem.DefaultManagerJobCron,
                RegisterJobCron = TikTokConsts.JobSystem.DefaultRegisterJobCron,
                WorkerTimeoutMinutes = TikTokConsts.JobSystem.DefaultWorkerTimeoutMinutes,
                MaxWorkers = TikTokConsts.JobSystem.DefaultMaxWorkers
            };

            await _jobConfigurationRepository.InsertAsync(configuration);

            // Clear cache sau khi tạo cấu hình mới
            await _configurationCacheService.ClearConfigurationCacheAsync();
        }

        /// <summary>
        /// Khởi tạo workers
        /// </summary>
        /// <param name="maxWorkers">Số lượng worker tối đa</param>
        private async Task InitializeWorkersAsync(int maxWorkers)
        {
            var existingWorkers = await _workerInfoRepository.GetListAsync();
            if (existingWorkers.Count >= maxWorkers)
            {
                return;
            }

            var workersToCreate = maxWorkers - existingWorkers.Count;
            var currentMax = existingWorkers.Count;
            for (int i = 0; i < workersToCreate; i++)
            {
                var worker = new WorkerInfoEntity(_guidGenerator.Create())
                {
                    WorkerId = $"{TikTokConsts.JobSystem.WorkerIdPrefix}{Guid.NewGuid():N}",
                    WorkerName = $"{TikTokConsts.JobSystem.WorkerNamePrefix}{currentMax + 1}",
                    Status = WorkerStatus.Idle
                };

                await _workerInfoRepository.InsertAsync(worker);
                currentMax++;
            }

            _logger.LogDebug("Initialized {Count} workers", workersToCreate);
        }

        /// <summary>
        /// Cập nhật số lượng workers
        /// </summary>
        /// <param name="maxWorkers">Số lượng worker tối đa mới</param>
        private async Task UpdateWorkersAsync(int maxWorkers)
        {
            var existingWorkers = await _workerInfoRepository.GetListAsync();

            if (existingWorkers.Count > maxWorkers)
            {
                // Xóa workers thừa (chỉ xóa những worker đang Idle)
                var idleWorkers = existingWorkers.Where(w => w.Status == WorkerStatus.Idle).ToList();
                var workersToRemove = idleWorkers.Take(existingWorkers.Count - maxWorkers).ToList();

                foreach (var worker in workersToRemove)
                {
                    await _workerInfoRepository.DeleteAsync(worker);
                }

                _logger.LogDebug("Removed {Count} workers", workersToRemove.Count);
            }
            else if (existingWorkers.Count < maxWorkers)
            {
                // Thêm workers mới
                var workersToAdd = maxWorkers - existingWorkers.Count;
                await InitializeWorkersAsync(maxWorkers);
            }
        }

        /// <summary>
        /// Khởi tạo cấu hình mặc định cho các loại công việc
        /// </summary>
        private async Task InitializeDefaultJobTypeConfigurationsAsync()
        {
            try
            {
                var allCommandTypes = Enum.GetValues<CommandType>();

                var commandTypeExists = await _jobTypeConfigurationRepository.GetListAsync();

                var listInsert = new List<JobTypeConfigurationEntity>();

                foreach (var commandType in allCommandTypes)
                {
                    if (commandTypeExists.Any(x => x.CommandType == commandType))
                    {
                        _logger.LogDebug("Configuration for CommandType {CommandType} already exists, skipping", commandType);
                        continue;
                    }

                    // Tạo cấu hình mặc định cho CommandType chưa tồn tại
                    var defaultConfig = CreateDefaultJobTypeConfiguration(commandType);
                    listInsert.Add(defaultConfig);

                    _logger.LogDebug("Created default configuration for CommandType {CommandType}", commandType);
                }
                if (listInsert.Count > 0)
                {
                    await _jobTypeConfigurationRepository.InsertManyAsync(listInsert);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize default job type configurations");
            }
        }

        /// <summary>
        /// Tạo cấu hình mặc định cho CommandType
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Cấu hình mặc định</returns>
        private JobTypeConfigurationEntity CreateDefaultJobTypeConfiguration(CommandType commandType)
        {
            var (displayName, description, intervalSeconds, priority) = GetDefaultJobTypeValues(commandType);
            // Todo CommandTypes Active
            var listCommandTypesActive = new List<CommandType>
            {
                CommandType.SyncBusinessCenter,
                CommandType.SyncTransaction,
                CommandType.SyncBalance,
                CommandType.SyncLatestBalance,
                CommandType.SyncAsset,
            };

            return new JobTypeConfigurationEntity(_guidGenerator.Create())
            {
                CommandType = commandType,
                DisplayName = displayName,
                Description = description,
                IntervalSeconds = intervalSeconds,
                IsActive = listCommandTypesActive.Contains(commandType),
                Priority = priority,
                TimeoutMinutes = null, // Sử dụng timeout chung
                MaxRetryCount = 3
            };
        }

        /// <summary>
        /// Lấy giá trị mặc định cho CommandType
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Tuple chứa (DisplayName, Description, IntervalSeconds, Priority)</returns>
        private (string displayName, string description, int intervalSeconds, int priority) GetDefaultJobTypeValues(CommandType commandType)
        {
            return commandType switch
            {
                CommandType.SyncBusinessCenter => ("Đồng bộ Business Center", "Đồng bộ thông tin Business Center", 30 * 60, 3), // 30 phút
                CommandType.SyncTransaction => ("Đồng bộ Transaction", "Đồng bộ giao dịch", 5 * 60, 2), // 5 phút
                CommandType.SyncBalance => ("Đồng bộ Balance", "Đồng bộ số dư", 12 * 60 * 60, 3), // 12 tiếng
                CommandType.SyncLatestBalance => ("Đồng bộ Balance mới nhất", "Đồng bộ số dư mới nhất", 5 * 60, 2), // 5 phút   
                CommandType.SyncAsset => ("Đồng bộ Asset", "Đồng bộ tài sản", 15 * 60, 3), // 15 phút
                CommandType.SyncCampaign => ("Đồng bộ Campaign", "Đồng bộ chiến dịch", 10 * 60, 5), // 10 phút
                CommandType.SyncReportIntegratedAdAccount => ("Đồng bộ Báo cáo AdAccount", "Đồng bộ báo cáo tích hợp AdAccount", 20 * 60, 6), // 20 phút
                CommandType.SyncReportIntegratedCampaign => ("Đồng bộ Báo cáo Campaign", "Đồng bộ báo cáo tích hợp Campaign", 25 * 60, 7), // 25 phút
                CommandType.SyncReportIntegratedAdGroup => ("Đồng bộ Báo cáo AdGroup", "Đồng bộ báo cáo tích hợp AdGroup", 30 * 60, 8), // 30 phút
                CommandType.SyncDetailedAdAccount => ("Đồng bộ chi tiết tài khoản quảng cáo", "Đồng bộ thông tin chi tiết tài khoản quảng cáo", 45 * 60, 4), // 45 phút
                CommandType.SyncGmvMax => ("Đồng bộ GMV Max", "Đồng bộ dữ liệu GMV Max", 60 * 60, 9), // 60 phút
                CommandType.SyncGmvMaxIdentities => ("Đồng bộ GMV Max Identities", "Đồng bộ dữ liệu GMV Max Identities", 12 * 60 * 60, 9), // 12 tiếng
                _ => ($"Đồng bộ {commandType}", $"Đồng bộ {commandType}", 60 * 60, 10) // 60 phút mặc định
            };
        }

        /// <summary>
        /// Khởi động recurring jobs
        /// </summary>
        /// <param name="configuration">Cấu hình</param>
        private async Task StartRecurringJobsAsync(JobConfigurationDto configuration)
        {
            // Khởi động Manager Job
            _recurringJobManager.AddOrUpdate<ManagerJob>(
                TikTokConsts.JobSystem.ManagerJobId,
                job => job.ExecuteAsync(new ManagerJobArgs()),
                configuration.ManagerJobCron);

            // Khởi động Register Job với cấu hình chung
            _recurringJobManager.AddOrUpdate<RegisterJob>(
                TikTokConsts.JobSystem.RegisterJobId,
                job => job.ExecuteAsync(new RegisterJobArgs()),
                configuration.RegisterJobCron);

            _logger.LogDebug("Started recurring jobs - Manager: {ManagerCron}, Register: {RegisterCron}",
                configuration.ManagerJobCron, configuration.RegisterJobCron);
        }
    }
}