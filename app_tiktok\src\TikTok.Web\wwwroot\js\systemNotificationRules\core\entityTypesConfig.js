/**
 * Entity Types Configuration for System Notification Rules
 * Centralized configuration for all entity types to ensure data integrity
 * 
 * This file serves as the single source of truth for entity types
 * All other files should reference this configuration instead of hard-coding values
 */

(function() {
    'use strict';

    /**
     * Entity Types Configuration
     * Maps entity type codes to their display information
     */
    const ENTITY_TYPES_CONFIG = {
        // Current supported entity types
        PRODUCT_CREATIVE: {
            code: 'RawGmvMaxProductCreativeReportEntity',
            displayName: 'gmvMaxProductCreative',
            description: 'GMV Max Product Creative',
            category: 'GMV_MAX',
            isActive: true
        },
        PRODUCT_CAMPAIGN: {
            code: 'RawGmvMaxProductCampaignReportEntity',
            displayName: 'gmvMaxProductCampaign',
            description: 'GMV Max Product Campaign',
            category: 'GMV_MAX',
            isActive: true
        },
        LIVE_CAMPAIGN: {
            code: 'RawGmvMaxLiveCampaignReportEntity',
            displayName: 'gmvMaxLiveCampaign',
            description: 'GMV Max Live Campaign',
            category: 'GMV_MAX',
            isActive: true
        },
        
        // Legacy entity types (for backward compatibility)
        LEGACY_CAMPAIGN: {
            code: 'FactGmvMaxCampaignEntity',
            displayName: 'gmvMaxCampaign',
            description: 'GMV Max Campaign (Legacy)',
            category: 'LEGACY',
            isActive: false
        },
        LEGACY_PRODUCT: {
            code: 'FactGmvMaxProductEntity',
            displayName: 'gmvMaxProduct',
            description: 'GMV Max Product (Legacy)',
            category: 'LEGACY',
            isActive: false
        }
    };

    /**
     * Entity Types Manager
     * Provides methods to access and manipulate entity types configuration
     */
    const EntityTypesManager = {
        /**
         * Get all entity types
         * @param {boolean} activeOnly - If true, returns only active entity types
         * @returns {Object} Entity types configuration
         */
        getAll(activeOnly = true) {
            if (activeOnly) {
                return Object.fromEntries(
                    Object.entries(ENTITY_TYPES_CONFIG).filter(([_, config]) => config.isActive)
                );
            }
            return ENTITY_TYPES_CONFIG;
        },

        /**
         * Get entity type by key
         * @param {string} key - Entity type key (e.g., 'PRODUCT_CREATIVE')
         * @returns {Object|null} Entity type configuration or null if not found
         */
        getByKey(key) {
            return ENTITY_TYPES_CONFIG[key] || null;
        },

        /**
         * Get entity type by code
         * @param {string} code - Entity type code (e.g., 'RawGmvMaxProductCreativeReportEntity')
         * @returns {Object|null} Entity type configuration or null if not found
         */
        getByCode(code) {
            return Object.values(ENTITY_TYPES_CONFIG).find(config => config.code === code) || null;
        },

        /**
         * Get all entity type codes
         * @param {boolean} activeOnly - If true, returns only active entity types
         * @returns {Array<string>} Array of entity type codes
         */
        getCodes(activeOnly = true) {
            const configs = this.getAll(activeOnly);
            return Object.values(configs).map(config => config.code);
        },

        /**
         * Get all entity type keys
         * @param {boolean} activeOnly - If true, returns only active entity types
         * @returns {Array<string>} Array of entity type keys
         */
        getKeys(activeOnly = true) {
            const configs = this.getAll(activeOnly);
            return Object.keys(configs);
        },

        /**
         * Get display name for entity type
         * @param {string} entityType - Entity type code or key
         * @returns {string} Display name or fallback to entity type
         */
        getDisplayName(entityType) {
            // Try to get by code first
            let config = this.getByCode(entityType);
            
            // If not found, try to get by key
            if (!config) {
                config = this.getByKey(entityType);
            }
            
            return config ? config.displayName : entityType;
        },

        /**
         * Get localized display name for entity type
         * @param {string} entityType - Entity type code or key
         * @returns {string} Localized display name
         */
        getLocalizedDisplayName(entityType) {
            const displayName = this.getDisplayName(entityType);
            
            // Use localization helper if available
            if (typeof getLocalizedText === 'function') {
                return getLocalizedText(displayName);
            }
            
            // Fallback to description if available
            const config = this.getByCode(entityType) || this.getByKey(entityType);
            return config ? config.description : entityType;
        },

        /**
         * Check if entity type is valid
         * @param {string} entityType - Entity type code or key
         * @returns {boolean} True if valid, false otherwise
         */
        isValid(entityType) {
            return this.getByCode(entityType) !== null || this.getByKey(entityType) !== null;
        },

        /**
         * Check if entity type is active
         * @param {string} entityType - Entity type code or key
         * @returns {boolean} True if active, false otherwise
         */
        isActive(entityType) {
            const config = this.getByCode(entityType) || this.getByKey(entityType);
            return config ? config.isActive : false;
        },

        /**
         * Generate dropdown options HTML
         * @param {Object} options - Configuration options
         * @param {boolean} options.includeAll - Include "All" option
         * @param {boolean} options.activeOnly - Include only active entity types
         * @param {string} options.allText - Text for "All" option
         * @param {string} options.selectText - Text for "Select" option
         * @returns {string} HTML string for dropdown options
         */
        generateDropdownOptions(options = {}) {
            const {
                includeAll = false,
                activeOnly = true,
                allText = 'all',
                selectText = 'select'
            } = options;

            let html = '';

            // Add "All" option if requested
            if (includeAll) {
                html += `<option value="">${getLocalizedText ? getLocalizedText(allText) : allText}</option>`;
            }

            // Add "Select" option if not including "All"
            if (!includeAll) {
                html += `<option value="">-- ${getLocalizedText ? getLocalizedText(selectText) : selectText} --</option>`;
            }

            // Add entity type options
            const configs = this.getAll(activeOnly);
            Object.values(configs).forEach(config => {
                const displayText = this.getLocalizedDisplayName(config.code);
                html += `<option value="${config.code}">${displayText}</option>`;
            });

            return html;
        },

        /**
         * Get entity types for constants (backward compatibility)
         * @returns {Object} Entity types object for constants.js
         */
        getConstantsObject() {
            const configs = this.getAll(true);
            const constants = {};
            
            Object.entries(configs).forEach(([key, config]) => {
                constants[key] = config.code;
            });
            
            return constants;
        }
    };

    // Export to global scope
    window.EntityTypesConfig = ENTITY_TYPES_CONFIG;
    window.EntityTypesManager = EntityTypesManager;

    // Backward compatibility exports
    window.SystemNotificationEntityTypes = EntityTypesManager.getConstantsObject();

    console.log('✅ EntityTypesConfig loaded successfully');
    console.log('Available entity types:', EntityTypesManager.getCodes());
})();
