using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// Core interface for building TikTok-specific notifications
    /// Simplified approach without GenericFactory dependency
    /// </summary>
    public interface ITikTokNotificationBuildProvider : ITransientDependency
    {
        string Context { get; }
        IEnumerable<TikTokUserType> AllowSendToUserTypes { get; }
        Task<string> GetTemplateTitle(TikTokUserType userType, TikTokEntityType? entityType = null);
        Task<IEnumerable<TikTokNotificationDto>> BuildNotifications(string objectId);
        Task<IEnumerable<TikTokNotificationDto>> BuildNotificationByUsers(string objectId, IEnumerable<TikTokNotificationUserDto> users);
    }
}


