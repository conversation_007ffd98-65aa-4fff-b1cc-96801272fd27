using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using TikTok.Application.Contracts.DataSync.TikTokVideoSync;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.DataSync.TikTokVideoSync
{
    /// <summary>
    /// Application Service để expose TikTok Video API sử dụng HttpClient để gọi TikTok share endpoint
    /// </summary>
    [Route("api/tiktok-video")]
    public class TikTokVideoApplicationService : AbpControllerBase
    {
        private readonly ITikTokVideoService _tikTokVideoService;

        public TikTokVideoApplicationService(ITikTokVideoService tikTokVideoService)
        {
            _tikTokVideoService = tikTokVideoService;
        }

        /// <summary>
        /// L<PERSON>y thông tin nhiều video TikTok từ danh sách video ID (tối đa 500 bản ghi)
        /// </summary>
        /// <param name="request">Request chứa danh sách video ID (tối đa 500)</param>
        /// <returns>Danh sách thông tin video TikTok</returns>
        [HttpPost("get-list")]
        public async Task<GetVideoInfoListResponseDto> GetVideoInfoListAsync([FromBody] GetVideoInfoListRequestDto request)
        {
            
            if (request.VideoIds.IsNullOrEmpty())
            {
                throw new ArgumentException("Danh sách video ID không được để trống");
            }
            
            if (request.VideoIds.Count > 500)
            {
                throw new ArgumentException("Danh sách video ID không được vượt quá 500 bản ghi");
            }
            
            return await _tikTokVideoService.GetVideoInfoListAsync(request.VideoIds);
        }


      
    }
}
