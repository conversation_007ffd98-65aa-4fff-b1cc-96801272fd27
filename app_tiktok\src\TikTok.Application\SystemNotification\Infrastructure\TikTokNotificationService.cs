using Microsoft.Extensions.Logging;
using Module.Notifications;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.SystemNotification;
using Volo.Abp.DependencyInjection;
using TikTok.Application.Contracts.SystemNotification.Realtime;

namespace TikTok.Application.SystemNotification
{
    public class TikTokNotificationService : ITikTokNotificationService, ITransientDependency
    {
        private readonly IEnumerable<ITikTokNotificationBuildProvider> _notificationBuilders;
        private readonly INotificationService _notificationService;
        private readonly ILogger<TikTokNotificationService> _logger;
        private readonly IRealtimeNotificationBroadcaster _realtimeBroadcaster;

        public TikTokNotificationService(
            IEnumerable<ITikTokNotificationBuildProvider> notificationBuilders,
            INotificationService notificationService,
            ILogger<TikTokNotificationService> logger,
            IRealtimeNotificationBroadcaster realtimeBroadcaster)
        {
            _notificationBuilders = notificationBuilders;
            _notificationService = notificationService;
            _logger = logger;
            _realtimeBroadcaster = realtimeBroadcaster;
        }

        public async Task<bool> SendCampaignNotificationAsync(
            string campaignId,
            string context,
            Dictionary<string, object> metadata = null)
        {
            try
            {
                _logger.LogInformation("Sending campaign notification: {CampaignId}, Context: {Context}",
                    campaignId, context);

                var builder = await GetNotificationBuilderAsync(context);
                if (builder == null)
                {
                    _logger.LogWarning("No notification builder found for context: {Context}", context);
                    return false;
                }

                var notifications = await builder.BuildNotifications(campaignId);
                if (!notifications.Any())
                {
                    _logger.LogInformation("No notifications to send for campaign: {CampaignId}", campaignId);
                    return false;
                }

                var results = new List<bool>();
                foreach (var notification in notifications)
                {
                    var result = await SendSingleNotification(notification);
                    results.Add(result);
                }

                var successCount = results.Count(r => r);
                _logger.LogInformation("Sent {SuccessCount}/{TotalCount} notifications for campaign: {CampaignId}",
                    successCount, results.Count, campaignId);

                return successCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending campaign notification: {CampaignId}", campaignId);
                return false;
            }
        }

        public async Task<bool> SendNotificationToUsersAsync(
            string objectId,
            string context,
            IEnumerable<TikTokNotificationUserDto> users)
        {
            try
            {
                var builder = await GetNotificationBuilderAsync(context);
                if (builder == null)
                {
                    return false;
                }

                var notifications = await builder.BuildNotificationByUsers(objectId, users);

                var results = new List<bool>();
                foreach (var notification in notifications)
                {
                    var result = await SendSingleNotification(notification);
                    results.Add(result);
                }

                return results.Any(r => r);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notifications to users");
                return false;
            }
        }

        public async Task<ITikTokNotificationBuildProvider> GetNotificationBuilderAsync(string context)
        {
            try
            {
                var builder = _notificationBuilders.FirstOrDefault(b => b.Context.Equals(context, StringComparison.OrdinalIgnoreCase));
                if (builder != null)
                {
                    return await Task.FromResult(builder);
                }

                var fallback = _notificationBuilders.FirstOrDefault(b => b.Context.Equals("SystemNotification", StringComparison.OrdinalIgnoreCase));
                return await Task.FromResult(fallback);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving notification builder for context: {Context}", context);
                var fallback = _notificationBuilders.FirstOrDefault(b => b.Context.Equals("SystemNotification", StringComparison.OrdinalIgnoreCase));
                return await Task.FromResult(fallback);
            }
        }

        private async Task<bool> SendSingleNotification(TikTokNotificationDto notification)
        {
            try
            {
                _logger.LogDebug("Processing notification: UserId={UserId}, ObjectId={ObjectId}",
                    notification.UserId, notification.ObjectId);

                if (string.IsNullOrEmpty(notification.UserId) || !Guid.TryParse(notification.UserId, out var userId))
                {
                    _logger.LogError("Invalid UserId format: {UserId}", notification.UserId);
                    return false;
                }

                Guid objectId;
                if (string.IsNullOrEmpty(notification.ObjectId))
                {
                    objectId = Guid.NewGuid();
                }
                else if (Guid.TryParse(notification.ObjectId, out objectId))
                {
                    _logger.LogDebug("Using ObjectId as Guid: {ObjectId}", objectId);
                }
                else
                {
                    objectId = Guid.NewGuid();
                    _logger.LogDebug("ObjectId is not Guid format (likely campaignId): {ObjectId}, generated new Guid: {NewGuid}",
                        notification.ObjectId, objectId);
                }

                // Build compact payload; flatten metadata keys to top-level and keep Extra for legacy custom payload
                var payload = new Dictionary<string, object?>
                {
                    ["AdAccountId"] = notification.AdAccountId,
                    ["BcId"] = notification.BcId,
                    ["UserType"] = notification.UserType.ToString(),
                    ["Extra"] = string.IsNullOrWhiteSpace(notification.Payload) ? null : notification.Payload
                };

                // Flatten metadata into top-level payload (do not overwrite existing base keys)
                if (notification.Metadata != null)
                {
                    foreach (var kv in notification.Metadata)
                    {
                        if (!payload.ContainsKey(kv.Key))
                        {
                            payload[kv.Key] = kv.Value;
                        }
                    }
                }

                var request = new Module.Notifications.Requests.SendNotificationReq
                {
                    UserId = userId,
                    Notifcation = new Module.Notifications.Requests.NotificationRequest
                    {
                        Title = notification.Title,
                        Content = notification.Content
                    },
                    Context = notification.Context ?? "SystemNotification",
                    ObjectId = objectId,
                    PhoneNumber = notification.PhoneNumber,
                    IdempotentKey = Guid.NewGuid().ToString(),
                    Payload = System.Text.Json.JsonSerializer.Serialize(payload)
                };

                var response = await _notificationService.SendNotification(request);

                _logger.LogInformation("Notification sent successfully via module. UserId: {UserId}, Response: {Response}",
                    notification.UserId, response);
                // Direct realtime push via SignalR bridge (summary delta)
                try
                {
                    await _realtimeBroadcaster.BroadcastUserSummaryAsync(userId, new NotificationSummaryUpdatedDto
                    {
                        UnreadDelta = 1,
                        Context = request.Context,
                        ObjectId = request.ObjectId.ToString()
                    });
                    _logger.LogDebug("Broadcasted NotificationSummaryUpdated (delta=1) for UserId={UserId}", notification.UserId);
                }
                catch (Exception pubEx)
                {
                    _logger.LogWarning(pubEx, "Failed to broadcast summary update for UserId={UserId}", notification.UserId);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to user: {UserId}", notification.UserId);
                return false;
            }
        }
    }
}


