using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TikTok.Application.Contracts.SystemNotification
{
    public interface ISystemNotificationRuleAppService
    {
        Task<SystemNotificationRuleListDto> GetListAsync(SystemNotificationRuleSearchDto input);
        Task<SystemNotificationRuleDto> GetAsync(Guid id);
        Task<SystemNotificationRuleDto> CreateAsync(CreateSystemNotificationRuleDto input);
        Task<SystemNotificationRuleDto> UpdateAsync(Guid id, UpdateSystemNotificationRuleDto input);
        Task DeleteAsync(Guid id);
        Task<SystemNotificationRuleDto> ToggleActiveAsync(Guid id);
        Task<SystemNotificationRuleStatsDto> GetStatisticsAsync();
        Task<SystemNotificationRuleValidationDto> ValidateRuleAsync(object input);
        Task TriggerRuleCheckAsync(Guid id);
        Task<List<FieldMetadata>> GetFieldsForEntityTypeAsync(string entityType);
    }
}
