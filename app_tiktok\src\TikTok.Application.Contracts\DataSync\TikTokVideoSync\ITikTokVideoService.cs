using System.Collections.Generic;
using System.Threading.Tasks;

namespace TikTok.Application.Contracts.DataSync.TikTokVideoSync
{
    /// <summary>
    /// Interface cho service lấy thông tin video TikTok sử dụng HttpClient để gọi TikTok share endpoint
    /// </summary>
    public interface ITikTokVideoService
    {

        /// <summary>
        /// Lấy thông tin nhiều video TikTok từ danh sách video ID (tối đa 500 bản ghi)
        /// </summary>
        /// <param name="videoIds">Danh sách ID của video TikTok (tối đa 500)</param>
        /// <returns>Danh sách thông tin video TikTok</returns>
        Task<GetVideoInfoListResponseDto> GetVideoInfoListAsync(IList<string> videoIds);
    }
}
