@page
@using TikTok.Localization
@using TikTok.Web.Pages.BusinessApplications
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model IndexModel
@inject IStringLocalizer<TikTokResource> L
@{
    ViewData["Title"] = L["Menu:BusinessApplications"];
}
@section styles {
    <abp-style src="/Pages/BusinessApplications/styles.css" />
}
@section scripts {
    <script>
        // Set redirect URI vào window object để JavaScript có thể sử dụng
        window.tikTokRedirectUri = '@Model.RedirectUri';
    </script>
    <abp-script src="/Pages/BusinessApplications/Index.js" />
}

<abp-card>
    <abp-card-header>
        <abp-row>
            <abp-column size-md="_6" class="mb-2 mb-md-0">
                <abp-card-title>@L["Menu:BusinessApplications"]</abp-card-title>
            </abp-column>
            <abp-column size-md="_6" class="text-md-end">
                <abp-button id="NewBusinessApplicationButton" text="@L["NewBusinessApplication"].Value" icon="plus"
                    button-type="Primary" />
            </abp-column>
        </abp-row>
        <!-- Search and Filter Row -->
        <abp-row class="mt-3">
            <abp-column size-md="_12">
                <div class="row align-items-end">
                    <div class="col-lg-6 col-md-12 mb-2 mb-lg-0">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="BusinessApplicationsTable_filter" 
                                   placeholder="@L["SearchPlaceholder"]" />
                            <button class="btn btn-outline-secondary clear-search-btn" type="button" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="input-group">
                            <select id="StatusFilter" class="form-select">
                                <option value="">@L["AllStatuses"]</option>
                                <option value="0">@L["BusinessApplication:Status:DEFAULT"]</option>
                                <option value="1">@L["BusinessApplication:Status:PENDING"]</option>
                                <option value="2">@L["BusinessApplication:Status:PROCESSING"]</option>
                                <option value="3">@L["BusinessApplication:Status:PROCESSED"]</option>
                                <option value="4">@L["BusinessApplication:Status:ERROR"]</option>
                            </select>
                            <button class="btn btn-outline-secondary clear-status-btn" type="button" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <div class="table-responsive">
            <abp-table striped-rows="true" id="BusinessApplicationsTable">
                <thead>
                    <tr>
                        <th>@L["BusinessApplication:ApplicationId"]</th>
                        <th>@L["BcId"]</th>
                        <th>@L["BcName"]</th>
                        <th>@L["BusinessApplication:Comment"]</th>
                        <th>@L["BusinessApplication:IsActive"]</th>
                        <th>@L["Status"]</th>
                        <th>@L["BusinessApplication:AccessTokenCreatedAt"]</th>
                        <th>@L["Authorize"]</th>
                        <th>@L["BetaAuthorization"]</th>
                        <th>@L["Actions"]</th>
                    </tr>
                </thead>
            </abp-table>
        </div>
    </abp-card-body>
</abp-card>

<!-- Modal Cài đặt Đồng bộ lùi -->
<div class="modal fade" id="SyncBackModal" tabindex="-1" aria-labelledby="SyncBackModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="SyncBackModalLabel">@L["SyncBackSettings"]</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="SyncBackForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="SyncBackStartDate" class="form-label">@L["StartDate"]</label>
                            <input type="date" class="form-control" id="SyncBackStartDate" name="SyncBackStartDate">
                        </div>
                        <div class="col-md-6">
                            <label for="SyncBackEndDate" class="form-label">@L["EndDate"]</label>
                            <input type="date" class="form-control" id="SyncBackEndDate" name="SyncBackEndDate">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">@L["CommandTypes"]</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="1"
                                            id="cmdSyncBusinessCenter" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncBusinessCenter">
                                            @L["SyncBusinessCenter"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="2"
                                            id="cmdSyncTransaction" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncTransaction">
                                            @L["SyncTransaction"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="3" id="cmdSyncBalance"
                                            name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncBalance">
                                            @L["SyncBalance"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="4" id="cmdSyncAsset"
                                            name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncAsset">
                                            @L["SyncAsset"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="5" id="cmdSyncCampaign"
                                            name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncCampaign">
                                            @L["SyncCampaign"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="6"
                                            id="cmdSyncReportIntegratedAdAccount" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncReportIntegratedAdAccount">
                                            @L["SyncReportIntegratedAdAccount"]
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="7"
                                            id="cmdSyncReportIntegratedCampaign" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncReportIntegratedCampaign">
                                            @L["SyncReportIntegratedCampaign"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="8"
                                            id="cmdSyncReportIntegratedAdGroup" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncReportIntegratedAdGroup">
                                            @L["SyncReportIntegratedAdGroup"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="9"
                                            id="cmdSyncLatestBalance" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncLatestBalance">
                                            @L["SyncLatestBalance"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="10"
                                            id="cmdSyncDetailedAdAccount" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncDetailedAdAccount">
                                            @L["SyncDetailedAdAccount"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="11" id="cmdSyncGmvMax"
                                            name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncGmvMax">
                                            @L["SyncGmvMax"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="12"
                                            id="cmdSyncGmvMaxIdentities" name="CommandTypes">
                                        <label class="form-check-label" for="cmdSyncGmvMaxIdentities">
                                            @L["SyncGmvMaxIdentities"]
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["Cancel"]</button>
                <button type="button" class="btn btn-primary" id="StartSyncBackButton">@L["Start"]</button>
            </div>
        </div>
    </div>
</div>