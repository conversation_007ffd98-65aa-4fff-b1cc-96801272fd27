using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu TikTok Shop Products
    /// </summary>
    public interface ITikTokShopProductsSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Business Center ID
        /// Lấy tất cả advertiserId và storeId từ bảng RawGmvMaxIdentitiesEntity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsAsync(string bcId);

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Store ID (được giữ lại cho tương thích)
        /// </summary>
        /// <param name="storeId">ID của Store</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="unauthorizedAdvertiserIds">Danh sách advertiser IDs không có quyền (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsByIdentityAsync(string storeId, string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null);

        /// <summary>
        /// Đồng bộ TikTok Shop Products cho nhiều Store của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="storeIds">Danh sách Store IDs (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TikTokShopProductsSyncResult> SyncManyTikTokShopProductsAsync(string advertiserId, string bcId, List<string>? storeIds = null);

        /// <summary>
        /// Đồng bộ tất cả TikTok Shop Products cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TikTokShopProductsSyncResult> SyncAllTikTokShopProductsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ TikTok Shop Products
    /// </summary>
    public class TikTokShopProductsSyncResult : SyncResultBase
    {
        /// <summary>
        /// Tổng số bản ghi đã xử lý
        /// </summary>
        public override int TotalSynced => NewRecords + UpdatedRecords + ErrorRecords;

        /// <summary>
        /// Thời gian bắt đầu đồng bộ
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc đồng bộ
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Thời gian thực hiện (tính bằng giây)
        /// </summary>
        public double DurationSeconds => (EndTime - StartTime).TotalSeconds;

        /// <summary>
        /// Số Business Center đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Advertiser đã đồng bộ
        /// </summary>
        public int AdvertiserCount { get; set; }

        /// <summary>
        /// Số Store đã đồng bộ
        /// </summary>
        public int StoreCount { get; set; }

        /// <summary>
        /// Số sản phẩm có sẵn (status = AVAILABLE)
        /// </summary>
        public int AvailableProductsCount { get; set; }

        /// <summary>
        /// Số sản phẩm không có sẵn
        /// </summary>
        public int UnavailableProductsCount { get; set; }

        /// <summary>
        /// Số sản phẩm đang chạy custom shop ads
        /// </summary>
        public int RunningCustomShopAdsCount { get; set; }

        /// <summary>
        /// Số sản phẩm có GMV Max Ads status = OCCUPIED
        /// </summary>
        public int GmvMaxOccupiedCount { get; set; }

        /// <summary>
        /// Số sản phẩm có GMV Max Ads status = AVAILABLE
        /// </summary>
        public int GmvMaxAvailableCount { get; set; }

        /// <summary>
        /// Danh sách các trạng thái sản phẩm và số lượng
        /// </summary>
        public Dictionary<string, int> ProductStatusCount { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Danh sách các GMV Max Ads status và số lượng
        /// </summary>
        public Dictionary<string, int> GmvMaxAdsStatusCount { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Danh sách các danh mục sản phẩm và số lượng
        /// </summary>
        public Dictionary<string, int> CategoryCount { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Danh sách các đơn vị tiền tệ và số lượng
        /// </summary>
        public Dictionary<string, int> CurrencyCount { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Danh sách advertiser IDs không có quyền truy cập
        /// </summary>
        public List<string> UnauthorizedAdvertiserIds { get; set; } = new List<string>();

        /// <summary>
        /// Tổng số trang đã xử lý
        /// </summary>
        public int TotalPagesProcessed { get; set; }

        /// <summary>
        /// Tổng số sản phẩm từ API
        /// </summary>
        public int TotalProductsFromApi { get; set; }
    }
}
