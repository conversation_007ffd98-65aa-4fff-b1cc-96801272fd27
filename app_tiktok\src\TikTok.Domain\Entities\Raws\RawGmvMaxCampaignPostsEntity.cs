using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho các bài post trong GMV Max Campaign từ API.
    /// Ch<PERSON>a thông tin chi tiết về identity, item, video và metadata của các bài post.
    /// </summary>
    public class RawGmvMaxCampaignPostsEntity : AuditedEntity<Guid>
    {
        public RawGmvMaxCampaignPostsEntity()
        {
            
        }

        public RawGmvMaxCampaignPostsEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// ID Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của Campaign
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// ID của item/post
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemId { get; set; }

        /// <summary>
        /// Danh sách SPU IDs (JSON array dạng string)
        /// </summary>
        [StringLength(1000)]
        public string? SpuIdList { get; set; }

        /// <summary>
        /// Nội dung text của post
        /// </summary>
        [StringLength(2000)]
        public string? Text { get; set; }

        // Identity Info Fields
        /// <summary>
        /// Tên hiển thị của identity
        /// </summary>
        [StringLength(200)]
        public string? IdentityDisplayName { get; set; }

        /// <summary>
        /// ID của identity
        /// </summary>
        [StringLength(100)]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Loại identity (AUTH_CODE, BC_AUTH_TT, etc.)
        /// </summary>
        [StringLength(50)]
        public string? IdentityType { get; set; }

        /// <summary>
        /// URL hình ảnh profile của identity
        /// </summary>
        [StringLength(500)]
        public string? IdentityProfileImage { get; set; }

        /// <summary>
        /// ID Business Center được ủy quyền cho identity (có thể null)
        /// </summary>
        [StringLength(100)]
        public string? IdentityAuthorizedBcId { get; set; }

        // Video Info Fields
        /// <summary>
        /// Bit rate của video
        /// </summary>
        public long? VideoBitRate { get; set; }

        /// <summary>
        /// Độ phân giải video (1080p, 540p, etc.)
        /// </summary>
        [StringLength(20)]
        public string? VideoDefinition { get; set; }

        /// <summary>
        /// Thời lượng video (giây)
        /// </summary>
        public double? VideoDuration { get; set; }

        /// <summary>
        /// Định dạng video (mp4, etc.)
        /// </summary>
        [StringLength(20)]
        public string? VideoFormat { get; set; }

        /// <summary>
        /// Frames per second của video
        /// </summary>
        public int? VideoFps { get; set; }

        /// <summary>
        /// Chiều cao video (pixels)
        /// </summary>
        public int? VideoHeight { get; set; }

        /// <summary>
        /// Chiều rộng video (pixels)
        /// </summary>
        public int? VideoWidth { get; set; }

        /// <summary>
        /// URL preview của video
        /// </summary>
        public string? VideoPreviewUrl { get; set; }

        /// <summary>
        /// Signature của video
        /// </summary>
        [StringLength(200)]
        public string? VideoSignature { get; set; }

        /// <summary>
        /// Kích thước file video (bytes)
        /// </summary>
        public long? VideoSize { get; set; }

        /// <summary>
        /// URL cover/thumbnail của video
        /// </summary>
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// ID của video
        /// </summary>
        [StringLength(100)]
        public string? VideoId { get; set; }

        /// <summary>
        /// Thời gian đồng bộ cuối cùng
        /// </summary>
        public DateTime? SyncedAt { get; set; }

        /// <summary>
        /// Kiểm tra xem entity hiện tại có khác với dữ liệu mới không
        /// </summary>
        /// <param name="newData">Dữ liệu mới để so sánh</param>
        /// <returns>True nếu có thay đổi</returns>
        public bool HasChanged(RawGmvMaxCampaignPostsEntity newData)
        {
            return SpuIdList != newData.SpuIdList ||
                   Text != newData.Text ||
                   IdentityDisplayName != newData.IdentityDisplayName ||
                   IdentityId != newData.IdentityId ||
                   IdentityType != newData.IdentityType ||
                   IdentityProfileImage != newData.IdentityProfileImage ||
                   IdentityAuthorizedBcId != newData.IdentityAuthorizedBcId ||
                   VideoBitRate != newData.VideoBitRate ||
                   VideoDefinition != newData.VideoDefinition ||
                   VideoDuration != newData.VideoDuration ||
                   VideoFormat != newData.VideoFormat ||
                   VideoFps != newData.VideoFps ||
                   VideoHeight != newData.VideoHeight ||
                   VideoWidth != newData.VideoWidth ||
                   VideoPreviewUrl != newData.VideoPreviewUrl ||
                   VideoSignature != newData.VideoSignature ||
                   VideoSize != newData.VideoSize ||
                   VideoCoverUrl != newData.VideoCoverUrl ||
                   VideoId != newData.VideoId;
        }

        /// <summary>
        /// Cập nhật entity hiện tại với dữ liệu mới
        /// </summary>
        /// <param name="newData">Dữ liệu mới</param>
        public void UpdateFromNewData(RawGmvMaxCampaignPostsEntity newData)
        {
            SpuIdList = newData.SpuIdList;
            Text = newData.Text;
            IdentityDisplayName = newData.IdentityDisplayName;
            IdentityId = newData.IdentityId;
            IdentityType = newData.IdentityType;
            IdentityProfileImage = newData.IdentityProfileImage;
            IdentityAuthorizedBcId = newData.IdentityAuthorizedBcId;
            VideoBitRate = newData.VideoBitRate;
            VideoDefinition = newData.VideoDefinition;
            VideoDuration = newData.VideoDuration;
            VideoFormat = newData.VideoFormat;
            VideoFps = newData.VideoFps;
            VideoHeight = newData.VideoHeight;
            VideoWidth = newData.VideoWidth;
            VideoPreviewUrl = newData.VideoPreviewUrl;
            VideoSignature = newData.VideoSignature;
            VideoSize = newData.VideoSize;
            VideoCoverUrl = newData.VideoCoverUrl;
            VideoId = newData.VideoId;
        }
    }
}
