using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Net.Http;
using TikTok.Application.Contracts.DataSync.TikTokVideoSync;
using TikTok.Entities.Dim;
using TikTokBusinessApi.Constants;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace TikTok.DataSync.TikTokVideoSync
{
    /// <summary>
    /// Service để lấy thông tin video TikTok sử dụng HttpClient để gọi TikTok share endpoint
    /// </summary>
    public class TikTokVideoService : ITikTokVideoService, ITransientDependency
    {
        private readonly ILogger<TikTokVideoService> _logger;
        private readonly IRepository<DimTTAccountEntity, Guid> _dimTTAccountRepository;
        private readonly IHttpClientFactory _httpClientFactory;

        public TikTokVideoService(
            ILogger<TikTokVideoService> logger, 
            IRepository<DimTTAccountEntity, Guid> dimTTAccountRepository,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _dimTTAccountRepository = dimTTAccountRepository;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Lấy thông tin video TikTok từ video ID
        /// </summary>
        /// <param name="videoId">ID của video TikTok</param>
        /// <returns>Thông tin video TikTok</returns>
        public async Task<TikTokVideoInfoDto> GetVideoInfoAsync(string videoId,HttpClient httpClient)
        {
            _logger.LogDebug("Bắt đầu lấy thông tin video TikTok từ ID: {VideoId}", videoId);

            var result = new TikTokVideoInfoDto
            {
                VideoId = videoId,
                SyncTime = DateTime.UtcNow
            };

            try
            {
                if (string.IsNullOrWhiteSpace(videoId))
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "Video ID không được để trống";
                    _logger.LogWarning("Video ID không được để trống");
                    return result;
                }

                // Gọi TikTok share endpoint để lấy HTML của video
                var htmlContent = await GetVideoHtmlFromTikTokAsync(videoId,httpClient);

                if (string.IsNullOrWhiteSpace(htmlContent))
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "Không thể lấy HTML từ TikTok share endpoint";
                    _logger.LogWarning("Không thể lấy HTML từ TikTok share endpoint cho Video ID: {VideoId}", videoId);
                    return result;
                }

                // Trích xuất thông tin từ HTML
                var videoData = ExtractDataFromHtml(htmlContent);
                
                if (videoData != null)
                {
                    result.IsSuccess = true;
                    result.RawData = videoData;
                    
                    // Map dữ liệu từ VideoData sang TikTokVideoInfoDto
                    MapVideoDataToDto(result, videoData);
                    
                    // Add or update DimTTAccount
                    if (videoData.Author != null)
                    {
                        var dimTTAccountId = await AddOrUpdateDimTTAccountAsync(videoData.Author);
                        result.DimTTAccountId = dimTTAccountId;
                    }
                    
                    _logger.LogDebug("Lấy thông tin video TikTok thành công. Video ID: {VideoId}", videoId);
                }
                else
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "Không thể trích xuất thông tin từ HTML";
                    _logger.LogWarning("Không thể trích xuất thông tin từ HTML cho Video ID: {VideoId}", videoId);
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"Lỗi khi lấy thông tin video: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi lấy thông tin video TikTok từ ID: {VideoId}", videoId);
            }

            return result;
        }

        /// <summary>
        /// Lấy thông tin nhiều video TikTok từ danh sách video ID
        /// </summary>
        /// <param name="videoIds">Danh sách ID của video TikTok</param>
        /// <returns>Danh sách thông tin video TikTok</returns>
        public async Task<GetVideoInfoListResponseDto> GetVideoInfoListAsync(IList<string> videoIds)
        {
            _logger.LogDebug("Bắt đầu lấy thông tin cho {Count} video TikTok", videoIds?.Count ?? 0);

            var response = new GetVideoInfoListResponseDto
            {
                Results = new List<VideoInfoResponseDto>()
            };

            if (videoIds == null || !videoIds.Any())
            {
                _logger.LogWarning("Danh sách video ID không được để trống");
                return response;
            }

            response.TotalProcessed = videoIds.Count;
            var httpClient = new HttpClient(new HttpClientHandler
            {
                AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
            });
            foreach (var videoId in videoIds)
            {
                try
                {
                    _logger.LogDebug("Đang xử lý video ID: {VideoId}", videoId);
                    
                    var videoInfo = await GetVideoInfoAsync(videoId, httpClient);
                    var simpleVideoInfo = MapToSimpleResponse(videoInfo);
                    response.Results.Add(simpleVideoInfo);
                    
                    if (videoInfo.IsSuccess)
                    {
                        response.SuccessCount++;
                        _logger.LogDebug("Thành công xử lý video ID: {VideoId}", videoId);
                    }
                    else
                    {
                        response.FailureCount++;
                        _logger.LogWarning("Thất bại xử lý video ID: {VideoId}, Lỗi: {Error}", 
                            videoId, videoInfo.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    response.FailureCount++;
                    _logger.LogError(ex, "Lỗi khi xử lý video ID: {VideoId}", videoId);
                    
                    // Thêm kết quả lỗi vào danh sách
                    response.Results.Add(new VideoInfoResponseDto
                    {
                        VideoId = videoId,
                        IsSuccess = false,
                        ErrorMessage = $"Lỗi khi xử lý video: {ex.Message}"
                    });
                }
            }
            httpClient.Dispose();
            _logger.LogDebug("Hoàn thành xử lý {Total} video, thành công: {Success}, thất bại: {Failure}", 
                response.TotalProcessed, response.SuccessCount, response.FailureCount);

            return response;
        }

        /// <summary>
        /// Map từ TikTokVideoInfoDto sang VideoInfoResponseDto (đơn giản hóa)
        /// </summary>
        /// <param name="videoInfo">Thông tin video đầy đủ</param>
        /// <returns>Thông tin video đơn giản</returns>
        private VideoInfoResponseDto MapToSimpleResponse(TikTokVideoInfoDto videoInfo)
        {
            var response = new VideoInfoResponseDto
            {
                VideoId = videoInfo.VideoId,
                VideoUrl = videoInfo.VideoUrl,
                Description = videoInfo.Description,
                CreatedTime = videoInfo.CreatedTime,
                IsSuccess = videoInfo.IsSuccess,
                ErrorMessage = videoInfo.ErrorMessage
            };

            // Map author info nếu có raw data
            if (videoInfo.RawData is VideoData videoData && videoData.Author != null)
            {
                response.Author = new AuthorResponseDto
                {
                    Id = videoData.Author.Id,
                    Username = videoData.Author.UniqueId ?? videoData.Author.Nickname,
                    Nickname = videoData.Author.Nickname,
                    AvatarThumb = videoData.Author.AvatarThumb,
                    FollowerCount = videoData.Author.FollowerCount,
                    Verified = videoData.Author.Verified,
                    DimTTAccountId = videoInfo.DimTTAccountId
                };
            }

            // Map stats nếu có raw data
            if (videoInfo.RawData is VideoData videoDataWithStats && videoDataWithStats.VideoDetail?.Stats != null)
            {
                response.Stats = new VideoStatsResponseDto
                {
                    ViewCount = videoDataWithStats.VideoDetail.Stats.PlayCount,
                    LikeCount = videoDataWithStats.VideoDetail.Stats.DiggCount,
                    CommentCount = videoDataWithStats.VideoDetail.Stats.CommentCount,
                    ShareCount = videoDataWithStats.VideoDetail.Stats.ShareCount
                };
            }

            return response;
        }

        /// <summary>
        /// Lấy HTML content từ TikTok share endpoint
        /// </summary>
        /// <param name="videoId">Video ID</param>
        /// <returns>HTML content</returns>
        private async Task<string?> GetVideoHtmlFromTikTokAsync(string videoId,HttpClient httpClient)
        {
            try
            {
                // Tạo URL từ ShareEndpoints.GetVideoHtml và videoId
                var url = ShareEndpoints.GetVideoHtml + videoId;
                
                _logger.LogDebug("Calling TikTok share endpoint: {Url}", url);

                // Thêm User-Agent header để tránh bị block
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                
                // Gửi HTTP GET request
                var response = await httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var htmlContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("Successfully retrieved HTML content, length: {Length}", htmlContent?.Length ?? 0);
                    return htmlContent;
                }
                else
                {
                    _logger.LogWarning("HTTP request failed with status code: {StatusCode} for video ID: {VideoId}", 
                        response.StatusCode, videoId);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling TikTok share endpoint for video ID: {VideoId}", videoId);
                return null;
            }
        }

        /// <summary>
        /// Trích xuất dữ liệu từ HTML content (từ TikTokVideoSyncService cũ)
        /// </summary>
        /// <param name="htmlContent">HTML content</param>
        /// <returns>VideoData object</returns>
        private VideoData? ExtractDataFromHtml(string htmlContent)
        {
            try
            {
                // Parse HTML
                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                // Find script tag containing JSON
                var scriptNode = doc.DocumentNode.SelectSingleNode("//script[@id='__UNIVERSAL_DATA_FOR_REHYDRATION__']");

                if (scriptNode == null)
                {
                    return null;
                }

                // Parse JSON
                JObject data = JObject.Parse(scriptNode.InnerText);

                // Get webapp.video-detail
                var videoDetailData = data["__DEFAULT_SCOPE__"]?["webapp.video-detail"];

                if (videoDetailData == null)
                {
                    return null;
                }

                // Get itemStruct
                var itemStruct = videoDetailData["itemInfo"]?["itemStruct"];

                if (itemStruct == null)
                {
                    return null;
                }

                // Get author info
                var authorInfo = itemStruct["author"];

                if (authorInfo == null)
                {
                    return null;
                }

                // Create AuthorInfo object
                var avatarThumbUrl = authorInfo["avatarThumb"]?.ToString() ?? string.Empty;
                
                var author = new AuthorInfo
                {
                    Id = authorInfo["id"]?.ToString() ?? string.Empty,
                    UniqueId = authorInfo["uniqueId"]?.ToString() ?? string.Empty,
                    Nickname = authorInfo["nickname"]?.ToString() ?? string.Empty,
                    FollowerCount = authorInfo["followerCount"]?.ToObject<long>() ?? 0,
                    Verified = authorInfo["verified"]?.ToObject<bool>() ?? false,
                    AvatarThumb = avatarThumbUrl
                };

                _logger.LogDebug("Extracted author info: Id={Id}, UniqueId={UniqueId}, Nickname={Nickname}, AvatarThumb={AvatarThumb}", 
                    author.Id, author.UniqueId, author.Nickname, 
                    string.IsNullOrEmpty(avatarThumbUrl) ? "None" : "Available");

                // Create VideoStats object
                var stats = itemStruct["stats"];
                var videoStats = stats != null ? new VideoStats
                {
                    PlayCount = stats["playCount"]?.ToObject<long>() ?? 0,
                    ShareCount = stats["shareCount"]?.ToObject<long>() ?? 0,
                    CommentCount = stats["commentCount"]?.ToObject<long>() ?? 0,
                    DiggCount = stats["diggCount"]?.ToObject<long>() ?? 0
                } : new VideoStats();

                // Create VideoDetail object
                var videoDetail = new VideoDetail
                {
                    Id = itemStruct["id"]?.ToString() ?? string.Empty,
                    Desc = itemStruct["desc"]?.ToString() ?? string.Empty,
                    CreateTime = itemStruct["createTime"]?.ToObject<long>() ?? 0,
                    Cover = itemStruct["video"]?["cover"]?.ToString() ?? string.Empty,
                    Stats = videoStats
                };

                // Create VideoData object
                var result = new VideoData
                {
                    VideoDetail = videoDetail,
                    Author = author
                };

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi trích xuất dữ liệu từ HTML");
                return null;
            }
        }

        /// <summary>
        /// Map dữ liệu từ VideoData sang TikTokVideoInfoDto
        /// </summary>
        /// <param name="dto">DTO để update</param>
        /// <param name="videoData">VideoData từ HTML</param>
        private void MapVideoDataToDto(TikTokVideoInfoDto dto, VideoData videoData)
        {
            if (videoData?.VideoDetail != null)
            {
                dto.VideoId = videoData.VideoDetail.Id;
                dto.Description = videoData.VideoDetail.Desc;
                dto.VideoUrl = videoData.VideoDetail.Cover;
                if (videoData.VideoDetail.CreateTime > 0)
                {
                    dto.CreatedTime = DateTimeOffset.FromUnixTimeSeconds(videoData.VideoDetail.CreateTime).DateTime;
                }

                if (videoData.VideoDetail.Stats != null)
                {
                    dto.ViewCount = videoData.VideoDetail.Stats.PlayCount;
                    dto.LikeCount = videoData.VideoDetail.Stats.DiggCount;
                    dto.CommentCount = videoData.VideoDetail.Stats.CommentCount;
                    dto.ShareCount = videoData.VideoDetail.Stats.ShareCount;
                }
            }

            if (videoData?.Author != null)
            {
                dto.Username = videoData.Author.UniqueId ?? videoData.Author.Nickname;
            }
        }

        /// <summary>
        /// Add or update DimTTAccount entity
        /// </summary>
        /// <param name="authorInfo">Author information from video</param>
        /// <returns>DimTTAccount ID</returns>
        private async Task<Guid> AddOrUpdateDimTTAccountAsync(AuthorInfo authorInfo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(authorInfo.Id))
                {
                    _logger.LogWarning("AuthorId is null or empty, cannot add/update DimTTAccount");
                    return Guid.Empty;
                }

                // Tìm bản ghi hiện tại theo TtAccountId
                var existingEntity = await _dimTTAccountRepository
                    .FirstOrDefaultAsync(x => x.TtAccountId == authorInfo.Id);

                if (existingEntity == null)
                {
                    // Thêm mới nếu chưa tồn tại
                    var newEntity = new DimTTAccountEntity(Guid.NewGuid())
                    {
                        TtAccountId = authorInfo.Id,
                        TtAccountName = authorInfo.UniqueId ?? authorInfo.Nickname ?? "Unknown",
                        TtAccountProfileImageUrl = string.IsNullOrEmpty(authorInfo.AvatarThumb) ? null : authorInfo.AvatarThumb,
                        TtAccountAuthorizationType = null, // Không có trong AuthorInfo
                        IsCurrent = true,
                        RowVersion = 1
                    };

                    await _dimTTAccountRepository.InsertAsync(newEntity);
                    _logger.LogDebug("Inserted new DimTTAccount for TtAccountId: {TtAccountId}", authorInfo.Id);
                    return newEntity.Id;
                }
                else
                {
                    // Update trực tiếp bản ghi hiện tại
                    var hasChanges = false;

                    // Kiểm tra và update TtAccountName
                    var newAccountName = authorInfo.UniqueId ?? authorInfo.Nickname ?? "Unknown";
                    if (existingEntity.TtAccountName != newAccountName)
                    {
                        existingEntity.TtAccountName = newAccountName;
                        hasChanges = true;
                    }

                    // Kiểm tra và update TtAccountProfileImageUrl
                    var newProfileImageUrl = string.IsNullOrEmpty(authorInfo.AvatarThumb) ? null : authorInfo.AvatarThumb;
                    if (existingEntity.TtAccountProfileImageUrl != newProfileImageUrl)
                    {
                        existingEntity.TtAccountProfileImageUrl = newProfileImageUrl;
                        hasChanges = true;
                    }

                    if (hasChanges)
                    {
                        // Tăng RowVersion khi có thay đổi
                        existingEntity.RowVersion += 1;
                        await _dimTTAccountRepository.UpdateAsync(existingEntity);
                        
                        _logger.LogDebug("Updated DimTTAccount for TtAccountId: {TtAccountId}, version: {Version}", 
                            authorInfo.Id, existingEntity.RowVersion);
                    }
                    else
                    {
                        _logger.LogDebug("No changes for DimTTAccount TtAccountId: {TtAccountId}", authorInfo.Id);
                    }

                    return existingEntity.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding/updating DimTTAccount for TtAccountId: {TtAccountId}", authorInfo.Id);
                return Guid.Empty;
            }
        }
    }
}
