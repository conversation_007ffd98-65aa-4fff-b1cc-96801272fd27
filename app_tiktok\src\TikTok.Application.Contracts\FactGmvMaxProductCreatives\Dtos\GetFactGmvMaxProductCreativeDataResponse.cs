using System;
using System.Collections.Generic;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using TikTok.DimDates;
using TikTok.DimProducts;
using TikTok.DimStores;
using TikTok.DimTTAccounts;

namespace TikTok.FactGmvMaxProductCreatives.Dtos
{
    /// <summary>
    /// Response DTO cho ProductCreative data - tương tự GetFactGmvMaxProductDataResponse
    /// </summary>
    public class GetFactGmvMaxProductCreativeDataResponse
    {
        /// <summary>
        /// Ngày bắt đầu của dữ liệu
        /// </summary>
        public DateTime From { get; set; }

        /// <summary>
        /// Ngày kết thúc của dữ liệu
        /// </summary>
        public DateTime To { get; set; }

        /// <summary>
        /// Tiền tệ sử dụng (USD/VND)
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Danh sách dữ liệu fact GMV Max ProductCreative
        /// </summary>
        public List<FactGmvMaxProductCreativeDto> FactGmvMaxProductCreatives { get; set; } = new();

        /// <summary>
        /// Dimension ngày tháng
        /// </summary>
        public List<DimDateDto> DimDates { get; set; } = new();

        /// <summary>
        /// Dimension tài khoản quảng cáo
        /// </summary>
        public List<DimAdAccountDto> DimAdAccounts { get; set; } = new();

        /// <summary>
        /// Dimension trung tâm kinh doanh
        /// </summary>
        public List<DimBusinessCenterDto> DimBusinessCenters { get; set; } = new();

        /// <summary>
        /// Dimension chiến dịch
        /// </summary>
        public List<DimCampaignDto> DimCampaigns { get; set; } = new();

        /// <summary>
        /// Dimension store
        /// </summary>
        public List<DimStoreDto> DimStores { get; set; } = new();

        /// <summary>
        /// Dimension sản phẩm
        /// </summary>
        public List<DimProductDto> DimProducts { get; set; } = new();

        /// <summary>
        /// ✅ NEW: Dimension TikTok Account
        /// </summary>
        public List<DimTTAccountDto> DimTTAccounts { get; set; } = new();
    }
}

