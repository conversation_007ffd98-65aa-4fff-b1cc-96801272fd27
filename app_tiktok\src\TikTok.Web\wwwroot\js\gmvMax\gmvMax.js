/**
 * GMV Max Analysis Main Controller
 * Manages the overall dashboard functionality with Campaign and Video tabs
 */

class GmvMaxDashboard {
    constructor() {
        this.config = null;
        this.permissions = null;
        this.campaignTab = null;
        this.videoTab = null;
        this.dataAggregator = null;
        this.autoRefreshInterval = null;

        this.init();
    }

    /**
     * Initialize the dashboard
     */
    async init() {
        try {
            // Load configuration from DOM
            this.loadConfiguration();

            // ✅ B1: Parse URL parameters for notification redirects (FIRST!)
            const urlParams = this.parseUrlParameters();

            // Initialize permissions
            this.initializePermissions();

            // Initialize data aggregator
            this.dataAggregator = new GmvMaxDataAggregator(this.config);

            // ✅ B2 & B3: Pre-process URL parameters and set filters BEFORE tab initialization
            const preProcessedFilters = this.preprocessUrlParameters(urlParams);

            // Initialize tab managers WITH pre-processed filters
            await this.initializeTabManagers(preProcessedFilters);

            // Setup event listeners
            this.setupEventListeners();

            // ✅ B4: Load initial data with filters already applied
            await this.loadInitialData();
        } catch (error) {
            console.error('❌ Error initializing GMV Max Analysis:', error);
            this.showErrorToast('Lỗi khởi tạo dashboard: ' + error.message);
        }
    }

    /**
     * Parse URL parameters for notification redirects and filters
     */
    parseUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            campaignId: urlParams.get('campaignId'),
            tab: urlParams.get('tab'),
            // Add more parameters as needed
        };
    }

    /**
     * ✅ B2 & B3: Pre-process URL parameters and prepare filters BEFORE tab initialization
     */
    preprocessUrlParameters(urlParams) {
        console.log('🔍 B1: Checking URL parameters:', urlParams);

        const filters = {};

        // ✅ B2: If campaignId exists, fill into filter, otherwise skip
        if (urlParams.campaignId) {
            console.log(
                '✅ B2: Found campaignId in URL, filling into filter:',
                urlParams.campaignId
            );
            filters.campaignIds = [urlParams.campaignId];
        } else {
            console.log('ℹ️ B2: No campaignId in URL, skipping filter');
        }

        // Store URL params for later use
        filters.urlParams = urlParams;

        console.log('✅ B3: Final filter values:', filters);
        return filters;
    }

    /**
     * Handle URL parameters (apply filters for notification redirects)
     * ✅ DEPRECATED: This method is now replaced by preprocessUrlParameters
     */
    async handleUrlParameters(urlParams) {
        try {
            // ✅ Tab switching is now handled by server-side rendering
            // No need to call switchToVideoTab() anymore

            let hasProcessedParameters = false;

            // Auto-apply campaign filter if campaignId is provided
            if (urlParams.campaignId) {
                // Apply to video tab if it's active or available
                if (this.videoTab) {
                    await this.applyNotificationCampaignFilter(
                        urlParams.campaignId
                    );
                    hasProcessedParameters = true;
                }
                // Apply to campaign tab if it's active or available
                else if (this.campaignTab) {
                    // Campaign tab can also handle campaignId filter if needed
                    console.log(
                        '✅ Campaign filter can be applied to campaign tab if needed:',
                        urlParams.campaignId
                    );
                    // TODO: Implement campaign tab filter if needed
                    hasProcessedParameters = true;
                }
            }

            // ✅ If no specific filters but tab parameter exists, load initial data for that tab
            if (!hasProcessedParameters && urlParams.tab) {
                const activeTab = document.querySelector(
                    '#gmvMaxTabs .nav-link.active'
                );
                if (activeTab) {
                    const activeTabId = activeTab.getAttribute('aria-controls');
                    await this.loadTabData(activeTabId);
                    hasProcessedParameters = true;
                }
            }

            // ✅ FIXED: Don't clear URL parameters immediately - let user keep them for refresh/sharing
            // Only clear them if there's an error or user manually navigates
            // if (urlParams.campaignId || urlParams.tab) {
            //     this.clearUrlParameters();
            // }

            return hasProcessedParameters;
        } catch (error) {
            console.error('❌ Error handling URL parameters:', error);
            return false;
        }
    }

    /**
     * Load configuration from DOM data attributes
     */
    loadConfiguration() {
        const configElement = document.getElementById('gmv-max-config');
        if (!configElement) {
            throw new Error('Configuration element not found');
        }

        this.config = {
            apiEndpoints: {
                campaign: configElement.dataset.apiCampaignEndpoint,
                product: configElement.dataset.apiProductEndpoint,
                productCreative:
                    configElement.dataset.apiProductCreativeEndpoint,
                creative: configElement.dataset.apiCreativeEndpoint,
            },
            refreshInterval:
                parseInt(configElement.dataset.refreshInterval) || 300000,
            locale: configElement.dataset.locale || 'vi-VN',
            currency: configElement.dataset.currency || 'USD',
            dateRange: {
                from: configElement.dataset.fromDate,
                to: configElement.dataset.toDate,
            },
            autoRefresh: configElement.dataset.autoRefresh === 'true',
        };
    }

    /**
     * Initialize permissions from existing permission helper
     */
    initializePermissions() {
        // Use existing permissionHelper if available
        if (typeof window.permissionHelper !== 'undefined') {
            this.permissions = window.permissionHelper;
        } else {
            // Fallback permissions based on UI visibility
            this.permissions = {
                hasViewSpending:
                    document.getElementById('campaign-content') !== null,
                hasViewMetrics:
                    document.getElementById('campaign-content') !== null,
                hasViewAll:
                    document.getElementById('campaign-content') !== null,
                hasViewVideoTab:
                    document.getElementById('video-content') !== null,
            };
        }
    }

    /**
     * Initialize tab managers - only initialize active tab for better performance
     * ✅ Now accepts pre-processed filters to avoid double API calls
     */
    async initializeTabManagers(preProcessedFilters = {}) {
        // ✅ Check which tab is currently active to avoid unnecessary initialization
        const activeTab = document.querySelector(
            '#gmvMaxTabs .nav-link.active'
        );
        const activeTabId = activeTab?.getAttribute('aria-controls');

        console.log('🎯 Active tab detected:', activeTabId);

        // Initialize Campaign Tab if it exists and is active or no specific active tab
        const campaignContent = document.getElementById('campaign-content');
        if (
            campaignContent &&
            (activeTabId === 'campaign-content' || !activeTabId)
        ) {
            console.log(
                '✅ Initializing Campaign Tab Manager with pre-processed filters'
            );
            this.campaignTab = new CampaignTabManager(
                this.config,
                this.dataAggregator,
                preProcessedFilters // ✅ Pass filters to constructor
            );
            await this.campaignTab.init();

            // ✅ Make campaignTab globally accessible for ROI modal
            window.campaignTabManager = this.campaignTab;
        }

        // Initialize Video Tab if it exists and is active or no campaign tab available
        const videoContent = document.getElementById('video-content');
        if (
            videoContent &&
            (activeTabId === 'video-content' ||
                (!campaignContent && !activeTabId))
        ) {
            console.log(
                '✅ Initializing Video Tab Manager with pre-processed filters'
            );
            this.videoTab = new VideoTabManager(
                this.config,
                this.dataAggregator,
                preProcessedFilters // ✅ Pass filters to constructor
            );
            await this.videoTab.init();
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Tab switching events
        const tabButtons = document.querySelectorAll(
            '#gmvMaxTabs button[data-bs-toggle="tab"]'
        );
        tabButtons.forEach((button) => {
            button.addEventListener('shown.bs.tab', (event) => {
                this.handleTabSwitch(
                    event.target.getAttribute('aria-controls')
                );
            });
        });

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById(
            'auto-refresh-toggle'
        );
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('click', () => {
                this.toggleAutoRefresh();
            });
        }

        // Window resize handler for responsive pivot tables
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }

    /**
     * Load initial data for active tab
     */
    async loadInitialData() {
        try {
            // Get active tab
            const activeTab = document.querySelector(
                '#gmvMaxTabs .nav-link.active'
            );
            if (!activeTab) return;

            const activeTabId = activeTab.getAttribute('aria-controls');
            await this.loadTabData(activeTabId);
        } catch (error) {
            console.error('❌ Error loading initial data:', error);
            this.showErrorToast('Lỗi tải dữ liệu ban đầu: ' + error.message);
        }
    }

    /**
     * Handle tab switch - initialize tab manager if needed
     */
    async handleTabSwitch(tabId) {
        try {
            console.log('🔄 Switching to tab:', tabId);

            // ✅ Lazy initialize tab managers when user switches tabs
            if (tabId === 'campaign-content' && !this.campaignTab) {
                console.log('⚡ Lazy initializing Campaign Tab Manager');
                this.campaignTab = new CampaignTabManager(
                    this.config,
                    this.dataAggregator,
                    {} // Empty filters for lazy loading
                );
                await this.campaignTab.init();
                window.campaignTabManager = this.campaignTab;
            }

            if (tabId === 'video-content' && !this.videoTab) {
                console.log('⚡ Lazy initializing Video Tab Manager');
                // ✅ For lazy loading, use empty filters since URL was already processed
                this.videoTab = new VideoTabManager(
                    this.config,
                    this.dataAggregator,
                    {} // Empty filters for lazy loading
                );
                await this.videoTab.init();
            }

            await this.loadTabData(tabId);
            this.updateLastUpdatedTime();
        } catch (error) {
            console.error(`❌ Error switching to tab ${tabId}:`, error);
            this.showErrorToast('Lỗi chuyển tab: ' + error.message);
        }
    }

    /**
     * Load data for specific tab
     */
    async loadTabData(tabId) {
        switch (tabId) {
            case 'campaign-content':
                if (this.campaignTab) {
                    await this.campaignTab.loadData();
                }
                break;
            case 'video-content':
                if (this.videoTab) {
                    await this.videoTab.loadData();
                }
                break;
            default:
                console.warn(`Unknown tab ID: ${tabId}`);
        }
    }

    /**
     * Toggle auto-refresh functionality
     */
    toggleAutoRefresh() {
        const statusElement = document.getElementById('auto-refresh-status');

        if (this.autoRefreshInterval) {
            // Stop auto-refresh
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            statusElement.textContent = 'OFF';
            this.showInfoToast('Auto-refresh đã tắt');
        } else {
            // Start auto-refresh
            this.autoRefreshInterval = setInterval(() => {
                this.refreshCurrentTab();
            }, this.config.refreshInterval);
            statusElement.textContent = 'ON';
            this.showInfoToast(
                `Auto-refresh đã bật (${this.config.refreshInterval / 1000}s)`
            );
        }
    }

    /**
     * Refresh current active tab
     */
    async refreshCurrentTab() {
        try {
            const activeTab = document.querySelector(
                '#gmvMaxTabs .nav-link.active'
            );
            if (!activeTab) return;

            const activeTabId = activeTab.getAttribute('aria-controls');

            await this.loadTabData(activeTabId);
            this.updateLastUpdatedTime();
        } catch (error) {
            console.error('❌ Error during auto-refresh:', error);
        }
    }

    /**
     * Handle window resize
     */
    handleWindowResize() {
        // Notify tab managers about resize
        if (this.campaignTab && this.campaignTab.handleResize) {
            this.campaignTab.handleResize();
        }
        if (this.videoTab && this.videoTab.handleResize) {
            this.videoTab.handleResize();
        }
    }

    /**
     * Update last updated time
     */
    updateLastUpdatedTime() {
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            const now = new Date();
            lastUpdatedElement.textContent = now.toLocaleString('vi-VN');
        }
    }

    /**
     * Switch to video tab programmatically
     * ✅ NOTE: This method is now deprecated since tab switching is handled by server-side rendering
     * When URL contains tab=video, the server renders the page with video tab already active
     */
    async switchToVideoTab() {
        try {
            const videoTabButton = document.getElementById('video-tab');
            if (videoTabButton) {
                // Trigger Bootstrap tab switch
                const tabTrigger = new bootstrap.Tab(videoTabButton);
                tabTrigger.show();

                console.log('✅ Switched to Video tab programmatically');
            }
        } catch (error) {
            console.error('❌ Error switching to video tab:', error);
        }
    }

    /**
     * Apply campaign filter from notification
     */
    async applyNotificationCampaignFilter(campaignId) {
        try {
            if (!this.videoTab || !campaignId) return;

            // Use the new setCampaignFilter method
            await this.videoTab.setCampaignFilter(campaignId);

            console.log(
                `✅ Applied campaign filter: ${campaignId} from notification`
            );
        } catch (error) {
            console.error(
                '❌ Error applying notification campaign filter:',
                error
            );
        }
    }

    /**
     * Clear URL parameters after processing
     */
    clearUrlParameters() {
        try {
            const url = new URL(window.location);
            url.searchParams.delete('campaignId');
            url.searchParams.delete('tab');
            window.history.replaceState(
                {},
                document.title,
                url.pathname + url.search
            );
        } catch (error) {
            console.error('❌ Error clearing URL parameters:', error);
        }
    }

    /**
     * Show success notification using ABP
     */
    showSuccessToast(message) {
        if (typeof abp !== 'undefined' && abp.notify) {
            abp.notify.success(message);
        } else {
            console.log('✅ SUCCESS:', message);
        }
    }

    /**
     * Show error notification using ABP
     */
    showErrorToast(message) {
        if (typeof abp !== 'undefined' && abp.notify) {
            abp.notify.error(message);
        } else {
            console.error('❌ ERROR:', message);
        }
    }

    /**
     * Show info notification using ABP
     */
    showInfoToast(message) {
        if (typeof abp !== 'undefined' && abp.notify) {
            abp.notify.info(message);
        } else {
            console.info('ℹ️ INFO:', message);
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }

        if (this.campaignTab && this.campaignTab.destroy) {
            this.campaignTab.destroy();
        }

        if (this.videoTab && this.videoTab.destroy) {
            this.videoTab.destroy();
        }
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    // Check if we're on the GMV Max page
    if (document.getElementById('gmv-max-config')) {
        window.gmvMaxDashboard = new GmvMaxDashboard();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function () {
    if (window.gmvMaxDashboard) {
        window.gmvMaxDashboard.destroy();
    }
});
