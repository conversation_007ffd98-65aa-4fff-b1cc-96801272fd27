using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ Campaign
    /// </summary>
    public class SyncCampaignWorker : WorkerJobBase
    {
        private readonly IGmvMaxCampaignSyncService _campaignSyncService;

        public SyncCampaignWorker(
            ILogger<SyncCampaignWorker> logger,
            IServiceProvider serviceProvider,
            IGmvMaxCampaignSyncService campaignSyncService)
            : base(logger, serviceProvider)
        {
            _campaignSyncService = campaignSyncService;
        }

        public override CommandType CommandType => CommandType.SyncCampaign;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Campaign - sync all advertisers for the BC
            var responseSync = await _campaignSyncService.SyncManyGmvMaxCampaignsAsync(parameters.BcId);
            if (responseSync != null)
            {
                if (string.IsNullOrEmpty(responseSync.ErrorMessage))
                    return JobResult.Success(JsonConvert.SerializeObject(responseSync));
                else
                    return JobResult.Error(responseSync.ErrorMessage, JsonConvert.SerializeObject(responseSync));
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ Campaign");
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private SyncCampaignParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<SyncCampaignParameters>(parametersJson)
                    ?? new SyncCampaignParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new SyncCampaignParameters();
            }
        }

        /// <summary>
        /// Parameters cho SyncCampaign
        /// </summary>
        public class SyncCampaignParameters:DefaultParameters
        {
            //public string BcId { get; set; }
            //public DateTime SyncDate { get; set; }
        }
    }
}