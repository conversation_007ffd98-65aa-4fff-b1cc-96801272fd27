using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TikTok.Application.Contracts.SystemNotification
{
    /// <summary>
    /// DTO cho System Notification Rule
    /// </summary>
    public class SystemNotificationRuleDto
    {
        public Guid Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string RuleName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty;

        // QueryBuilder JSON conditions (single source of truth)
        [StringLength(4000)]
        public string? ConditionsJson { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime? LastTriggeredAt { get; set; }
        
        public long TriggerCount { get; set; } = 0;
        
        // Audit fields
        public DateTime CreationTime { get; set; }
        public Guid? CreatorId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public Guid? LastModifierId { get; set; }
        
        // Rule configuration fields
        public bool IsDefault { get; set; } = false;
        public bool IsPublic { get; set; } = false;

        // Helper properties for UI
        public string? CreatorName { get; set; }
        public string? LastModifierName { get; set; }
        public string? EntityTypeDisplayName { get; set; }
    }

    /// <summary>
    /// DTO cho creating System Notification Rule
    /// </summary>
    public class CreateSystemNotificationRuleDto
    {
        [Required]
        [StringLength(200)]
        public string RuleName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty;

        [StringLength(4000)]
        public string? ConditionsJson { get; set; }
        public bool IsActive { get; set; } = true;
        // Rule configuration fields
        public bool IsDefault { get; set; } = false;
        public bool IsPublic { get; set; } = false;
        
        // Helper properties for validation
        public List<string>? RecipientUserList { get; set; }
    }

    /// <summary>
    /// DTO cho updating System Notification Rule
    /// </summary>
    public class UpdateSystemNotificationRuleDto
    {
        [Required]
        [StringLength(200)]
        public string RuleName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty;

        [StringLength(4000)]
        public string? ConditionsJson { get; set; }
        
        public bool IsActive { get; set; } = true;

        // Rule configuration fields
        public bool IsDefault { get; set; } = false;
        public bool IsPublic { get; set; } = false;
        
        // Helper properties for validation
        public List<string>? RecipientUserList { get; set; }
    }

    /// <summary>
    /// DTO cho System Notification Rule statistics
    /// </summary>
    public class SystemNotificationRuleStatsDto
    {
        public int TotalRules { get; set; }
        public int ActiveRules { get; set; }
        public int InactiveRules { get; set; }
        public int StandardRules { get; set; }
        public long TotalTriggerCount { get; set; }
        public DateTime? LastTriggerTime { get; set; }
        public DateTime? OldestRuleCreated { get; set; }
        public DateTime? NewestRuleCreated { get; set; }
        
        public Dictionary<string, int> RulesByEntityTypeBreakdown { get; set; } = new();
        public Dictionary<string, int> RulesByRecipientTypeBreakdown { get; set; } = new();
        public Dictionary<string, int> RulesByOperatorBreakdown { get; set; } = new();
        public Dictionary<string, int> RulesByFieldBreakdown { get; set; } = new();
    }

    /// <summary>
    /// DTO cho System Notification Rule search criteria
    /// </summary>
    public class SystemNotificationRuleSearchDto
    {
        public string? EntityType { get; set; }
        public string? RecipientType { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public Guid? CreatorId { get; set; }
        public string? SearchText { get; set; }
        
        // New search criteria
        public bool? IsDefault { get; set; }
        public bool? IsPublic { get; set; }
        
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 100;
        public string? Sorting { get; set; } = "CreationTime";
        public bool Ascending { get; set; } = true;
    }

    /// <summary>
    /// DTO cho System Notification Rule list response
    /// </summary>
    public class SystemNotificationRuleListDto
    {
        public List<SystemNotificationRuleDto> Items { get; set; } = new();
        public long TotalCount { get; set; }
        public int SkipCount { get; set; }
        public int MaxResultCount { get; set; }
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// DTO cho System Notification Rule validation result
    /// </summary>
    public class SystemNotificationRuleValidationDto
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object>? FieldMetadata { get; set; }
        public List<string>? SuggestedValues { get; set; }
    }

}
