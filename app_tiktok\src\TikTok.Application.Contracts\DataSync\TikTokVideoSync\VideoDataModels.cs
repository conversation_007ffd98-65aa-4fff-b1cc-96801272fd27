namespace TikTok.Application.Contracts.DataSync.TikTokVideoSync
{
    /// <summary>
    /// Data models cho TikTok Video
    /// </summary>
    public class VideoData
    {
        public VideoDetail VideoDetail { get; set; }
        public AuthorInfo Author { get; set; }
    }

    public class VideoDetail
    {
        public string Id { get; set; }
        public string Desc { get; set; }
        public long CreateTime { get; set; }
        public string Cover { get; set; }
        public VideoStats Stats { get; set; }
    }

    public class VideoStats
    {
        public long PlayCount { get; set; }
        public long ShareCount { get; set; }
        public long CommentCount { get; set; }
        public long DiggCount { get; set; }
    }

    public class AuthorInfo
    {
        public string Id { get; set; }
        public string UniqueId { get; set; }
        public string Nickname { get; set; }
        public long FollowerCount { get; set; }
        public bool Verified { get; set; }
        public string AvatarThumb { get; set; }
    }
}
