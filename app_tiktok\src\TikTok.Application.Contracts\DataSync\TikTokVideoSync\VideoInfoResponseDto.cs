using System;

namespace TikTok.Application.Contracts.DataSync.TikTokVideoSync
{
    /// <summary>
    /// DTO cho response thông tin video TikTok (đơn giản)
    /// </summary>
    public class VideoInfoResponseDto
    {
        /// <summary>
        /// ID của video
        /// </summary>
        public string VideoId { get; set; }

        /// <summary>
        /// URL cover của video
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// Mô tả của video
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Thời gian tạo video
        /// </summary>
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// Thông tin author
        /// </summary>
        public AuthorResponseDto Author { get; set; }

        /// <summary>
        /// Thống kê video
        /// </summary>
        public VideoStatsResponseDto Stats { get; set; }

        /// <summary>
        /// Trạng thái thành công
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Thông báo lỗi
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// DTO cho thông tin author
    /// </summary>
    public class AuthorResponseDto
    {
        /// <summary>
        /// ID của author
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Username của author
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Tên hiển thị của author
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// URL ảnh đại diện
        /// </summary>
        public string AvatarThumb { get; set; }

        /// <summary>
        /// Số lượng follower
        /// </summary>
        public long FollowerCount { get; set; }

        /// <summary>
        /// Trạng thái verified
        /// </summary>
        public bool Verified { get; set; }
        /// <summary>
        /// Id của kênh trong bảng Dim
        /// </summary>
        public Guid? DimTTAccountId { get; set; }
    }

    /// <summary>
    /// DTO cho thống kê video
    /// </summary>
    public class VideoStatsResponseDto
    {
        /// <summary>
        /// Số lượt view
        /// </summary>
        public long ViewCount { get; set; }

        /// <summary>
        /// Số lượt like
        /// </summary>
        public long LikeCount { get; set; }

        /// <summary>
        /// Số lượt comment
        /// </summary>
        public long CommentCount { get; set; }

        /// <summary>
        /// Số lượt share
        /// </summary>
        public long ShareCount { get; set; }
    }
}
